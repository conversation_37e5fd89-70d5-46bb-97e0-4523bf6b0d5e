# 使用更小的基础镜像以减小体积
FROM python:3.13.3-slim-bullseye

LABEL maintainer="<EMAIL>" \
      provider="evo mind" \
      description="EVYD AI Service"

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PATH="/root/.local/bin:$PATH"

# 安装构建所需的系统依赖（curl, build 工具, git）
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    build-essential \
    git \
    vim \
 && rm -rf /var/lib/apt/lists/*

# 安装 uv
RUN curl -LsSf https://astral.sh/uv/install.sh | sh

# 切换工作目录
WORKDIR /evo-mind

# 复制依赖文件
COPY pyproject.toml uv.lock ./

# 同步依赖（使用锁文件，生产模式）
RUN #uv sync --index-url $UV_INDEX_URL
RUN uv sync

# 复制项目源码
COPY . .

# 添加执行权限
RUN chmod +x start.sh

# 暴露服务端口
EXPOSE 8080

# 用脚本启动
CMD ["./start.sh"]
