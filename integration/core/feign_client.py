# feign_client.py - 类Feign声明式客户端
#
# @author: shaohua.sun
# @date: 2025-01-27
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD

from typing import Dict, Any, Optional, Callable, Type, Union, get_type_hints, List
from functools import wraps
import inspect
from dataclasses import dataclass

from .http_client import HttpClient
from .base_client import ResponseContext
from ..config.integration_config import ServiceConfig, HttpConfig
from utils.biz_logger import get_logger

# 支持Pydantic模型
try:
    from pydantic import BaseModel, ValidationError

    PYDANTIC_AVAILABLE = True
except ImportError:
    BaseModel = None
    ValidationError = None
    PYDANTIC_AVAILABLE = False

logger = get_logger(__name__)


@dataclass
class RequestMapping:
    """请求映射配置"""
    method: str  # HTTP方法
    path: str  # 请求路径
    params: Optional[Dict[str, str]] = None  # 参数映射
    headers: Optional[Dict[str, str]] = None  # 请求头


class FeignMethod:
    """Feign方法包装器"""

    def __init__(self, mapping: RequestMapping, original_method: Callable):
        self.mapping = mapping
        self.original_method = original_method
        self.signature = inspect.signature(original_method)


def get_mapping(path: str, **kwargs) -> Callable:
    """GET请求映射装饰器"""

    def decorator(func: Callable) -> Callable:
        func._feign_mapping = RequestMapping(method="GET", path=path, **kwargs)
        return func

    return decorator


def post_mapping(path: str, **kwargs) -> Callable:
    """POST请求映射装饰器"""

    def decorator(func: Callable) -> Callable:
        func._feign_mapping = RequestMapping(method="POST", path=path, **kwargs)
        return func

    return decorator


def put_mapping(path: str, **kwargs) -> Callable:
    """PUT请求映射装饰器"""

    def decorator(func: Callable) -> Callable:
        func._feign_mapping = RequestMapping(method="PUT", path=path, **kwargs)
        return func

    return decorator


def delete_mapping(path: str, **kwargs) -> Callable:
    """DELETE请求映射装饰器"""

    def decorator(func: Callable) -> Callable:
        func._feign_mapping = RequestMapping(method="DELETE", path=path, **kwargs)
        return func

    return decorator


class FeignClientMeta(type):
    """Feign客户端元类"""

    def __new__(cls, name, bases, attrs):
        # 收集所有带有映射的方法
        feign_methods = {}

        for attr_name, attr_value in attrs.items():
            if hasattr(attr_value, '_feign_mapping'):
                feign_methods[attr_name] = FeignMethod(
                    attr_value._feign_mapping,
                    attr_value
                )

        # 将收集的方法存储到类属性中
        attrs['_feign_methods'] = feign_methods

        return super().__new__(cls, name, bases, attrs)


class FeignClient(metaclass=FeignClientMeta):
    """Feign客户端基类"""

    def __init__(
            self,
            service_name: str,
            service_config: ServiceConfig,
            http_config: Optional[HttpConfig] = None,
            request_interceptors: Optional[List] = None,
            response_interceptors: Optional[List] = None
    ):
        """
        初始化Feign客户端

        Args:
            service_name: 服务名称
            service_config: 服务配置
            http_config: HTTP配置
            request_interceptors: 初始请求拦截器列表
            response_interceptors: 初始响应拦截器列表
        """
        self.service_name = service_name
        self.service_config = service_config
        self.http_client = HttpClient(service_name, service_config, http_config)

        # 如果提供了初始拦截器，则添加到客户端
        if request_interceptors:
            for interceptor in request_interceptors:
                self.http_client.add_request_interceptor(interceptor)

        if response_interceptors:
            for interceptor in response_interceptors:
                self.http_client.add_response_interceptor(interceptor)

        self._setup_methods()

    def _setup_methods(self):
        """设置方法代理"""
        for method_name, feign_method in self._feign_methods.items():
            proxy_method = self._create_proxy_method(feign_method)
            setattr(self, method_name, proxy_method)

    def _create_proxy_method(self, feign_method: FeignMethod) -> Callable:
        """创建代理方法"""
        mapping = feign_method.mapping
        signature = feign_method.signature

        def proxy_method(*args, **kwargs) -> Any:
            # 绑定参数（排除self）
            bound_args = signature.bind(self, *args, **kwargs)
            bound_args.apply_defaults()

            # 移除self参数
            arguments = bound_args.arguments.copy()
            arguments.pop('self', None)

            # 构建请求参数
            request_kwargs = self._build_request_kwargs(mapping, arguments)

            # 执行HTTP请求
            if mapping.method.upper() == "GET":
                response = self.http_client.get(**request_kwargs)
            elif mapping.method.upper() == "POST":
                response = self.http_client.post(**request_kwargs)
            elif mapping.method.upper() == "PUT":
                response = self.http_client.put(**request_kwargs)
            elif mapping.method.upper() == "DELETE":
                response = self.http_client.delete(**request_kwargs)
            else:
                raise ValueError(f"Unsupported HTTP method: {mapping.method}")

            # 获取返回类型并进行反序列化
            return_type = signature.return_annotation
            if return_type and return_type != inspect.Signature.empty:
                return self._deserialize_response(response.data, return_type)

            # 返回响应数据
            return response.data

        return proxy_method

    def _serialize_argument(self, value: Any) -> Any:
        """序列化参数值，支持Pydantic模型"""
        if PYDANTIC_AVAILABLE and isinstance(value, BaseModel):
            # Pydantic模型转换为dict，使用alias
            return value.model_dump(by_alias=True)
        elif isinstance(value, list):
            # 列表中的每个元素都需要序列化
            return [self._serialize_argument(item) for item in value]
        elif isinstance(value, dict):
            # 字典中的每个值都需要序列化
            return {k: self._serialize_argument(v) for k, v in value.items()}
        else:
            # 基本类型直接返回
            return value

    def _deserialize_response(self, response_data: Dict[str, Any], return_type: Type) -> Any:
        """反序列化响应数据为指定类型，支持容错处理"""
        if not PYDANTIC_AVAILABLE:
            return response_data

        # 获取实际的模型类型
        actual_type = self._extract_actual_type(return_type)

        # 如果是Pydantic模型，则进行反序列化
        if (inspect.isclass(actual_type) and
                issubclass(actual_type, BaseModel)):
            try:
                # 尝试反序列化，启用容错模式
                return actual_type.model_validate(response_data, strict=False)
            except ValidationError as e:
                logger.warning(f"响应反序列化失败，返回原始数据: {e}")
                # 反序列化失败时返回原始数据，确保业务逻辑不中断
                return response_data

        return response_data

    def _extract_actual_type(self, return_type: Type) -> Type:
        """提取实际的类型，处理泛型嵌套"""
        if not hasattr(return_type, '__origin__'):
            return return_type

        # 处理泛型类型，如BaseResponse[T]
        if hasattr(return_type, '__args__') and return_type.__args__:
            # 对于BaseResponse[List[T]]这样的嵌套泛型，直接返回BaseResponse
            # 因为我们主要关心的是外层的BaseResponse结构
            return return_type

        return return_type

    def _build_request_kwargs(self, mapping: RequestMapping, bound_args: Dict[str, Any]) -> Dict[str, Any]:
        """构建请求参数"""
        kwargs = {
            'endpoint': mapping.path,
            'headers': mapping.headers or {}
        }

        # 处理路径参数
        path = mapping.path
        remaining_args = bound_args.copy()

        for param_name, param_value in bound_args.items():
            placeholder = f"{{{param_name}}}"
            if placeholder in path:
                path = path.replace(placeholder, str(param_value))
                # 移除已使用的参数
                remaining_args.pop(param_name, None)

        kwargs['endpoint'] = path

        # 处理剩余参数
        if mapping.method.upper() in ["GET", "DELETE"]:
            # GET/DELETE请求：展开所有参数到query string
            params = {}
            for key, value in remaining_args.items():
                serialized_value = self._serialize_argument(value)

                # 如果序列化后的值是字典（Pydantic模型），则展开字段
                if isinstance(serialized_value, dict):
                    params.update(serialized_value)
                else:
                    params[key] = serialized_value

            kwargs['params'] = params
        else:
            # POST/PUT请求：直接序列化参数作为JSON体
            if len(remaining_args) == 1:
                # 单个参数，直接使用其序列化结果
                single_value = list(remaining_args.values())[0]
                kwargs['json'] = self._serialize_argument(single_value)
            else:
                # 多个参数，构建对象
                json_data = {}
                for key, value in remaining_args.items():
                    json_data[key] = self._serialize_argument(value)
                kwargs['json'] = json_data

        return kwargs

        # ===== 拦截器管理接口 =====

    def add_request_interceptor(self, interceptor):
        """添加请求拦截器"""
        self.http_client.add_request_interceptor(interceptor)
        return self

    def add_response_interceptor(self, interceptor):
        """添加响应拦截器"""
        self.http_client.add_response_interceptor(interceptor)
        return self

    def remove_request_interceptor(self, interceptor_type: type):
        """移除指定类型的请求拦截器"""
        self.http_client.remove_request_interceptor(interceptor_type)
        return self

    def remove_response_interceptor(self, interceptor_type: type):
        """移除指定类型的响应拦截器"""
        self.http_client.remove_response_interceptor(interceptor_type)
        return self

    def set_interceptors(
            self,
            request_interceptors: Optional[List] = None,
            response_interceptors: Optional[List] = None,
            keep_logging: bool = True
    ):
        """
        批量设置拦截器

        Args:
            request_interceptors: 请求拦截器列表
            response_interceptors: 响应拦截器列表
            keep_logging: 是否保留日志拦截器

        Returns:
            self: 支持链式调用
        """
        self.http_client.set_interceptors(
            request_interceptors=request_interceptors,
            response_interceptors=response_interceptors,
            keep_logging=keep_logging
        )
        return self

    def get_interceptors(self):
        """获取当前所有拦截器"""
        return self.http_client.get_interceptors()

    def clear_interceptors(self, keep_logging: bool = True):
        """
        清除所有拦截器

        Args:
            keep_logging: 是否保留日志拦截器

        Returns:
            self: 支持链式调用
        """
        if keep_logging:
            self.http_client.clear_request_interceptors()
            self.http_client.clear_response_interceptors()
        else:
            self.http_client.set_interceptors([], [], keep_logging=False)
        return self

    # ===== 核心业务方法 ===== 