# integration.core - 核心组件模块
#
# @author: shaohua.sun
# @date: 2025-01-27
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD

"""
核心组件模块

提供HTTP客户端、Feign客户端等核心功能：
- BaseClient: 基础客户端抽象类
- HttpClient: 通用HTTP客户端
- FeignClient: 类Feign声明式客户端
- 异常处理和重试机制
- 请求/响应拦截器
"""

from .base_client import BaseClient
from .http_client import HttpClient
from .feign_client import FeignClient
from .exceptions import IntegrationError, ServiceUnavailableError, TimeoutError
from .interceptors import (
    RequestInterceptor, ResponseInterceptor, ConfigOverrideInterceptor,
    LoggingInterceptor, RetryInterceptor, TimeoutInterceptor, 
    AuthenticationInterceptor, RateLimitInterceptor, MetricsInterceptor,
    GlobalInterceptorManager
)

__all__ = [
    "BaseClient",
    "HttpClient",
    "FeignClient",
    "IntegrationError",
    "ServiceUnavailableError", 
    "TimeoutError",
    "RequestInterceptor",
    "ResponseInterceptor",
    "ConfigOverrideInterceptor",
    "LoggingInterceptor",
    "RetryInterceptor",
    "TimeoutInterceptor",
    "AuthenticationInterceptor",
    "RateLimitInterceptor",
    "MetricsInterceptor",
    "GlobalInterceptorManager"
] 