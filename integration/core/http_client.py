# http_client.py - HTTP客户端实现
#
# @author: shaohua.sun
# @date: 2025-01-27
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD

import json
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from typing import Dict, Any, Optional, List
import time

from .base_client import BaseClient, RequestContext, ResponseContext
from .exceptions import (
    IntegrationError, TimeoutError, NetworkError, 
    ServiceUnavailableError, AuthenticationError, RateLimitError
)
from .interceptors import RequestInterceptor, ResponseInterceptor, LoggingInterceptor
from ..config.integration_config import ServiceConfig, HttpConfig
from utils.biz_logger import get_logger

logger = get_logger(__name__)


class HttpClient(BaseClient):
    """HTTP客户端实现"""
    
    def __init__(
        self, 
        service_name: str, 
        service_config: ServiceConfig,
        http_config: Optional[HttpConfig] = None
    ):
        """
        初始化HTTP客户端
        
        Args:
            service_name: 服务名称
            service_config: 服务配置
            http_config: HTTP配置
        """
        super().__init__(service_name, service_config)
        self.http_config = http_config or HttpConfig()
        
        # 初始化拦截器
        self.request_interceptors: List[RequestInterceptor] = []
        self.response_interceptors: List[ResponseInterceptor] = []
        
        # 默认添加日志拦截器
        logging_interceptor = LoggingInterceptor()
        self.add_request_interceptor(logging_interceptor)
        self.add_response_interceptor(logging_interceptor)
        
        # 应用全局拦截器
        from .interceptors import GlobalInterceptorManager
        GlobalInterceptorManager.apply_to_client(self)
        
        # 创建session
        self._session = self._create_session()
    
    def _create_session(self) -> requests.Session:
        """创建requests session"""
        session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=self.http_config.max_retries,
            backoff_factor=self.http_config.retry_backoff_factor,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "PUT", "DELETE", "OPTIONS", "TRACE", "POST"]
        )
        
        # 配置适配器
        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=self.http_config.pool_connections,
            pool_maxsize=self.http_config.pool_maxsize
        )
        
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
    
    def add_request_interceptor(self, interceptor: RequestInterceptor):
        """添加请求拦截器"""
        self.request_interceptors.append(interceptor)
    
    def add_response_interceptor(self, interceptor: ResponseInterceptor):
        """添加响应拦截器"""
        self.response_interceptors.append(interceptor)
    
    def remove_request_interceptor(self, interceptor_type: type):
        """移除指定类型的请求拦截器"""
        self.request_interceptors = [
            i for i in self.request_interceptors 
            if not isinstance(i, interceptor_type)
        ]
    
    def remove_response_interceptor(self, interceptor_type: type):
        """移除指定类型的响应拦截器"""
        self.response_interceptors = [
            i for i in self.response_interceptors 
            if not isinstance(i, interceptor_type)
        ]
    
    def clear_request_interceptors(self):
        """清除所有请求拦截器（保留日志拦截器）"""
        logging_interceptors = [
            i for i in self.request_interceptors 
            if isinstance(i, LoggingInterceptor)
        ]
        self.request_interceptors = logging_interceptors
    
    def clear_response_interceptors(self):
        """清除所有响应拦截器（保留日志拦截器）"""
        logging_interceptors = [
            i for i in self.response_interceptors 
            if isinstance(i, LoggingInterceptor)
        ]
        self.response_interceptors = logging_interceptors
    
    def set_interceptors(
        self, 
        request_interceptors: List[RequestInterceptor] = None,
        response_interceptors: List[ResponseInterceptor] = None,
        keep_logging: bool = True
    ):
        """
        批量设置拦截器
        
        Args:
            request_interceptors: 请求拦截器列表
            response_interceptors: 响应拦截器列表 
            keep_logging: 是否保留日志拦截器
        """
        if request_interceptors is not None:
            if keep_logging:
                # 保留现有的日志拦截器
                existing_logging = [
                    i for i in self.request_interceptors 
                    if isinstance(i, LoggingInterceptor)
                ]
                self.request_interceptors = existing_logging + request_interceptors
            else:
                self.request_interceptors = request_interceptors[:]
        
        if response_interceptors is not None:
            if keep_logging:
                # 保留现有的日志拦截器
                existing_logging = [
                    i for i in self.response_interceptors 
                    if isinstance(i, LoggingInterceptor)
                ]
                self.response_interceptors = existing_logging + response_interceptors
            else:
                self.response_interceptors = response_interceptors[:]
    
    def get_interceptors(self) -> Dict[str, List]:
        """
        获取当前所有拦截器
        
        Returns:
            包含request和response拦截器列表的字典
        """
        return {
            'request': self.request_interceptors[:],
            'response': self.response_interceptors[:]
        }
    
    def _execute_request_interceptors(self, context: RequestContext) -> RequestContext:
        """执行请求拦截器"""
        for interceptor in self.request_interceptors:
            try:
                context = interceptor.before_request(context)
            except Exception as e:
                logger.error(f"Request interceptor error: {e}")
        return context
    
    def _execute_response_interceptors(self, context: ResponseContext) -> ResponseContext:
        """执行响应拦截器"""
        for interceptor in self.response_interceptors:
            try:
                context = interceptor.after_response(context)
            except Exception as e:
                logger.error(f"Response interceptor error: {e}")
        return context
    
    def _handle_request_error(self, e: Exception, context: RequestContext) -> ResponseContext:
        """处理请求异常"""
        error_message = str(e)
        status_code = 0
        
        if isinstance(e, requests.exceptions.Timeout):
            raise TimeoutError(
                service_name=self.service_name,
                timeout_duration=context.timeout,
                details={"url": context.url, "method": context.method}
            )
        elif isinstance(e, requests.exceptions.ConnectionError):
            raise NetworkError(
                service_name=self.service_name,
                message="Connection failed",
                details={"url": context.url, "method": context.method},
                cause=e
            )
        elif isinstance(e, requests.exceptions.RequestException):
            if hasattr(e, 'response') and e.response is not None:
                status_code = e.response.status_code
                if status_code == 401:
                    raise AuthenticationError(
                        service_name=self.service_name,
                        details={"url": context.url, "status_code": status_code}
                    )
                elif status_code == 429:
                    retry_after = e.response.headers.get('Retry-After')
                    raise RateLimitError(
                        service_name=self.service_name,
                        retry_after=int(retry_after) if retry_after else None,
                        details={"url": context.url}
                    )
                elif status_code >= 500:
                    raise ServiceUnavailableError(
                        service_name=self.service_name,
                        details={"url": context.url, "status_code": status_code}
                    )
        
        # 其他未知异常
        raise IntegrationError(
            message=f"Request failed: {error_message}",
            service_name=self.service_name,
            details={"url": context.url, "method": context.method}
        )
    
    def _make_request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Any] = None,
        json: Optional[Any] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: Optional[float] = None
    ) -> ResponseContext:
        """执行HTTP请求"""
        # 构建请求头
        request_headers = self._build_headers(headers)
        
        # 创建请求上下文
        context = self._create_request_context(
            method=method,
            endpoint=endpoint,
            headers=request_headers,
            params=params,
            data=data or json,
            timeout=timeout
        )
        
        # 执行请求拦截器
        context = self._execute_request_interceptors(context)
        
        try:
            # 执行HTTP请求
            response = self._session.request(
                method=context.method,
                url=context.url,
                params=context.params,
                data=data,
                json=json,
                headers=context.headers,
                timeout=context.timeout
            )
            
            # 解析响应数据
            response_data = None
            try:
                if response.content:
                    response_data = response.json()
            except Exception:
                # 如果不是JSON格式，返回文本内容
                response_data = response.text
            
            # 创建响应上下文
            response_context = self._create_response_context(
                request_context=context,
                status_code=response.status_code,
                response_headers=dict(response.headers),
                response_data=response_data,
                success=response.status_code < 400
            )
            
        except Exception as e:
            self._handle_request_error(e, context)
        
        # 执行响应拦截器
        response_context = self._execute_response_interceptors(response_context)
        
        return response_context
    
    def get(
        self, 
        endpoint: str, 
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: Optional[float] = None
    ) -> ResponseContext:
        """GET请求"""
        return self._make_request("GET", endpoint, params=params, headers=headers, timeout=timeout)
    
    def post(
        self, 
        endpoint: str, 
        data: Optional[Any] = None,
        json: Optional[Any] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: Optional[float] = None
    ) -> ResponseContext:
        """POST请求"""
        return self._make_request("POST", endpoint, data=data, json=json, headers=headers, timeout=timeout)
    
    def put(
        self, 
        endpoint: str, 
        data: Optional[Any] = None,
        json: Optional[Any] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: Optional[float] = None
    ) -> ResponseContext:
        """PUT请求"""
        return self._make_request("PUT", endpoint, data=data, json=json, headers=headers, timeout=timeout)
    
    def delete(
        self, 
        endpoint: str, 
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: Optional[float] = None
    ) -> ResponseContext:
        """DELETE请求"""
        return self._make_request("DELETE", endpoint, params=params, headers=headers, timeout=timeout) 