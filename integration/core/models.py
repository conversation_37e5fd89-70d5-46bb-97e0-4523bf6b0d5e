# models.py - 集成核心模型定义
#
# @author: shaohua.sun
# @date: 2025-06-23
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD

"""
集成核心模型定义
包含所有服务共用的基础模型
"""

from typing import Optional, Generic, TypeVar, Any, Dict
from pydantic import BaseModel, Field

T = TypeVar('T')


class BaseResponse(BaseModel, Generic[T]):
    """
    通用API响应模型
    
    所有服务的API响应都应该继承或使用此模型
    提供统一的响应结构：code、message、data
    """
    code: int = Field(..., description="响应码：0表示成功，其他值表示失败")
    message: Optional[str] = Field(None, description="响应消息，通常在失败时提供错误描述")
    data: Optional[T] = Field(None, description="响应数据，具体结构由泛型T定义")
    
    model_config = {"populate_by_name": True}
    
    @property
    def is_success(self) -> bool:
        """判断响应是否成功"""
        return self.code == 0
    
    @property
    def is_failure(self) -> bool:
        """判断响应是否失败"""
        return self.code != 0


class EmptyResponse(BaseResponse[None]):
    """
    空响应模型
    
    用于只返回状态码和消息，没有具体数据的API
    """
    data: None = Field(None, description="无数据返回")


class BooleanResponse(BaseResponse[bool]):
    """
    布尔响应模型
    
    用于返回布尔值的API，如操作成功/失败
    """
    data: Optional[bool] = Field(None, description="布尔值结果")


class DictResponse(BaseResponse[Dict[str, Any]]):
    """
    字典响应模型
    
    用于返回任意字典结构的API
    """
    data: Optional[Dict[str, Any]] = Field(None, description="字典数据")


# 类型别名，提高可读性
SuccessResponse = BaseResponse[bool]
StringResponse = BaseResponse[str]
IntResponse = BaseResponse[int] 