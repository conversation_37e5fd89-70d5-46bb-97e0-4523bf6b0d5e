# exceptions.py - 自定义异常类
#
# @author: shaohua.sun
# @date: 2025-01-27
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD

from typing import Optional, Dict, Any


class IntegrationError(Exception):
    """第三方集成基础异常类"""
    
    def __init__(
        self, 
        message: str, 
        service_name: Optional[str] = None,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None
    ):
        super().__init__(message)
        self.message = message
        self.service_name = service_name
        self.error_code = error_code
        self.details = details or {}
        self.cause = cause
    
    def __str__(self) -> str:
        parts = [self.message]
        if self.service_name:
            parts.append(f"Service: {self.service_name}")
        if self.error_code:
            parts.append(f"Code: {self.error_code}")
        return " | ".join(parts)


class ServiceUnavailableError(IntegrationError):
    """服务不可用异常"""
    
    def __init__(
        self, 
        service_name: str, 
        message: str = "Service is currently unavailable",
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            service_name=service_name,
            error_code="SERVICE_UNAVAILABLE",
            details=details
        )


class TimeoutError(IntegrationError):
    """请求超时异常"""
    
    def __init__(
        self, 
        service_name: str,
        timeout_duration: float,
        operation: str = "request",
        details: Optional[Dict[str, Any]] = None
    ):
        message = f"{operation.title()} timeout after {timeout_duration}s"
        super().__init__(
            message=message,
            service_name=service_name,
            error_code="TIMEOUT",
            details=details or {"timeout_duration": timeout_duration, "operation": operation}
        )


class AuthenticationError(IntegrationError):
    """认证失败异常"""
    
    def __init__(
        self, 
        service_name: str,
        message: str = "Authentication failed",
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            service_name=service_name,
            error_code="AUTHENTICATION_FAILED",
            details=details
        )


class RateLimitError(IntegrationError):
    """请求频率限制异常"""
    
    def __init__(
        self, 
        service_name: str,
        retry_after: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        message = "Rate limit exceeded"
        if retry_after:
            message += f", retry after {retry_after}s"
        
        super().__init__(
            message=message,
            service_name=service_name,
            error_code="RATE_LIMIT_EXCEEDED",
            details=details or {"retry_after": retry_after}
        )


class ValidationError(IntegrationError):
    """参数验证异常"""
    
    def __init__(
        self, 
        message: str,
        field_name: Optional[str] = None,
        field_value: Optional[Any] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            details=details or {"field_name": field_name, "field_value": field_value}
        )


class NetworkError(IntegrationError):
    """网络连接异常"""
    
    def __init__(
        self, 
        service_name: str,
        message: str = "Network connection failed",
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None
    ):
        super().__init__(
            message=message,
            service_name=service_name,
            error_code="NETWORK_ERROR",
            details=details,
            cause=cause
        )


class ConfigurationError(IntegrationError):
    """配置错误异常"""
    
    def __init__(
        self, 
        service_name: str,
        config_key: str,
        message: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        if not message:
            message = f"Invalid configuration for key: {config_key}"
        
        super().__init__(
            message=message,
            service_name=service_name,
            error_code="CONFIGURATION_ERROR",
            details=details or {"config_key": config_key}
        ) 