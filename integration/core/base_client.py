# base_client.py - 基础HTTP客户端抽象类
#
# @author: shaohua.sun
# @date: 2025-01-27
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Union, List
from dataclasses import dataclass
import time
from urllib.parse import urljoin

from ..config.integration_config import ServiceConfig
from .exceptions import IntegrationError, ValidationError


@dataclass
class RequestContext:
    """请求上下文"""
    service_name: str
    method: str
    url: str
    headers: Dict[str, str]
    params: Optional[Dict[str, Any]] = None
    data: Optional[Any] = None
    timeout: Optional[float] = None
    start_time: float = None
    
    def __post_init__(self):
        if self.start_time is None:
            self.start_time = time.time()


@dataclass
class ResponseContext:
    """响应上下文"""
    request_context: RequestContext
    status_code: int
    headers: Dict[str, str]
    data: Any
    elapsed_time: float
    success: bool
    error_message: Optional[str] = None


class BaseClient(ABC):
    """基础HTTP客户端抽象类"""
    
    def __init__(self, service_name: str, service_config: ServiceConfig):
        """
        初始化基础客户端
        
        Args:
            service_name: 服务名称
            service_config: 服务配置
        """
        self.service_name = service_name
        self.service_config = service_config
        self._validate_config()
    
    def _validate_config(self):
        """验证配置"""
        if not self.service_config.base_url:
            raise ValidationError(
                message="Service base_url is required",
                field_name="base_url",
                details={"service_name": self.service_name}
            )
        
        if not self.service_config.enabled:
            raise ValidationError(
                message=f"Service {self.service_name} is disabled",
                details={"service_name": self.service_name}
            )
    
    def _build_url(self, endpoint: str) -> str:
        """构建完整URL"""
        if endpoint.startswith(('http://', 'https://')):
            return endpoint
        
        # 确保endpoint以/开头
        if not endpoint.startswith('/'):
            endpoint = '/' + endpoint
        
        return urljoin(self.service_config.base_url + '/', endpoint.lstrip('/'))
    
    def _build_headers(self, additional_headers: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """构建请求头"""
        headers = self.service_config.headers.copy()
        
        # 添加API Key
        if self.service_config.api_key:
            headers['Authorization'] = f'Bearer {self.service_config.api_key}'
        
        # 添加额外的请求头
        if additional_headers:
            headers.update(additional_headers)
        
        return headers
    
    def _get_timeout(self, timeout_override: Optional[float] = None) -> float:
        """获取超时时间"""
        if timeout_override is not None:
            return timeout_override
        
        if self.service_config.timeout_override is not None:
            return float(self.service_config.timeout_override)
        
        # 返回默认超时时间
        return 30.0
    
    @abstractmethod
    def get(
        self, 
        endpoint: str, 
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: Optional[float] = None
    ) -> ResponseContext:
        """GET请求"""
        pass
    
    @abstractmethod
    def post(
        self, 
        endpoint: str, 
        data: Optional[Any] = None,
        json: Optional[Any] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: Optional[float] = None
    ) -> ResponseContext:
        """POST请求"""
        pass
    
    @abstractmethod
    def put(
        self, 
        endpoint: str, 
        data: Optional[Any] = None,
        json: Optional[Any] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: Optional[float] = None
    ) -> ResponseContext:
        """PUT请求"""
        pass
    
    @abstractmethod
    def delete(
        self, 
        endpoint: str, 
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: Optional[float] = None
    ) -> ResponseContext:
        """DELETE请求"""
        pass
    
    def _create_request_context(
        self,
        method: str,
        endpoint: str,
        headers: Dict[str, str],
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Any] = None,
        timeout: Optional[float] = None
    ) -> RequestContext:
        """创建请求上下文"""
        return RequestContext(
            service_name=self.service_name,
            method=method.upper(),
            url=self._build_url(endpoint),
            headers=headers,
            params=params,
            data=data,
            timeout=self._get_timeout(timeout)
        )
    
    def _create_response_context(
        self,
        request_context: RequestContext,
        status_code: int,
        response_headers: Dict[str, str],
        response_data: Any,
        success: bool,
        error_message: Optional[str] = None
    ) -> ResponseContext:
        """创建响应上下文"""
        elapsed_time = time.time() - request_context.start_time
        
        return ResponseContext(
            request_context=request_context,
            status_code=status_code,
            headers=response_headers,
            data=response_data,
            elapsed_time=elapsed_time,
            success=success,
            error_message=error_message
        )
    
    def health_check(self) -> bool:
        """健康检查"""
        try:
            # 默认使用GET /health或根路径进行健康检查
            response = self.get('/health', timeout=5.0)
            return response.success and response.status_code < 400
        except Exception:
            try:
                # 尝试根路径
                response = self.get('/', timeout=5.0)
                return response.success and response.status_code < 400
            except Exception:
                return False 