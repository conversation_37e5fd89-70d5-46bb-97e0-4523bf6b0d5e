# interceptors.py - 请求/响应拦截器
#
# @author: shaohua.sun
# @date: 2025-01-27
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD

from abc import ABC, abstractmethod
from typing import Optional, Dict, List
import time

from .base_client import RequestContext, ResponseContext
from utils.biz_logger import get_logger

logger = get_logger(__name__)


class RequestInterceptor(ABC):
    """请求拦截器抽象类"""
    
    @abstractmethod
    def before_request(self, context: RequestContext) -> RequestContext:
        """请求前处理"""
        pass


class ResponseInterceptor(ABC):
    """响应拦截器抽象类"""
    
    @abstractmethod  
    def after_response(self, context: ResponseContext) -> ResponseContext:
        """响应后处理"""
        pass


class LoggingInterceptor(RequestInterceptor, ResponseInterceptor):
    """日志拦截器"""
    
    def before_request(self, context: RequestContext) -> RequestContext:
        """记录请求日志"""
        # 构建完整URL（包含查询参数）
        full_url = context.url
        if context.params:
            from urllib.parse import urlencode
            query_string = urlencode(context.params)
            full_url = f"{context.url}?{query_string}"
        
        # 构建请求头信息（敏感信息脱敏）
        headers_info = {}
        for key, value in context.headers.items():
            if key.lower() in ['authorization', 'cookie', 'x-api-key']:
                # 敏感信息脱敏显示
                if len(value) > 20:
                    headers_info[key] = f"{value[:10]}...{value[-10:]}"
                else:
                    headers_info[key] = f"{value[:5]}***"
            else:
                headers_info[key] = value
        
        logger.info(
            f"INTEGRATION_REQUEST|{context.service_name}|{context.method}|{full_url}|"
            f"timeout={context.timeout}|headers={headers_info}"
        )
        return context
    
    def after_response(self, context: ResponseContext) -> ResponseContext:
        """记录响应日志"""
        req = context.request_context
        
        # 构建完整URL（包含查询参数）
        full_url = req.url
        if req.params:
            from urllib.parse import urlencode
            query_string = urlencode(req.params)
            full_url = f"{req.url}?{query_string}"
        
        logger.info(
            f"INTEGRATION_RESPONSE|{req.service_name}|{req.method}|{full_url}|"
            f"status={context.status_code}|elapsed={context.elapsed_time:.3f}s|"
            f"success={context.success}"
        )
        
        if not context.success and context.error_message:
            logger.error(
                f"INTEGRATION_ERROR|{req.service_name}|{context.error_message}"
            )
        
        return context


class ConfigOverrideInterceptor(RequestInterceptor):
    """
    配置覆盖拦截器
    
    用于动态覆盖请求的URL和请求头配置，
    支持测试环境、灰度环境等不同环境的配置切换
    """
    
    def __init__(self, base_url: str, headers: Dict[str, str]):
        """
        初始化配置覆盖拦截器
        
        Args:
            base_url: 目标环境的基础URL
            headers: 目标环境的请求头
        """
        self.base_url = base_url.rstrip('/')
        self.headers = headers
    
    def before_request(self, context: RequestContext) -> RequestContext:
        """在请求前注入配置覆盖"""
        # 替换URL为目标环境
        if not context.url.startswith(self.base_url):
            # 提取API路径部分
            if '/internal/' in context.url:
                api_path = context.url[context.url.find('/internal/'):]
                context.url = self.base_url + api_path
            else:
                # 如果无法识别路径，直接替换域名部分
                from urllib.parse import urlparse
                parsed = urlparse(context.url)
                target_parsed = urlparse(self.base_url)
                context.url = context.url.replace(
                    f"{parsed.scheme}://{parsed.netloc}", 
                    f"{target_parsed.scheme}://{target_parsed.netloc}"
                )
        
        # 注入目标环境请求头
        context.headers.update(self.headers)
        
        return context 


class RetryInterceptor(RequestInterceptor):
    """
    重试拦截器
    
    对失败的请求进行自动重试，支持指数退避策略
    """
    
    def __init__(self, max_retries: int = 3, backoff_factor: float = 1.0):
        """
        初始化重试拦截器
        
        Args:
            max_retries: 最大重试次数
            backoff_factor: 退避因子，重试间隔 = backoff_factor * (2 ** retry_count)
        """
        self.max_retries = max_retries
        self.backoff_factor = backoff_factor
    
    def before_request(self, context: RequestContext) -> RequestContext:
        """在请求前设置重试配置"""
        context.retry_count = getattr(context, 'retry_count', 0)
        context.max_retries = self.max_retries
        context.backoff_factor = self.backoff_factor
        return context


class TimeoutInterceptor(RequestInterceptor):
    """
    超时拦截器
    
    动态设置请求超时时间，支持不同接口的差异化超时策略
    """
    
    def __init__(self, default_timeout: float = 30.0, timeout_mapping: Optional[Dict[str, float]] = None):
        """
        初始化超时拦截器
        
        Args:
            default_timeout: 默认超时时间（秒）
            timeout_mapping: URL路径到超时时间的映射，例如 {'/upload': 120.0}
        """
        self.default_timeout = default_timeout
        self.timeout_mapping = timeout_mapping or {}
    
    def before_request(self, context: RequestContext) -> RequestContext:
        """在请求前设置超时时间"""
        # 检查是否有特定路径的超时配置
        for path, timeout in self.timeout_mapping.items():
            if path in context.url:
                context.timeout = timeout
                return context
        
        # 使用默认超时时间
        if context.timeout is None:
            context.timeout = self.default_timeout
        
        return context


class AuthenticationInterceptor(RequestInterceptor):
    """
    认证拦截器
    
    自动为请求添加认证信息，支持多种认证方式
    """
    
    def __init__(self, auth_type: str = "Bearer", token: str = "", 
                 auth_header: str = "Authorization"):
        """
        初始化认证拦截器
        
        Args:
            auth_type: 认证类型，如 "Bearer", "Basic", "Token" 等
            token: 认证令牌
            auth_header: 认证头字段名
        """
        self.auth_type = auth_type
        self.token = token
        self.auth_header = auth_header
    
    def before_request(self, context: RequestContext) -> RequestContext:
        """在请求前添加认证信息"""
        if self.token and self.auth_header not in context.headers:
            if self.auth_type:
                context.headers[self.auth_header] = f"{self.auth_type} {self.token}"
            else:
                context.headers[self.auth_header] = self.token
        
        return context


class RateLimitInterceptor(RequestInterceptor):
    """
    限流拦截器
    
    控制请求频率，避免触发服务端限流
    """
    
    def __init__(self, requests_per_second: float = 10.0):
        """
        初始化限流拦截器
        
        Args:
            requests_per_second: 每秒允许的请求数
        """
        self.requests_per_second = requests_per_second
        self.min_interval = 1.0 / requests_per_second
        self.last_request_time = 0.0
    
    def before_request(self, context: RequestContext) -> RequestContext:
        """在请求前执行限流控制"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_interval:
            sleep_time = self.min_interval - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
        return context


class MetricsInterceptor(RequestInterceptor, ResponseInterceptor):
    """
    指标收集拦截器
    
    收集请求响应的性能指标，用于监控和分析
    """
    
    def __init__(self):
        """初始化指标收集拦截器"""
        self.metrics = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'average_response_time': 0.0,
            'response_times': []
        }
    
    def before_request(self, context: RequestContext) -> RequestContext:
        """在请求前记录开始时间"""
        context.start_time = time.time()
        self.metrics['total_requests'] += 1
        return context
    
    def after_response(self, context: ResponseContext) -> ResponseContext:
        """在响应后收集指标"""
        if context.success:
            self.metrics['successful_requests'] += 1
        else:
            self.metrics['failed_requests'] += 1
        
        # 记录响应时间
        if hasattr(context.request_context, 'start_time'):
            response_time = context.elapsed_time
            self.metrics['response_times'].append(response_time)
            
            # 计算平均响应时间
            total_time = sum(self.metrics['response_times'])
            count = len(self.metrics['response_times'])
            self.metrics['average_response_time'] = total_time / count if count > 0 else 0.0
        
        return context
    
    def get_metrics(self) -> Dict:
        """获取收集的指标数据"""
        return self.metrics.copy()
    
    def reset_metrics(self):
        """重置指标数据"""
        self.metrics = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'average_response_time': 0.0,
            'response_times': []
        }


class GlobalInterceptorManager:
    """
    全局拦截器管理器
    
    支持全局拦截器配置，新创建的客户端会自动应用全局拦截器
    """
    
    _global_request_interceptors: List[RequestInterceptor] = []
    _global_response_interceptors: List[ResponseInterceptor] = []
    
    @classmethod
    def add_global_request_interceptor(cls, interceptor: RequestInterceptor):
        """添加全局请求拦截器"""
        cls._global_request_interceptors.append(interceptor)
    
    @classmethod
    def add_global_response_interceptor(cls, interceptor: ResponseInterceptor):
        """添加全局响应拦截器"""
        cls._global_response_interceptors.append(interceptor)
    
    @classmethod
    def remove_global_request_interceptor(cls, interceptor_type: type):
        """移除指定类型的全局请求拦截器"""
        cls._global_request_interceptors = [
            i for i in cls._global_request_interceptors
            if not isinstance(i, interceptor_type)
        ]
    
    @classmethod
    def remove_global_response_interceptor(cls, interceptor_type: type):
        """移除指定类型的全局响应拦截器"""
        cls._global_response_interceptors = [
            i for i in cls._global_response_interceptors
            if not isinstance(i, interceptor_type)
        ]
    
    @classmethod
    def clear_global_interceptors(cls):
        """清除所有全局拦截器"""
        cls._global_request_interceptors.clear()
        cls._global_response_interceptors.clear()
    
    @classmethod
    def get_global_interceptors(cls) -> Dict[str, List]:
        """获取全局拦截器"""
        return {
            'request': cls._global_request_interceptors[:],
            'response': cls._global_response_interceptors[:]
        }
    
    @classmethod
    def apply_to_client(cls, http_client):
        """将全局拦截器应用到指定客户端"""
        for interceptor in cls._global_request_interceptors:
            http_client.add_request_interceptor(interceptor)
        
        for interceptor in cls._global_response_interceptors:
            http_client.add_response_interceptor(interceptor)


 