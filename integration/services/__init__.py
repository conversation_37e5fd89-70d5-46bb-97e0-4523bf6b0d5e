# __init__.py - 第三方接口服务集合
#
# @author: shao<PERSON>.sun
# @date: 2025-01-27
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD

"""
第三方接口服务集合

本模块包含所有第三方服务的接口封装，采用统一的设计模式：
每个服务包含三个核心文件：
- models.py: 数据模型定义（使用Pydantic）
- client.py: Feign客户端声明式接口
- service.py: 业务逻辑封装和高级API

支持的第三方服务：
- ai_clock: AI Clock服务（evyd-health-manage-routines，营养、运动、睡眠、饮水追踪）
- hsd: HSD服务（evyd-health-service-hsd-ehospital，预约管理、时间段查询）
- chatbot: Chatbot服务（evyd-chatbot-chatbot-app-web，用户信息查询、ID转换）

使用示例:
    from integration.services.ai_clock import AiClockService
    from integration.services.hsd import HsdService
    from integration.services.chatbot import ChatbotService
    
    # AI Clock服务
    ai_clock_service = AiClockService()
    meal_timing = ai_clock_service.get_meal_timing()
    
    # HSD服务
    hsd_service = HsdService()
    slots = hsd_service.query_available_time_slots(doctor_id="123")
    
    # Chatbot服务
    chatbot_service = ChatbotService()
    user_info = chatbot_service.get_user_info(biz_user_id="123")
"""

# 导入各个服务模块
from . import ai_clock
from . import hsd
from . import chatbot

# 导入主要的服务类
from .ai_clock import AiClockService
from .hsd import HsdService
from .chatbot import ChatbotService

__all__ = [
    'ai_clock',
    'hsd',
    'chatbot',
    'AiClockService',
    'HsdService',
    'ChatbotService'
] 