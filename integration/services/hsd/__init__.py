# __init__.py - HSD服务模块
#
# @author: shao<PERSON>.sun
# @date: 2025-01-27
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD

"""
HSD服务模块

对接evyd-health-service-hsd-ehospital服务，提供预约管理、时间段查询、预约确认等医疗预约相关服务的接口封装。

使用示例:
    from integration.services.hsd import HsdService
    
    service = HsdService()
    available_slots = service.query_available_time_slots(doctor_id="123", date="2025-01-28")
"""

from .service import HsdService
from .client import HsdClient

__all__ = [
    'HsdService',
    'HsdClient'
] 