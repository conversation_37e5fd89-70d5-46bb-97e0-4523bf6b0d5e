# client.py - 医疗预约服务Feign客户端
#
# @author: shao<PERSON>.sun
# @date: 2025-01-27
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD

"""
医疗预约服务Feign客户端

提供对医疗预约相关API的声明式调用接口。
严格遵循AI Clock模块的封装模式：
1. 继承FeignClient基类并支持依赖注入
2. 支持请求/响应拦截器
3. 使用强类型化的请求/响应模型
4. 统一的异常处理和配置管理
"""

from typing import Dict, Any, List, Optional
from datetime import date
from ...core.feign_client import FeignClient, get_mapping, post_mapping, put_mapping, delete_mapping
from ...config.integration_config import ServiceConfig, HttpConfig
from .models import (
    # 请求模型
    AppointmentCheckRequest, TimeSlotQueryRequest,
    AppointmentBookRequest, AppointmentConfirmRequest, AppointmentCancelRequest,
    # 响应模型
    AppointmentCheckResponse, TimeSlotsResponse, AppointmentBookResponse
)


class HsdClient(FeignClient):
    """HSD服务Feign客户端"""
    
    def __init__(
        self, 
        service_config: Optional[ServiceConfig] = None, 
        http_config: Optional[HttpConfig] = None,
        request_interceptors: Optional[List] = None,
        response_interceptors: Optional[List] = None
    ):
        """
        初始化HSD服务客户端
        
        Args:
            service_config: 服务配置，为None时使用硬编码的默认配置
            http_config: HTTP配置，为None时使用硬编码的默认配置
            request_interceptors: 初始请求拦截器列表
            response_interceptors: 初始响应拦截器列表
        """
        # 硬编码的HSD服务配置
        if service_config is None:
            service_config = ServiceConfig(
                base_url="http://evyd-health-service-hsd-ehospital",
                api_key=None,
                headers={
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                enabled=True
            )
        
        # 硬编码的HTTP配置
        if http_config is None:
            http_config = HttpConfig(
                connect_timeout=10,
                read_timeout=30,
                max_retries=3,
                retry_backoff_factor=0.3,
                enable_compression=True,
                pool_connections=10,
                pool_maxsize=50
            )
        
        super().__init__(
            "hsd", 
            service_config, 
            http_config,
            request_interceptors,
            response_interceptors
        )
    
    @get_mapping("/internal/ai-process/check-appointment")
    def check_appointment(self, request: AppointmentCheckRequest) -> AppointmentCheckResponse:
        """
        检查预约状态
        
        Args:
            request: 预约检查请求模型，包含用户ID和时间段ID列表
            
        Returns:
            预约检查结果响应模型
        """
        pass
    
    @get_mapping("/internal/ai-process/slots")
    def get_slots(self, request: TimeSlotQueryRequest) -> TimeSlotsResponse:
        """
        查询可用时间段
        
        Args:
            request: 时间段查询请求模型
            
        Returns:
            时间段查询结果响应模型
        """
        pass
    
    @post_mapping("/internal/ai-process/appointment")
    def book_appointment(self, request: AppointmentBookRequest) -> AppointmentBookResponse:
        """
        申请预约
        
        Args:
            request: 预约申请请求模型
            
        Returns:
            预约申请响应模型
        """
        pass
    
    @put_mapping("/internal/ai-process/appointment/confirm")
    def confirm_appointment(self, request: AppointmentConfirmRequest) -> AppointmentBookResponse:
        """
        确认预约
        
        Args:
            request: 预约确认请求模型
            
        Returns:
            预约记录响应模型
        """
        pass
    
    @delete_mapping("/internal/ai-process/appointment/cancel")
    def cancel_appointment(self, request: AppointmentCancelRequest) -> AppointmentBookResponse:
        """
        取消预约
        
        Args:
            request: 预约取消请求模型
            
        Returns:
            预约记录响应模型
        """
        pass 