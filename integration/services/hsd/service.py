# service.py - HSD服务业务封装
#
# @author: shaohua.sun
# @date: 2025-01-27
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD

"""
HSD服务业务封装

提供HSD服务相关的高级业务接口封装，包括数据验证、类型转换等。
对接evyd-health-service-hsd-ehospital服务。
严格遵循AI Clock模块的封装模式：
1. 支持客户端依赖注入，保持向下兼容
2. 提供强类型化的业务接口
3. 统一的异常处理和日志记录
4. 提供便利方法和数据转换功能
"""

from typing import List, Dict, Any, Optional
from datetime import date, datetime

from utils.env_util import EnvUtil
from ...core.exceptions import IntegrationError, ValidationError
from utils.biz_logger import get_logger
from .client import HsdClient
from .models import (
    AppointmentCheckRequest, TimeSlotQueryRequest, LegacyTimeSlotQueryRequest,
    AppointmentBookRequest, AppointmentConfirmRequest, AppointmentCancelRequest,
    AppointmentCheckResponse, TimeSlotsResponse, LegacyTimeSlotsResponse, AppointmentBookResponse,
    AppointmentStatus, DaySlots, TimeSlot, DoctorInfo, AppointmentRecord
)

logger = get_logger(__name__)


class HsdService:
    """HSD服务业务封装类"""
    
    def __init__(self, client: Optional[HsdClient] = None):
        """
        初始化HSD服务
        
        Args:
            client: 可选的HsdClient实例，用于依赖注入。
                   如果为None，则创建默认客户端实例。
        """
        # 支持客户端依赖注入，保持向下兼容
        if client is not None:
            self.client = client
            logger.info("HSD服务初始化完成（使用注入的客户端）")
        else:
            self.client = HsdClient()
            logger.info("HSD服务初始化完成")
    
    # === 核心接口方法 ===
    
    def check_appointment(self, user_id: str, slot_id_list: List[int]) -> Optional[List[AppointmentStatus]]:
        """
        检查预约状态
        
        Args:
            user_id: 用户ID
            slot_id_list: 时间段ID列表
            
        Returns:
            预约状态列表，如果调用失败返回None
            
        Raises:
            IntegrationError: 服务调用失败时抛出
        """
        try:
            logger.info(f"检查预约状态: user_id={user_id}, slot_id_list={slot_id_list}")
            
            # 数据验证
            if not user_id:
                raise ValueError("用户ID不能为空")
            if not slot_id_list:
                raise ValueError("时间段ID列表不能为空")
            
            # 创建请求模型
            request = AppointmentCheckRequest(
                userId=user_id,
                slotIdList=slot_id_list
            )
            if EnvUtil.is_local():
                response = self._mock_appointment_data()
            else:
                response = self.client.check_appointment(request)
            
            # 判断响应状态，直接返回data字段
            if hasattr(response, 'code') and response.code == 0:
                return response.data if hasattr(response, 'data') else None
            elif isinstance(response, dict) and response.get('code') == 0:
                return response.get('data')
            else:
                # 安全地获取code值
                code = getattr(response, 'code', None)
                if code is None and isinstance(response, dict):
                    code = response.get('code', 'unknown')
                logger.warning(f"检查预约状态失败，code: {code}")
                return None
            
        except Exception as e:
            logger.error(f"检查预约状态失败: {e}")
            raise IntegrationError(f"检查预约状态失败: {str(e)}")
    
    def get_slots_java_compatible(self, appt_from_timestamp: str, appt_to_timestamp: str, user_id: str) -> Optional[List[DaySlots]]:
        """
        查询可用时间段（Java版本兼容接口）
        
        Args:
            appt_from_timestamp: 查询预约开始时间，格式：2025-05-22 16:21:00
            appt_to_timestamp: 查询预约结束时间，格式：2025-05-23 16:21:00
            user_id: 用户ID（用于计算距离）
            
        Returns:
            时间段数据列表，如果调用失败返回None
            
        Raises:
            IntegrationError: 服务调用失败时抛出
        """
        try:
            logger.info(f"查询可用时间段（Java兼容）: appt_from_time={appt_from_timestamp}, appt_to_time={appt_to_timestamp}, user_id={user_id}")
            
            # 数据验证
            if not appt_from_timestamp:
                raise ValueError("预约开始时间不能为空")
            if not appt_to_timestamp:
                raise ValueError("预约结束时间不能为空")
            if not user_id:
                raise ValueError("用户ID不能为空")
            
            # 创建请求模型（匹配Java版本SlotQo）
            request = TimeSlotQueryRequest(
                apptFromTimestamp=appt_from_timestamp,
                apptToTimestamp=appt_to_timestamp,
                userId=user_id
            )

            if EnvUtil.is_local():
                response = self._mock_slot_data()
            else:
                response = self.client.get_slots(request)
            
            # 判断响应状态，直接返回data字段
            if hasattr(response, 'code') and response.code == 0:
                return response.data if hasattr(response, 'data') else None
            elif isinstance(response, dict) and response.get('code') == 0:
                return response.get('data')
            else:
                logger.warning(f"查询可用时间段失败，code: {getattr(response, 'code', response.get('code', 'unknown'))}")
                return None
            
        except Exception as e:
            logger.error(f"查询可用时间段失败: {e}")
            raise IntegrationError(f"查询可用时间段失败: {str(e)}")
    
    def get_slots(self, query_date: date, doctor_id: Optional[str] = None, 
                  department: Optional[str] = None, hospital_id: Optional[str] = None) -> Optional[List[DaySlots]]:
        """
        查询可用时间段
        
        Args:
            query_date: 查询日期
            doctor_id: 医生ID
            department: 科室
            hospital_id: 医院ID
            
        Returns:
            时间段数据列表，如果调用失败返回None
            
        Raises:
            IntegrationError: 服务调用失败时抛出
        """
        try:
            logger.info(f"查询可用时间段: query_date={query_date}, doctor_id={doctor_id}, department={department}, hospital_id={hospital_id}")
            
            if not query_date:
                raise ValueError("查询日期不能为空")
            
            # 创建请求模型
            request = LegacyTimeSlotQueryRequest(
                queryDate=query_date,
                doctorId=doctor_id,
                department=department,
                hospitalId=hospital_id
            )
            
            response = self.client.get_slots(request)
            
            # 判断响应状态，直接返回data字段
            if hasattr(response, 'code') and response.code == 0:
                return response.data if hasattr(response, 'data') else None
            elif isinstance(response, dict) and response.get('code') == 0:
                return response.get('data')
            else:
                logger.warning(f"查询可用时间段失败，code: {getattr(response, 'code', response.get('code', 'unknown'))}")
                return None
            
        except Exception as e:
            logger.error(f"查询可用时间段失败: {e}")
            raise IntegrationError(f"查询可用时间段失败: {str(e)}")
    
    def book_appointment(self, patient_id: str, patient_name: str, patient_phone: str,
                        doctor_id: str, slot_id: str, appointment_date: date,
                        symptoms: Optional[str] = None, remarks: Optional[str] = None) -> Optional[AppointmentRecord]:
        """
        申请预约
        
        Args:
            patient_id: 患者ID
            patient_name: 患者姓名
            patient_phone: 患者电话
            doctor_id: 医生ID
            slot_id: 时间段ID
            appointment_date: 预约日期
            symptoms: 症状描述
            remarks: 备注信息
            
        Returns:
            预约记录，如果调用失败返回None
            
        Raises:
            IntegrationError: 服务调用失败时抛出
        """
        try:
            logger.info(f"申请预约: patient_id={patient_id}, patient_name={patient_name}, doctor_id={doctor_id}, slot_id={slot_id}")
            
            # 数据验证
            if not patient_id:
                raise ValueError("患者ID不能为空")
            if not patient_name:
                raise ValueError("患者姓名不能为空")
            if not patient_phone:
                raise ValueError("患者电话不能为空")
            if not doctor_id:
                raise ValueError("医生ID不能为空")
            if not slot_id:
                raise ValueError("时间段ID不能为空")
            if not appointment_date:
                raise ValueError("预约日期不能为空")
            
            # 创建请求模型
            request = AppointmentBookRequest(
                patientId=patient_id,
                patientName=patient_name,
                patientPhone=patient_phone,
                doctorId=doctor_id,
                slotId=slot_id,
                appointmentDate=appointment_date,
                symptoms=symptoms,
                remarks=remarks
            )
            
            response = self.client.book_appointment(request)
            
            # 判断响应状态，直接返回data字段
            if hasattr(response, 'code') and response.code == 0:
                return response.data if hasattr(response, 'data') else None
            elif isinstance(response, dict) and response.get('code') == 0:
                return response.get('data')
            else:
                # 安全地获取code值
                code = getattr(response, 'code', None)
                if code is None and isinstance(response, dict):
                    code = response.get('code', 'unknown')
                logger.warning(f"申请预约失败，code: {code}")
                return None
            
        except Exception as e:
            logger.error(f"申请预约失败: {e}")
            raise IntegrationError(f"申请预约失败: {str(e)}")
    
    def confirm_appointment(self, appointment_id: str, confirm_code: Optional[str] = None) -> Optional[AppointmentRecord]:
        """
        确认预约
        
        Args:
            appointment_id: 预约ID
            confirm_code: 确认码
            
        Returns:
            预约记录，如果调用失败返回None
            
        Raises:
            IntegrationError: 服务调用失败时抛出
        """
        try:
            logger.info(f"确认预约: appointment_id={appointment_id}")
            
            if not appointment_id:
                raise ValueError("预约ID不能为空")
            
            # 创建请求模型
            request = AppointmentConfirmRequest(
                appointmentId=appointment_id,
                confirmCode=confirm_code
            )
            
            response = self.client.confirm_appointment(request)
            
            # 判断响应状态，直接返回data字段
            if hasattr(response, 'code') and response.code == 0:
                return response.data if hasattr(response, 'data') else None
            elif isinstance(response, dict) and response.get('code') == 0:
                return response.get('data')
            else:
                # 安全地获取code值
                code = getattr(response, 'code', None)
                if code is None and isinstance(response, dict):
                    code = response.get('code', 'unknown')
                logger.warning(f"确认预约失败，code: {code}")
                return None
            
        except Exception as e:
            logger.error(f"确认预约失败: {e}")
            raise IntegrationError(f"确认预约失败: {str(e)}")
    
    def cancel_appointment(self, appointment_id: str, cancel_reason: Optional[str] = None) -> Optional[AppointmentRecord]:
        """
        取消预约
        
        Args:
            appointment_id: 预约ID
            cancel_reason: 取消原因
            
        Returns:
            预约记录，如果调用失败返回None
            
        Raises:
            IntegrationError: 服务调用失败时抛出
        """
        try:
            logger.info(f"取消预约: appointment_id={appointment_id}, cancel_reason={cancel_reason}")
            
            if not appointment_id:
                raise ValueError("预约ID不能为空")
            
            # 创建请求模型
            request = AppointmentCancelRequest(
                appointmentId=appointment_id,
                cancelReason=cancel_reason
            )
            
            response = self.client.cancel_appointment(request)
            
            # 判断响应状态，直接返回data字段
            if hasattr(response, 'code') and response.code == 0:
                return response.data if hasattr(response, 'data') else None
            elif isinstance(response, dict) and response.get('code') == 0:
                return response.get('data')
            else:
                # 安全地获取code值
                code = getattr(response, 'code', None)
                if code is None and isinstance(response, dict):
                    code = response.get('code', 'unknown')
                logger.warning(f"取消预约失败，code: {code}")
                return None
            
        except Exception as e:
            logger.error(f"取消预约失败: {e}")
            raise IntegrationError(f"取消预约失败: {str(e)}")
    
    # === 便利方法接口（可选，基于核心接口构建） ===
    
    def get_available_slots_by_doctor(self, doctor_id: str, query_date: date) -> List[TimeSlot]:
        """
        获取指定医生的可用时间段（便利方法）
        
        Args:
            doctor_id: 医生ID
            query_date: 查询日期
            
        Returns:
            可用时间段列表
        """
        day_slots_list = self.get_slots(query_date=query_date, doctor_id=doctor_id)
        if day_slots_list:
            day_slots = day_slots_list[0] if day_slots_list else None
            return day_slots.availableSlots if day_slots else []
        return []
    
    def get_available_slots_by_department(self, department: str, query_date: date, 
                                        hospital_id: Optional[str] = None) -> Dict[str, List[TimeSlot]]:
        """
        获取指定科室的可用时间段（便利方法）
        
        Args:
            department: 科室名称
            query_date: 查询日期
            hospital_id: 医院ID
            
        Returns:
            按医生分组的可用时间段字典 {医生ID: [时间段列表]}
        """
        day_slots_list = self.get_slots(query_date=query_date, department=department, hospital_id=hospital_id)
        result = {}
        
        if day_slots_list:
            for day_slot in day_slots_list:
                if day_slot.doctor:
                    result[day_slot.doctor.doctorId] = day_slot.availableSlots
        
        return result
    
    def check_slot_availability(self, user_id: str, slot_id: int) -> bool:
        """
        检查单个时间段是否可用（便利方法）
        
        Args:
            user_id: 用户ID
            slot_id: 时间段ID
            
        Returns:
            是否可用
        """
        try:
            status_list = self.check_appointment(user_id=user_id, slot_id_list=[slot_id])
            
            if status_list:
                status = status_list[0] if status_list else None
                return status.isAvailable if status else False
            return False
        except Exception as e:
            logger.warning(f"检查时间段可用性失败: {e}")
            return False
    
    def get_doctor_info_by_slot(self, query_date: date, slot_id: str) -> Optional[DoctorInfo]:
        """
        根据时间段获取医生信息（便利方法）
        
        Args:
            query_date: 查询日期
            slot_id: 时间段ID
            
        Returns:
            医生信息，如果未找到返回None
        """
        try:
            day_slots_list = self.get_slots(query_date=query_date)
            
            if day_slots_list:
                for day_slot in day_slots_list:
                    for time_slot in day_slot.availableSlots:
                        if time_slot.slotId == slot_id:
                            return day_slot.doctor
            
            return None
        except Exception as e:
            logger.warning(f"获取医生信息失败: {e}")
            return None
    
    # def quick_book_appointment(self, patient_id: str, patient_name: str, patient_phone: str,
    #                           doctor_id: str, appointment_date: date, preferred_time: str = "morning") -> Optional[str]:
    #     """
    #     快速预约（便利方法）- 自动选择可用时间段
    #
    #     Args:
    #         patient_id: 患者ID
    #         patient_name: 患者姓名
    #         patient_phone: 患者电话
    #         doctor_id: 医生ID
    #         appointment_date: 预约日期
    #         preferred_time: 偏好时间（morning/afternoon/evening）
    #
    #     Returns:
    #         预约ID，如果预约失败返回None
    #     """
    #     try:
    #         # 获取可用时间段
    #         available_slots = self.get_available_slots_by_doctor(doctor_id, appointment_date)
    #
    #         if not available_slots:
    #             logger.warning(f"医生 {doctor_id} 在 {appointment_date} 没有可用时间段")
    #             return None
    #
    #         # 根据偏好时间选择时间段
    #         selected_slot = None
    #         for slot in available_slots:
    #             if preferred_time == "morning" and slot.startTime.hour < 12:
    #                 selected_slot = slot
    #                 break
    #             elif preferred_time == "afternoon" and 12 <= slot.startTime.hour < 18:
    #                 selected_slot = slot
    #                 break
    #             elif preferred_time == "evening" and slot.startTime.hour >= 18:
    #                 selected_slot = slot
    #                 break
    #
    #         # 如果没有找到偏好时间段，选择第一个可用时间段
    #         if not selected_slot:
    #             selected_slot = available_slots[0]
    #
    #         # 预约
    #         appointment_record = self.book_appointment(
    #             patient_id=patient_id,
    #             patient_name=patient_name,
    #             patient_phone=patient_phone,
    #             doctor_id=doctor_id,
    #             slot_id=selected_slot.slotId,
    #             appointment_date=appointment_date
    #         )
    #
    #         if appointment_record:
    #             return appointment_record.appointmentId
    #         return None
    #
    #     except Exception as e:
    #         logger.warning(f"快速预约失败: {e}")
    #         return None

    def _mock_slot_data(self)->TimeSlotsResponse:
        """
            使用给定的 JSON 数据创建 TimeSlotsResponse 实例。

            Returns:
                TimeSlotsResponse: 包含模拟数据的响应实例。
            Raises:
                ValidationError: 如果数据验证失败。
            """
        json_data = {
            "code": 0,
            "message": "success",
            "data": [
                {
                    "hospitalList": {
                        "hospitalId": "H001",
                        "hospitalName": "Hospital 1",
                        "address": "Brunei dress 1",
                        "hospitalType": "综合医院",
                        "introduction": "这是一家测试医院",
                        "appointmentRemainder": 10,
                        "videoRemainder": 5,
                        "distance": 1000,
                        "district": "测试区",
                        "unit": "KM",
                        "hospitalImage": "http://example.com/hospital.jpg",
                        "isRecommend": 1,
                        "location": "120.5,30.5",
                        "mukim": "测试区域",
                        "sortWeight": 100,
                        "clinicList": [
                            {
                                "clinicId": "C001",
                                "clinicName": "测试诊所1",
                                "clinicType": "普通门诊",
                                "introduction": "这是一个测试诊所1",
                                "slotList": [
                                    {
                                        "apptDate": "2025-07-10",
                                        "visitTimeList": [
                                            {
                                                "amPm": "am",
                                                "apptFromTime": "08:00",
                                                "apptToTime": "12:00",
                                                "scheduleId": 1111,
                                                "remainder": 11
                                            }
                                        ]
                                    }
                                ]
                            }
                        ]
                    }
                },
                {
                    "hospitalList": {
                        "hospitalId": "H001",
                        "hospitalName": "Hospital 2",
                        "address": "Brunei dress 2",
                        "hospitalType": "综合医院",
                        "introduction": "这是一家测试医院",
                        "appointmentRemainder": 10,
                        "videoRemainder": 5,
                        "distance": 10,
                        "district": "测试区",
                        "unit": "KM",
                        "hospitalImage": "http://example.com/hospital.jpg",
                        "isRecommend": 1,
                        "location": "120.5,30.5",
                        "mukim": "测试区域",
                        "sortWeight": 100,
                        "clinicList": [
                            {
                                "clinicId": "C001",
                                "clinicName": "测试诊所2",
                                "clinicType": "普通门诊",
                                "introduction": "这是一个测试诊所2",
                                "slotList": [
                                    {
                                        "apptDate": "2025-07-10",
                                        "visitTimeList": [
                                            {
                                                "amPm": "am",
                                                "apptFromTime": "08:00",
                                                "apptToTime": "12:00",
                                                "scheduleId": 1111,
                                                "remainder": 11
                                            }
                                        ]
                                    }
                                ]
                            }
                        ]
                    }
                },
                {
                    "hospitalList": {
                        "hospitalId": "H001",
                        "hospitalName": "Hospital 3",
                        "address": "Brunei dress 3",
                        "hospitalType": "综合医院",
                        "introduction": "这是一家测试医院",
                        "appointmentRemainder": 10,
                        "videoRemainder": 5,
                        "distance": 20,
                        "district": "测试区",
                        "unit": "KM",
                        "hospitalImage": "http://example.com/hospital.jpg",
                        "isRecommend": 1,
                        "location": "120.5,30.5",
                        "mukim": "测试区域",
                        "sortWeight": 100,
                        "clinicList": [
                            {
                                "clinicId": "C001",
                                "clinicName": "测试诊所3",
                                "clinicType": "普通门诊",
                                "introduction": "这是一个测试诊所3",
                                "slotList": [
                                    {
                                        "apptDate": "2025-06-05",
                                        "visitTimeList": [
                                            {
                                                "amPm": "am",
                                                "apptFromTime": "08:00",
                                                "apptToTime": "12:00",
                                                "scheduleId": 1111,
                                                "remainder": 11
                                            }
                                        ]
                                    }
                                ]
                            }
                        ]
                    }
                }
            ]
        }
        try:
            return TimeSlotsResponse.model_validate(json_data)
        except ValidationError as e:
            logger.error(f"Slot Mock数据验证失败: {e}")
            raise

    def _mock_appointment_data(self) -> AppointmentCheckResponse:
        mock_data = {
            "code": 0,
            "message": "success",
            "data": {
                "1111": None,
                "2222": "ORDER_ID_2",
                "9026994": "ORDER_ID_99"
            }
        }
        try:
            return AppointmentCheckResponse.model_validate(mock_data)
        except ValidationError as e:
            logger.error(f"Check Mock数据验证失败: {e}")
            raise