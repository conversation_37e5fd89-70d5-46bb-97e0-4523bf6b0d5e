# models.py - 医疗预约服务数据模型
#
# @author: shaohua.sun
# @date: 2025-01-27
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD

"""
医疗预约服务数据模型定义

包含预约管理、时间段查询、预约确认等服务的请求和响应模型。
严格遵循AI Clock模块的封装模式：
1. 统一使用驼峰命名，与API规范一致
2. 明确的字段定义，避免过度抽象
3. 继承BaseResponse[T]作为统一响应格式
4. 分离关注点：模型负责结构定义，业务逻辑在服务层
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime, date, time
from enum import Enum
from ...core.models import BaseResponse


class AppointmentStatus(str, Enum):
    """预约状态枚举"""
    PENDING = "pending"          # 待确认
    CONFIRMED = "confirmed"      # 已确认
    CANCELLED = "cancelled"      # 已取消
    COMPLETED = "completed"      # 已完成
    NO_SHOW = "no_show"         # 未到场


# ==================== 预约检查相关模型 ====================

class AppointmentCheckRequest(BaseModel):
    """预约检查请求模型"""
    userId: str = Field(..., description="用户ID")
    slotIdList: List[int] = Field(..., description="时间段ID列表")
    
    model_config = {"populate_by_name": True}


class AppointmentStatus(BaseModel):
    """预约状态信息模型"""
    slotId: int = Field(..., description="时间段ID")
    isAvailable: bool = Field(..., description="是否可用")
    currentBookings: int = Field(0, description="当前预约数量")
    maxBookings: int = Field(1, description="最大预约数量")
    reason: Optional[str] = Field(None, description="不可用原因")
    
    model_config = {"populate_by_name": True}


class AppointmentCheckResponse(BaseResponse[Dict[str, Any]]):
    """
    预约检查响应模型
    
    实际API返回示例：
    {
        "code": 0,
        "message": "success",
        "data": {
            "123": null,
            "124": null,
            "125": null
        },
        "timeStamp": 1751961748458
    }
    
    data字段是字典格式，key为时间段ID字符串，value为预约状态（null表示可用）
    """
    pass


# ==================== 时间段查询相关模型 ====================

class TimeSlotQueryRequest(BaseModel):
    """时间段查询请求模型（匹配Java版本SlotQo）"""
    apptFromTimestamp: str = Field(..., description="查询预约开始时间，格式：2025-05-22 16:21:00")
    apptToTimestamp: str = Field(..., description="查询预约结束时间，格式：2025-05-23 16:21:00")
    userId: str = Field(..., description="当前用户ID（用于计算距离）")
    
    model_config = {"populate_by_name": True}


class LegacyTimeSlotQueryRequest(BaseModel):
    """传统时间段查询请求模型（保持向后兼容）"""
    doctorId: Optional[str] = Field(None, description="医生ID")
    department: Optional[str] = Field(None, description="科室")
    queryDate: date = Field(..., description="查询日期")
    hospitalId: Optional[str] = Field(None, description="医院ID")
    
    model_config = {"populate_by_name": True}


class DoctorInfo(BaseModel):
    """医生信息模型"""
    doctorId: str = Field(..., description="医生ID")
    doctorName: str = Field(..., description="医生姓名")
    department: str = Field(..., description="科室")
    title: Optional[str] = Field(None, description="职称")
    specialties: List[str] = Field(default=[], description="专业特长")
    hospitalId: Optional[str] = Field(None, description="医院ID")
    hospitalName: Optional[str] = Field(None, description="医院名称")
    
    model_config = {"populate_by_name": True}


class TimeSlot(BaseModel):
    """时间段模型"""
    slotId: str = Field(..., description="时间段ID")
    startTime: time = Field(..., description="开始时间")
    endTime: time = Field(..., description="结束时间")
    isAvailable: bool = Field(True, description="是否可用")
    maxAppointments: int = Field(1, description="最大预约数")
    currentAppointments: int = Field(0, description="当前预约数")
    price: Optional[float] = Field(None, description="价格")
    
    model_config = {"populate_by_name": True}


class DaySlots(BaseModel):
    """单日时间段信息模型"""
    queryDate: date = Field(..., description="日期")
    doctor: Optional[DoctorInfo] = Field(None, description="医生信息")
    availableSlots: List[TimeSlot] = Field(default=[], description="可用时间段")
    totalSlots: int = Field(0, description="总时间段数")
    availableCount: int = Field(0, description="可用时间段数")
    
    model_config = {"populate_by_name": True}


# ==================== Java版本兼容的响应模型 ====================

class HospitalVo(BaseModel):
    """医院信息模型（匹配Java版本SlotWrapperVo.HospitalVo）"""
    address: Optional[str] = Field(None, description="地址")
    appointmentRemainder: Optional[int] = Field(None, description="预约剩余数量")
    distance: Optional[float] = Field(None, description="距离（浮点数，如14.14）")
    district: Optional[str] = Field(None, description="区域")
    unit: Optional[str] = Field(None, description="单位")
    hospitalId: Optional[str] = Field(None, description="医院ID")
    hospitalImage: Optional[str] = Field(None, description="医院图片")
    hospitalName: Optional[str] = Field(None, description="医院名称")
    hospitalType: Optional[str] = Field(None, description="医院类型")
    introduction: Optional[str] = Field(None, description="介绍")
    isRecommend: Optional[int] = Field(None, description="是否推荐")
    location: Optional[str] = Field(None, description="位置")
    mukim: Optional[str] = Field(None, description="区域")
    sortWeight: Optional[int] = Field(None, description="排序权重")
    videoRemainder: Optional[int] = Field(None, description="视频剩余数量")
    clinicList: Optional[List['ClinicVo']] = Field(None, description="诊所列表")
    
    model_config = {"populate_by_name": True}


class VisitTimeVo(BaseModel):
    """就诊时间模型（匹配Java版本SlotWrapperVo.SlotVo.VisitTimeVo）"""
    amPm: Optional[str] = Field(None, description="上午/下午")
    apptFromTime: Optional[str] = Field(None, description="预约开始时间")
    apptToTime: Optional[str] = Field(None, description="预约结束时间")
    remainder: Optional[int] = Field(None, description="剩余数量")
    scheduleId: Optional[int] = Field(None, description="时间段ID")
    totalSlot: Optional[int] = Field(None, description="总时间段数")
    type: Optional[int] = Field(None, description="类型")
    
    model_config = {"populate_by_name": True}


class SlotVo(BaseModel):
    """时间段模型（匹配Java版本SlotWrapperVo.SlotVo）"""
    appointmentFlag: Optional[int] = Field(None, description="预约标志")
    apptDate: Optional[str] = Field(None, description="预约日期")
    clinicId: Optional[str] = Field(None, description="诊所ID")
    hospitalId: Optional[str] = Field(None, description="医院ID")
    totalRemainder: Optional[int] = Field(None, description="总剩余数量")
    totalSlot: Optional[int] = Field(None, description="总时间段数")
    visitTimeList: Optional[List[VisitTimeVo]] = Field(None, description="就诊时间列表")
    
    model_config = {"populate_by_name": True}


class ClinicVo(BaseModel):
    """诊所模型（匹配Java版本SlotWrapperVo.ClinicVo）"""
    apptType: Optional[str] = Field(None, description="预约类型")
    bruhealthName: Optional[str] = Field(None, description="文莱健康名称")
    clinicCategory: Optional[int] = Field(None, description="诊所类别")
    clinicId: Optional[str] = Field(None, description="诊所ID")
    clinicName: Optional[str] = Field(None, description="诊所名称")
    clinicShortName: Optional[str] = Field(None, description="诊所简称")
    clinicType: Optional[str] = Field(None, description="诊所类型")
    createTime: Optional[str] = Field(None, description="创建时间")
    hospitalId: Optional[str] = Field(None, description="医院ID")
    id: Optional[int] = Field(None, description="ID")
    introduction: Optional[str] = Field(None, description="介绍")
    rescheduleStatus: Optional[str] = Field(None, description="重新安排状态")
    scheduleStatus: Optional[str] = Field(None, description="时间表状态")
    specialityCode: Optional[str] = Field(None, description="专业代码")
    status: Optional[str] = Field(None, description="状态")
    type: Optional[str] = Field(None, description="类型")
    updateTime: Optional[str] = Field(None, description="更新时间")
    slotList: Optional[List[SlotVo]] = Field(None, description="时间段列表")
    
    model_config = {"populate_by_name": True}


class SlotWrapperVo(BaseModel):
    """时间段包装器模型（匹配Java版本SlotWrapperVo）"""
    hospitalList: Optional[HospitalVo] = Field(None, description="医院信息")
    
    model_config = {"populate_by_name": True}


class TimeSlotsResponse(BaseResponse[List[SlotWrapperVo]]):
    """
    时间段查询响应模型（匹配Java版本SlotResultVo）
    
    API返回示例（匹配Java版本结构）：
    {
        "code": 0,
        "message": null,
        "data": [
            {
                "hospitalList": {
                    "hospitalId": "H001",
                    "hospitalName": "协和医院",
                    "clinicList": [
                    {
                            "clinicId": "C001",
                            "clinicName": "内科",
                            "slotList": [
                                {
                                    "apptDate": "2025-01-28",
                                    "visitTimeList": [
                                        {
                                            "scheduleId": 123,
                                            "apptFromTime": "09:00",
                                            "apptToTime": "09:30",
                                            "remainder": 1
                                        }
                                    ]
                    }
                            ]
                        }
                    ]
                }
            }
        ]
    }
    """
    pass


# ==================== 传统响应模型（保持向后兼容） ====================

class LegacyTimeSlotsResponse(BaseResponse[List[DaySlots]]):
    """
    传统时间段查询响应模型（保持向后兼容）
    """
    pass


# ==================== 预约管理相关模型 ====================

class AppointmentBookRequest(BaseModel):
    """预约申请请求模型"""
    patientId: str = Field(..., description="患者ID")
    patientName: str = Field(..., description="患者姓名")
    patientPhone: str = Field(..., description="患者电话")
    doctorId: str = Field(..., description="医生ID")
    slotId: str = Field(..., description="时间段ID")
    appointmentDate: date = Field(..., description="预约日期")
    symptoms: Optional[str] = Field(None, description="症状描述")
    remarks: Optional[str] = Field(None, description="备注信息")
    
    model_config = {"populate_by_name": True}


class PatientInfo(BaseModel):
    """患者信息模型"""
    patientId: str = Field(..., description="患者ID")
    patientName: str = Field(..., description="患者姓名")
    patientPhone: str = Field(..., description="患者电话")
    idCard: Optional[str] = Field(None, description="身份证号")
    age: Optional[int] = Field(None, description="年龄")
    gender: Optional[str] = Field(None, description="性别")
    
    model_config = {"populate_by_name": True}


class AppointmentRecord(BaseModel):
    """预约记录模型"""
    appointmentId: str = Field(..., description="预约ID")
    patientInfo: PatientInfo = Field(..., description="患者信息")
    doctorInfo: DoctorInfo = Field(..., description="医生信息")
    appointmentDate: date = Field(..., description="预约日期")
    timeSlot: TimeSlot = Field(..., description="时间段信息")
    status: str = Field(..., description="预约状态")
    symptoms: Optional[str] = Field(None, description="症状描述")
    remarks: Optional[str] = Field(None, description="备注信息")
    createdTime: datetime = Field(default_factory=datetime.now, description="创建时间")
    
    model_config = {"populate_by_name": True}


class AppointmentBookResponse(BaseResponse[AppointmentRecord]):
    """
    预约申请响应模型
    
    API返回示例：
    {
        "code": 0,
        "message": null,
        "data": {
            "appointmentId": "A001",
            "patientInfo": {
                "patientId": "P001",
                "patientName": "张三",
                "patientPhone": "13800138000"
            },
            "doctorInfo": {
                "doctorId": "D001",
                "doctorName": "张医生",
                "department": "内科",
                "title": "主治医师",
                "specialties": ["心血管"],
                "hospitalId": "H001",
                "hospitalName": "协和医院"
            },
            "appointmentDate": "2025-01-28",
            "timeSlot": {
                "slotId": "S001",
                "startTime": "09:00:00",
                "endTime": "09:30:00",
                "isAvailable": false,
                "maxAppointments": 1,
                "currentAppointments": 1,
                "price": 50.0
            },
            "status": "confirmed",
            "symptoms": "头痛",
            "remarks": "预约检查",
            "createdTime": "2025-01-27T14:30:00"
        }
    }
    """
    pass


class AppointmentConfirmRequest(BaseModel):
    """预约确认请求模型"""
    appointmentId: str = Field(..., description="预约ID")
    confirmCode: Optional[str] = Field(None, description="确认码")
    
    model_config = {"populate_by_name": True}


class AppointmentCancelRequest(BaseModel):
    """预约取消请求模型"""
    appointmentId: str = Field(..., description="预约ID")
    cancelReason: Optional[str] = Field(None, description="取消原因")
    
    model_config = {"populate_by_name": True} 