# __init__.py - AI Clock服务模块
#
# @author: s<PERSON><PERSON>.sun
# @date: 2025-01-27
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD

"""
AI Clock服务模块

提供营养、运动、睡眠、饮水等健康数据的追踪和管理服务。
包含：
- Feign客户端接口定义
- 业务服务封装
- 数据模型定义

使用示例:
    from integration.services.ai_clock import AiClockService
    
    service = AiClockService()
    meal_timing = service.get_meal_timing()
"""

from .client import AiClockClient
from .service import AiClockService

__all__ = [
    "AiClockClient",
    "AiClockService"
] 