# models.py - AI时钟服务模型定义（合理设计版本）
#
# @author: shaohua.sun
# @date: 2025-01-27  
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD

"""
AI时钟服务模型定义（合理设计版本）

本模块定义了AI时钟服务所有接口的请求和响应模型。
设计原则：
1. 统一使用驼峰命名，与API规范一致
2. 明确的字段定义，避免过度抽象
3. 对于动态字段场景，直接使用Dict，在FeignClient层提供便利方法
4. 保持模型简洁性和可读性
5. 分离关注点：模型负责结构定义，业务逻辑在服务层
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from ...core.models import BaseResponse, BooleanResponse


# ==================== 基础请求模型 ====================

class DateRangeRequest(BaseModel):
    """日期范围查询请求"""
    startDate: str = Field(..., description="开始日期，格式：yyyy-MM-dd")
    endDate: str = Field(..., description="结束日期，格式：yyyy-MM-dd")
    userId: str = Field(..., description="用户ID")
    
    model_config = {"populate_by_name": True}


# ==================== 食物和营养相关模型 ====================

class FoodItem(BaseModel):
    """
    食物项目
    
    表示用户摄入的单个食物项目，包含营养成分信息
    """
    code: Optional[str] = Field(None, description="食物代码")
    name: str = Field(..., description="食物名称")
    servingNumber: float = Field(..., description="份数")
    servingUnit: str = Field(..., description="单位")
    calories: Optional[float] = Field(None, description="卡路里(kcal)")
    carbohydrate: Optional[float] = Field(None, description="碳水化合物(g)")
    protein: Optional[float] = Field(None, description="蛋白质(g)")
    fat: Optional[float] = Field(None, description="脂肪(g)")
    
    model_config = {"populate_by_name": True}


class MealTiming(BaseModel):
    """用餐时间段"""
    code: int = Field(..., description="时间段标识：1早餐，2午餐，3晚餐，4零食")
    name: str = Field(..., description="时间段名称")
    
    model_config = {"populate_by_name": True}


class MealTimingResponse(BaseResponse[List[MealTiming]]):
    """
    用餐时间段列表响应
    
    API返回示例：
    {
        "code": 0,
        "message": null,
        "data": [
            {"code": 1, "name": "Breakfast"},
            {"code": 2, "name": "Lunch"},
            {"code": 3, "name": "Dinner"},
            {"code": 4, "name": "Snack"}
        ]
    }
    """
    pass


class NutritionSummary(BaseModel):
    """营养摄入汇总"""
    totalCalories: int = Field(..., description="总卡路里")
    totalCarbohydrate: int = Field(..., description="总碳水化合物(g)")
    totalProtein: int = Field(..., description="总蛋白质(g)")
    totalFat: int = Field(..., description="总脂肪(g)")
    
    model_config = {"populate_by_name": True}


class NutritionRecord(BaseModel):
    """
    营养记录
    
    表示单次用餐的营养摄入记录，对应API返回的单条记录结构
    """
    recordDate: str = Field(..., description="记录日期，格式：yyyy-MM-dd")
    timing: int = Field(..., description="用餐时间段：1早餐，2午餐，3晚餐，4零食")
    mealPhoto: Optional[str] = Field(None, description="用餐照片URL")
    calories: float = Field(..., description="本次用餐总卡路里")
    carbohydrate: Optional[float] = Field(None, description="碳水化合物(g)")
    protein: Optional[float] = Field(None, description="蛋白质(g)")
    fat: Optional[float] = Field(None, description="脂肪(g)")
    foods: List[FoodItem] = Field(..., description="食物列表")
    
    model_config = {"populate_by_name": True}


class NutritionDailyResponse(BaseResponse[Dict[str, List[NutritionRecord]]]):
    """
    营养日报响应
    
    API返回示例：
    {
        "code": 0,
        "message": null,
        "data": {
            "2025-06-23": [
                {
                    "recordDate": "2025-06-23",
                    "timing": 4,
                    "mealPhoto": "https://example.com/photo.jpg",
                    "calories": 95.0,
                    "carbohydrate": 25.0,
                    "protein": 0.0,
                    "fat": 0.0,
                    "foods": [
                        {
                            "code": "F000038",
                            "name": "苹果",
                            "servingNumber": 1,
                            "servingUnit": "个",
                            "calories": 95,
                            "carbohydrate": 25,
                            "protein": 0,
                            "fat": 0
                        }
                    ]
                }
            ],
            "2025-06-24": []
        }
    }
    
    说明：
    - 外层Dict的key为日期字符串(yyyy-MM-dd)，value为该日期的营养记录列表
    - 内层List的元素为NutritionRecord实体，提供完整的类型安全
    - 对于动态字段(日期)，我们保持Dict的灵活性，在业务层提供便利方法
    
    使用方式：
    - records = response.data["2025-06-23"]  # List[NutritionRecord]
    - record = records[0]                    # NutritionRecord
    - food_name = record.foods[0].name   # str (类型安全)
    """
    pass


# ==================== 营养记录请求模型 ====================

class NutritionClockFoodItem(BaseModel):
    """
    营养记录请求中的食物项目
    
    注意：请求和响应使用不同的字段名
    请求使用 foodName，响应使用 name
    """
    foodName: str = Field(..., description="食物名称")
    servingNumber: float = Field(..., description="份数")
    servingUnit: str = Field(..., description="单位")
    calories: float = Field(..., description="卡路里(kcal)")
    carbohydrate: float = Field(..., description="碳水化合物(g)")
    protein: float = Field(..., description="蛋白质(g)")
    fat: float = Field(..., description="脂肪(g)")
    
    model_config = {"populate_by_name": True}


class NutritionClockRequest(BaseModel):
    """营养记录请求"""
    userId: str = Field(..., description="用户ID")
    timing: str = Field(..., description="用餐时间：1早餐，2午餐，3晚餐，4零食")
    recordDate: str = Field(..., description="记录日期，格式：yyyy-MM-dd")
    mealPhoto: Optional[str] = Field(None, description="用餐照片URL")
    foods: List[NutritionClockFoodItem] = Field(..., description="食物列表")
    
    model_config = {"populate_by_name": True}


# ==================== 饮水记录模型 ====================

class HydrationClockRequest(BaseModel):
    """饮水记录请求"""
    userId: str = Field(..., description="用户ID")
    recordDate: str = Field(..., description="记录日期")
    completedValue: Optional[int] = Field(None, description="完成值（杯数）覆盖现有数据")
    clockValue: Optional[int] = Field(None, description="打卡值（杯数）累加现有数据")
    
    model_config = {"populate_by_name": True}


# ==================== 睡眠记录模型 ====================

class SleepClockRequest(BaseModel):
    """睡眠记录请求"""
    userId: str = Field(..., description="用户ID")
    recordDate: str = Field(..., description="记录日期")
    completedValue: int = Field(..., description="完成值（分钟）")
    
    model_config = {"populate_by_name": True}


# ==================== 运动相关模型 ====================

class ExerciseType(BaseModel):
    """
    运动类型
    
    表示系统支持的运动项目类型
    """
    id: int = Field(..., description="运动ID")
    name: str = Field(..., description="运动名称")
    icon: Optional[str] = Field(None, description="运动图标URL")
    trackRoute: Optional[str] = Field(None, description="是否跟踪路线")
    
    model_config = {"populate_by_name": True}


class ExerciseListResponse(BaseResponse[List[ExerciseType]]):
    """
    运动类型列表响应
    
    API返回示例：
    {
        "code": 0,
        "message": null,
        "data": [
            {
                "id": 1,
                "name": "跑步",
                "icon": "https://example.com/running.png",
                "trackRoute": "true"
            },
            {
                "id": 2, 
                "name": "游泳",
                "icon": "https://example.com/swimming.png",
                "trackRoute": "false"
            }
        ]
    }
    """
    pass


class ExerciseClockRequest(BaseModel):
    """运动记录请求"""
    userId: str = Field(..., description="用户ID")
    recordDate: str = Field(..., description="记录日期")
    taskId: int = Field(..., description="运动类型ID")
    duration: int = Field(..., description="运动时长(秒)")
    distance: Optional[int] = Field(None, description="运动距离(米)")
    avgHeartRate: Optional[int] = Field(None, description="平均心率(1-200/秒)")
    intensityLevel: Optional[str] = Field(None, description="运动强度(light/moderate/high)")
    
    model_config = {"populate_by_name": True}


class ExerciseRecord(BaseModel):
    """
    运动记录
    
    表示单次运动的详细记录信息，对应API返回的单条记录结构
    """
    name: str = Field(..., description="运动名称")  # API返回name字段，不是exerciseName
    icon: Optional[str] = Field(None, description="运动图标URL")
    duration: int = Field(..., description="运动时长(分钟)")
    calories: int = Field(..., description="消耗卡路里")
    distance: Optional[int] = Field(None, description="运动距离(米)")
    trackRoute: Optional[str] = Field(None, description="是否跟踪路线(Y/N)")
    intensityLevel: Optional[str] = Field(None, description="强度级别")
    
    model_config = {"populate_by_name": True}


class ExerciseDailyResponse(BaseResponse[Dict[str, List[ExerciseRecord]]]):
    """
    运动日报响应
    
    API返回示例：
    {
        "code": 0,
        "message": null,
        "data": {
            "2025-06-23": [
                {
                    "name": "Cycling",
                    "icon": "https://healthresource-**********.file.myqcloud.com/...",
                    "distance": 5000,
                    "duration": 300,
                    "calories": 31250,
                    "trackRoute": "Y",
                    "intensityLevel": "moderate"
                }
            ],
            "2025-06-24": []
        }
    }
    
    说明：
    - 外层Dict的key为日期字符串(yyyy-MM-dd)，value为该日期的运动记录列表
    - 内层List的元素为ExerciseRecord实体，提供完整的类型安全
    - 保持结构清晰和灵活性的平衡
    
    使用方式：
    - records = response.data["2025-06-23"]  # List[ExerciseRecord]
    - record = records[0]                    # ExerciseRecord
    - exercise_name = record.name    # str (类型安全)
    """
    pass


# ==================== 任务目标相关模型 ====================

class TaskGoal(BaseModel):
    """
    任务目标
    
    表示用户设置的健康目标项目
    """
    goalCode: str = Field(..., description="目标代码")
    title: Optional[str] = Field(None, description="目标标题")
    icon: Optional[str] = Field(None, description="目标图标")
    goal: Optional['Goal'] = Field(None, description="目标详情")
    completedValue: Optional[int] = Field(None, description="完成值")
    completedPct: Optional[float] = Field(None, description="完成百分比")
    
    model_config = {"populate_by_name": True}


class Goal(BaseModel):
    """
    目标详情
    
    表示具体的目标设置信息
    """
    goalValue: Optional[int] = Field(None, description="目标值")
    goalUnit: Optional[str] = Field(None, description="目标单位")
    goalMinValue: Optional[int] = Field(None, description="目标最小值")
    goalMaxValue: Optional[int] = Field(None, description="目标最大值")
    
    model_config = {"populate_by_name": True}


class TaskCategory(BaseModel):
    """
    任务类别
    
    表示健康任务的分类（如营养、运动、睡眠等）
    """
    icon: Optional[str] = Field(None, description="图标URL")  # API返回可能为None
    title: str = Field(..., description="类别标题")
    type: str = Field(..., description="类别类型")
    phaseGoal: bool = Field(..., description="是否是阶段目标")
    goals: List[TaskGoal] = Field(..., description="具体目标列表")
    
    model_config = {"populate_by_name": True}



class DailyDetailResponse(BaseResponse[Dict[str, List[TaskCategory]]]):
    """
    日常任务详情响应
    
    根据Java代码和实际API调用，正确的数据结构应该是：
    {
        "code": 0,
        "message": null,
        "data": {
            "2025-06-23": [
                {
                    "icon": "https://example.com/nutrition.png",
                    "title": "营养目标",
                    "type": "nutrition",
                    "phaseGoal": false,
                    "goals": [
                        {
                            "goalCode": "DAILY_CALORIES",
                            "title": "每日卡路里",
                            "goal": {
                                "goalValue": 2000,
                                "goalUnit": "kcal"
                            },
                            "completedValue": 1500,
                            "completedPct": 75.0
                        }
                    ]
                }
            ]
        }
    }
    
    说明：
    - 外层Dict的key为日期字符串(yyyy-MM-dd)，value为该日期的任务类别列表
    - 直接是List[TaskCategory]，而不是包含categories字段的对象
    - 与Java代码中的 Map<String, List<TaskCategory>> data 结构一致
    
    使用方式：
    - categories = response.data["2025-06-23"]  # List[TaskCategory]
    - category = categories[0]                   # TaskCategory
    - goal_title = category.goals[0].title       # str (类型安全)
    """
    pass


# ==================== 通用响应模型 ====================

class ClockResponse(BooleanResponse):
    """
    记录操作响应
    
    用于所有POST记录接口的统一响应格式：
    {
        "code": 0,
        "message": null,
        "data": true  // 布尔值，表示操作成功或失败
    }
    
    适用接口：
    - nutrition-clock (营养记录)
    - hydration-clock (饮水记录)
    - sleep-clock (睡眠记录)
    - exercise-clock (运动记录)
    """
    pass


# ==================== 响应类型别名 ====================

# 为提高代码可读性，定义具体的响应类型别名
NutritionClockResponse = ClockResponse
HydrationClockResponse = ClockResponse  
SleepClockResponse = ClockResponse
ExerciseClockResponse = ClockResponse

# 更新模型，确保前向引用能正常工作
TaskGoal.model_rebuild()