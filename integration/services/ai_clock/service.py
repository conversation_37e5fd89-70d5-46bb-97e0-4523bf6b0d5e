# service.py - AI Clock服务业务封装
#
# @author: shaohua.sun
# @date: 2025-01-27
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD

"""
AI Clock服务业务封装

提供AI Clock服务相关的高级业务接口封装，包括数据验证、类型转换等。
对接evyd-health-manage-routines服务。
"""

from typing import Dict, Any, Optional, List, Iterator, Tuple
from datetime import datetime, timedelta
from ...core.exceptions import IntegrationError
from utils.biz_logger import get_logger
from .client import AiClockClient
from .models import (
    DateRangeRequest, NutritionRecord, ExerciseRecord, 
    TaskCategory, TaskGoal, FoodItem,
    NutritionDailyResponse, ExerciseDailyResponse, DailyDetailResponse
)

logger = get_logger(__name__)


class AiClockService:
    """AI Clock服务业务封装类"""
    
    def __init__(self, client: Optional[AiClockClient] = None):
        """
        初始化AI Clock服务
        
        Args:
            client: 可选的AiClockClient实例，用于依赖注入。
                   如果为None，则创建默认客户端实例。
        """
        # 支持客户端依赖注入，保持向下兼容
        if client is not None:
            self.client = client
            logger.info("AI Clock服务初始化完成（使用注入的客户端）")
        else:
            self.client = AiClockClient()
            logger.info("AI Clock服务初始化完成")
    
    def get_meal_timing(self) -> Optional[Dict[str, Any]]:
        """
        获取用餐时间段列表
        
        Returns:
            用餐时间段数据，如果调用失败返回None
            
        Raises:
            IntegrationError: 服务调用失败时抛出
        """
        try:
            logger.info("获取用餐时间段列表")
            response = self.client.get_meal_timing()
            
            # 判断响应状态，直接返回data字段
            if hasattr(response, 'code') and response.code == 0:
                return response.data if hasattr(response, 'data') else None
            elif isinstance(response, dict) and response.get('code') == 0:
                return response.get('data')
            else:
                # 修复：正确处理响应对象的 code 属性访问
                if hasattr(response, 'code'):
                    code = response.code
                elif isinstance(response, dict):
                    code = response.get('code', 'unknown')
                else:
                    code = 'unknown'
                logger.warning(f"获取用餐时间段列表失败，code: {code}")
                return None
            
        except Exception as e:
            logger.error(f"获取用餐时间段列表失败: {e}")
            raise IntegrationError(f"获取用餐时间段列表失败: {str(e)}")

    # === 营养相关接口 ===
    
    def get_nutrition_daily(self, userId: str, startDate: str, endDate: str) -> Optional[Dict[str, List[NutritionRecord]]]:
        """
        获取营养日报
        
        Args:
            userId: 用户ID
            startDate: 开始日期，格式：yyyy-MM-dd
            endDate: 结束日期，格式：yyyy-MM-dd
            
        Returns:
            营养日报数据字典，如果调用失败返回None
        """
        try:
            logger.info(f"获取营养日报: userId={userId}, startDate={startDate}, endDate={endDate}")
            
            if not userId:
                raise ValueError("用户ID不能为空")
            if not startDate:
                raise ValueError("开始日期不能为空")
            if not endDate:
                raise ValueError("结束日期不能为空")
            
            # 创建请求模型
            request = DateRangeRequest(
                startDate=startDate,
                endDate=endDate,
                userId=userId
            )
            
            response = self.client.get_nutrition_daily(request)
            
            # 判断响应状态，直接返回data字段
            if hasattr(response, 'code') and response.code == 0:
                return response.data if hasattr(response, 'data') else None
            elif isinstance(response, dict) and response.get('code') == 0:
                return response.get('data')
            else:
                # 修复：正确处理响应对象的 code 属性访问
                if hasattr(response, 'code'):
                    code = response.code
                elif isinstance(response, dict):
                    code = response.get('code', 'unknown')
                else:
                    code = 'unknown'
                logger.warning(f"获取营养日报失败，code: {code}")
                return None
            
        except Exception as e:
            logger.error(f"获取营养日报失败: {e}")
            raise IntegrationError(f"获取营养日报失败: {str(e)}")
    
    # === 便利方法接口（可选，基于核心接口构建） ===
    
    def get_nutrition_records_by_date(self, userId: str, date: str) -> List[NutritionRecord]:
        """
        获取指定日期的营养记录（便利方法）
        
        Args:
            userId: 用户ID
            date: 日期，格式：yyyy-MM-dd
            
        Returns:
            营养记录列表
        """
        nutrition_data = self.get_nutrition_daily(userId, date, date)
        if nutrition_data and date in nutrition_data:
            return nutrition_data[date]
        return []
    
    def get_nutrition_records_range(self, userId: str, startDate: str, endDate: str) -> Dict[str, List[NutritionRecord]]:
        """
        获取日期范围内的营养记录（便利方法）
        
        Args:
            userId: 用户ID
            startDate: 开始日期，格式：yyyy-MM-dd
            endDate: 结束日期，格式：yyyy-MM-dd
            
        Returns:
            按日期分组的营养记录字典
        """
        nutrition_data = self.get_nutrition_daily(userId, startDate, endDate)
        return nutrition_data if nutrition_data else {}
    
    def iter_nutrition_records(self, userId: str, startDate: str, endDate: str) -> Iterator[Tuple[str, NutritionRecord]]:
        """
        迭代访问日期范围内的营养记录
        
        Args:
            userId: 用户ID
            startDate: 开始日期，格式：yyyy-MM-dd
            endDate: 结束日期，格式：yyyy-MM-dd
            
        Yields:
            (日期, 营养记录) 元组
        """
        records_dict = self.get_nutrition_records_range(userId, startDate, endDate)
        for date, records in records_dict.items():
            for record in records:
                yield date, record
    
    def get_total_calories_by_date(self, userId: str, date: str) -> float:
        """
        计算指定日期的总摄入卡路里
        
        Args:
            userId: 用户ID
            date: 日期，格式：yyyy-MM-dd
            
        Returns:
            总卡路里数
        """
        records = self.get_nutrition_records_by_date(userId, date)
        total_calories = 0.0
        
        for record in records:
            for food in record.foods:
                total_calories += food.calorie or 0.0
        
        return total_calories
    
    def get_foods_by_date(self, userId: str, date: str) -> List[FoodItem]:
        """
        获取指定日期的所有食物条目
        
        Args:
            userId: 用户ID
            date: 日期，格式：yyyy-MM-dd
            
        Returns:
            食物条目列表
        """
        records = self.get_nutrition_records_by_date(userId, date)
        all_foods = []
        
        for record in records:
            all_foods.extend(record.foods)
        
        return all_foods
    
    def record_nutrition(self, nutritionData: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        记录用户营养摄入信息
        
        Args:
            nutritionData: 营养记录数据
            
        Returns:
            营养记录响应数据，如果调用失败返回None
        """
        try:
            logger.info(f"记录用户营养摄入信息: userId={nutritionData.get('userId')}")
            
            if not nutritionData.get('userId'):
                raise ValueError("用户ID不能为空")
            if not nutritionData.get('recordDate'):
                raise ValueError("记录日期不能为空")
            
            response = self.client.nutrition_clock(nutritionData)
            
            # 判断响应状态，直接返回data字段
            if hasattr(response, 'code') and response.code == 0:
                return response.data if hasattr(response, 'data') else None
            elif isinstance(response, dict) and response.get('code') == 0:
                return response.get('data')
            else:
                # 修复：正确处理响应对象的 code 属性访问
                if hasattr(response, 'code'):
                    code = response.code
                elif isinstance(response, dict):
                    code = response.get('code', 'unknown')
                else:
                    code = 'unknown'
                logger.warning(f"记录营养摄入失败，code: {code}")
                return None
            
        except Exception as e:
            logger.error(f"记录用户营养摄入信息失败: {e}")
            raise IntegrationError(f"记录用户营养摄入信息失败: {str(e)}")

    # === 运动相关接口 ===
    
    def get_exercise_daily(self, userId: str, startDate: str, endDate: str) -> Optional[Dict[str, List[ExerciseRecord]]]:
        """
        获取运动日报
        
        Args:
            userId: 用户ID
            startDate: 开始日期，格式：yyyy-MM-dd
            endDate: 结束日期，格式：yyyy-MM-dd
            
        Returns:
            运动日报数据字典，如果调用失败返回None
        """
        try:
            logger.info(f"获取运动日报: userId={userId}, startDate={startDate}, endDate={endDate}")
            
            if not userId:
                raise ValueError("用户ID不能为空")
            if not startDate:
                raise ValueError("开始日期不能为空")
            if not endDate:
                raise ValueError("结束日期不能为空")
            
            # 创建请求模型
            request = DateRangeRequest(
                startDate=startDate,
                endDate=endDate,
                userId=userId
            )
            
            response = self.client.get_exercise_daily(request)
            
            # 判断响应状态，直接返回data字段
            if hasattr(response, 'code') and response.code == 0:
                return response.data if hasattr(response, 'data') else None
            elif isinstance(response, dict) and response.get('code') == 0:
                return response.get('data')
            else:
                # 修复：正确处理响应对象的 code 属性访问
                if hasattr(response, 'code'):
                    code = response.code
                elif isinstance(response, dict):
                    code = response.get('code', 'unknown')
                else:
                    code = 'unknown'
                logger.warning(f"获取运动日报失败，code: {code}")
                return None
            
        except Exception as e:
            logger.error(f"获取运动日报失败: {e}")
            raise IntegrationError(f"获取运动日报失败: {str(e)}")
    
    def get_exercise_records_by_date(self, userId: str, date: str) -> List[ExerciseRecord]:
        """
        获取指定日期的运动记录（便利方法）
        
        Args:
            userId: 用户ID
            date: 日期，格式：yyyy-MM-dd
            
        Returns:
            运动记录列表
        """
        exercise_data = self.get_exercise_daily(userId, date, date)
        if exercise_data and date in exercise_data:
            return exercise_data[date]
        return []
    
    def get_exercise_records_range(self, userId: str, startDate: str, endDate: str) -> Dict[str, List[ExerciseRecord]]:
        """
        获取日期范围内的运动记录（便利方法）
        
        Args:
            userId: 用户ID
            startDate: 开始日期，格式：yyyy-MM-dd
            endDate: 结束日期，格式：yyyy-MM-dd
            
        Returns:
            按日期分组的运动记录字典
        """
        exercise_data = self.get_exercise_daily(userId, startDate, endDate)
        return exercise_data if exercise_data else {}
    
    def get_total_exercise_calories_by_date(self, userId: str, date: str) -> int:
        """
        计算指定日期的总运动消耗卡路里
        
        Args:
            userId: 用户ID
            date: 日期，格式：yyyy-MM-dd
            
        Returns:
            总消耗卡路里数
        """
        records = self.get_exercise_records_by_date(userId, date)
        total_calories = 0
        
        for record in records:
            total_calories += record.burnedCalorie or 0
        
        return total_calories
    
    def get_total_exercise_duration_by_date(self, userId: str, date: str) -> int:
        """
        计算指定日期的总运动时长
        
        Args:
            userId: 用户ID
            date: 日期，格式：yyyy-MM-dd
            
        Returns:
            总运动时长（分钟）
        """
        records = self.get_exercise_records_by_date(userId, date)
        total_duration = 0
        
        for record in records:
            total_duration += record.duration or 0
        
        return total_duration
    
    def get_all_exercise_list(self) -> Optional[Dict[str, Any]]:
        """
        获取所有运动类型列表
        
        Returns:
            运动类型列表数据，如果调用失败返回None
        """
        try:
            logger.info("获取所有运动类型列表")
            response = self.client.get_all_exercise_list()
            
            # 判断响应状态，直接返回data字段
            if hasattr(response, 'code') and response.code == 0:
                return response.data if hasattr(response, 'data') else None
            elif isinstance(response, dict) and response.get('code') == 0:
                return response.get('data')
            else:
                # 修复：正确处理响应对象的 code 属性访问
                if hasattr(response, 'code'):
                    code = response.code
                elif isinstance(response, dict):
                    code = response.get('code', 'unknown')
                else:
                    code = 'unknown'
                logger.warning(f"获取运动类型列表失败，code: {code}")
                return None
            
        except Exception as e:
            logger.error(f"获取所有运动类型列表失败: {e}")
            raise IntegrationError(f"获取所有运动类型列表失败: {str(e)}")
    
    def record_exercise(self, exerciseData: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        记录用户运动信息
        
        Args:
            exerciseData: 运动记录数据
            
        Returns:
            运动记录响应数据，如果调用失败返回None
        """
        try:
            logger.info(f"记录用户运动信息: userId={exerciseData.get('userId')}")
            
            if not exerciseData.get('userId'):
                raise ValueError("用户ID不能为空")
            if not exerciseData.get('recordDate'):
                raise ValueError("记录日期不能为空")
            
            response = self.client.exercise_clock(exerciseData)
            
            # 判断响应状态，直接返回data字段
            if hasattr(response, 'code') and response.code == 0:
                return response.data if hasattr(response, 'data') else None
            elif isinstance(response, dict) and response.get('code') == 0:
                return response.get('data')
            else:
                # 修复：正确处理响应对象的 code 属性访问
                if hasattr(response, 'code'):
                    code = response.code
                elif isinstance(response, dict):
                    code = response.get('code', 'unknown')
                else:
                    code = 'unknown'
                logger.warning(f"记录运动信息失败，code: {code}")
                return None
            
        except Exception as e:
            logger.error(f"记录用户运动信息失败: {e}")
            raise IntegrationError(f"记录用户运动信息失败: {str(e)}")

    # === 每日任务详情相关接口 ===
    
    def get_daily_detail(self, userId: str, startDate: str, endDate: str) -> Optional[Dict[str, List[TaskCategory]]]:
        """
        获取每日任务详情
        
        Args:
            userId: 用户ID
            startDate: 开始日期，格式：yyyy-MM-dd
            endDate: 结束日期，格式：yyyy-MM-dd
            
        Returns:
            每日任务详情数据字典，key为日期，value为任务类别列表，如果调用失败返回None
        """
        try:
            logger.info(f"获取每日任务详情: userId={userId}, startDate={startDate}, endDate={endDate}")
            
            if not userId:
                raise ValueError("用户ID不能为空")
            if not startDate:
                raise ValueError("开始日期不能为空")
            if not endDate:
                raise ValueError("结束日期不能为空")
            
            # 创建请求模型
            request = DateRangeRequest(
                startDate=startDate,
                endDate=endDate,
                userId=userId
            )
            
            response = self.client.get_daily_detail(request)
            
            # 判断响应状态，直接返回data字段
            if hasattr(response, 'code') and response.code == 0:
                return response.data if hasattr(response, 'data') else None
            elif isinstance(response, dict) and response.get('code') == 0:
                return response.get('data')
            else:
                # 修复：正确处理 DailyDetailResponse 对象的 code 属性访问
                if hasattr(response, 'code'):
                    code = response.code
                elif isinstance(response, dict):
                    code = response.get('code', 'unknown')
                else:
                    code = 'unknown'
                logger.warning(f"获取每日任务详情失败，code: {code}")
                return None
            
        except Exception as e:
            logger.error(f"获取每日任务详情失败: {e}")
            raise IntegrationError(f"获取每日任务详情失败: {str(e)}")
    
    def get_daily_task_detail_by_date(self, userId: str, date: str) -> Optional[List[TaskCategory]]:
        """
        获取指定日期的每日任务详情
        
        Args:
            userId: 用户ID
            date: 日期，格式：yyyy-MM-dd
            
        Returns:
            每日任务类别列表，如果没有则返回None
        """
        daily_data = self.get_daily_detail(userId, date, date)
        if daily_data and date in daily_data:
            return daily_data[date]
        return None
    
    def get_task_categories_by_date(self, userId: str, date: str) -> List[TaskCategory]:
        """
        获取指定日期的任务类别列表
        
        Args:
            userId: 用户ID
            date: 日期，格式：yyyy-MM-dd
            
        Returns:
            任务类别列表
        """
        daily_detail = self.get_daily_task_detail_by_date(userId, date)
        return daily_detail if daily_detail else []
    
    def get_task_goals_by_date_and_type(self, userId: str, date: str, taskType: str) -> List[TaskGoal]:
        """
        获取指定日期和类型的任务目标列表
        
        Args:
            userId: 用户ID
            date: 日期，格式：yyyy-MM-dd
            taskType: 任务类型
            
        Returns:
            任务目标列表
        """
        task_categories = self.get_task_categories_by_date(userId, date)
        
        for category in task_categories:
            if category.type == taskType:
                return category.goals or []
        
        return []
    
    def get_completed_goals_by_date(self, userId: str, date: str) -> List[TaskGoal]:
        """
        获取指定日期的已完成任务目标列表
        
        Args:
            userId: 用户ID
            date: 日期，格式：yyyy-MM-dd
            
        Returns:
            已完成任务目标列表
        """
        task_categories = self.get_task_categories_by_date(userId, date)
        completed_goals = []
        
        for category in task_categories:
            if category.goals:
                for goal in category.goals:
                    if goal.completedValue and goal.completedValue > 0:
                        completed_goals.append(goal)
        
        return completed_goals

    # === 其他记录接口 ===
    
    def record_hydration(self, hydrationData: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        记录用户饮水信息
        
        Args:
            hydrationData: 饮水记录数据，包含userId, recordDate, clockValue等
            
        Returns:
            饮水记录响应数据，如果调用失败返回None
        """
        try:
            logger.info(f"记录用户饮水信息: userId={hydrationData.get('userId')}")
            
            if not hydrationData.get('userId'):
                raise ValueError("用户ID不能为空")
            if not hydrationData.get('recordDate'):
                raise ValueError("记录日期不能为空")
            
            response = self.client.hydration_clock(hydrationData)
            
            # 判断响应状态，直接返回data字段
            if hasattr(response, 'code') and response.code == 0:
                return response.data if hasattr(response, 'data') else None
            elif isinstance(response, dict) and response.get('code') == 0:
                return response.get('data')
            else:
                # 修复：正确处理响应对象的 code 属性访问
                if hasattr(response, 'code'):
                    code = response.code
                elif isinstance(response, dict):
                    code = response.get('code', 'unknown')
                else:
                    code = 'unknown'
                logger.warning(f"记录饮水信息失败，code: {code}")
                return None
            
        except Exception as e:
            logger.error(f"记录用户饮水信息失败: {e}")
            raise IntegrationError(f"记录用户饮水信息失败: {str(e)}")
    
    def record_sleep(self, sleepData: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        记录用户睡眠信息
        
        Args:
            sleepData: 睡眠记录数据，包含userId, recordDate, completedValue等
            
        Returns:
            睡眠记录响应数据，如果调用失败返回None
        """
        try:
            logger.info(f"记录用户睡眠信息: userId={sleepData.get('userId')}")
            
            if not sleepData.get('userId'):
                raise ValueError("用户ID不能为空")
            if not sleepData.get('recordDate'):
                raise ValueError("记录日期不能为空")
            
            response = self.client.sleep_clock(sleepData)
            
            # 判断响应状态，直接返回data字段
            if hasattr(response, 'code') and response.code == 0:
                return response.data if hasattr(response, 'data') else None
            elif isinstance(response, dict) and response.get('code') == 0:
                return response.get('data')
            else:
                # 修复：正确处理响应对象的 code 属性访问
                if hasattr(response, 'code'):
                    code = response.code
                elif isinstance(response, dict):
                    code = response.get('code', 'unknown')
                else:
                    code = 'unknown'
                logger.warning(f"记录睡眠信息失败，code: {code}")
                return None
            
        except Exception as e:
            logger.error(f"记录用户睡眠信息失败: {e}")
            raise IntegrationError(f"记录用户睡眠信息失败: {str(e)}")
    
    # === 汇总统计接口 ===
    
    def get_weekly_nutrition_summary(self, userId: str, endDate: Optional[str] = None) -> Dict[str, float]:
        """
        获取一周营养摄入汇总
        
        Args:
            userId: 用户ID
            endDate: 结束日期，默认为今天
            
        Returns:
            营养摄入汇总数据，包含totalCalories, avgCaloriesPerDay等
        """
        if not endDate:
            endDate = datetime.now().strftime('%Y-%m-%d')
        
        # 计算开始日期（7天前）
        end_dt = datetime.strptime(endDate, '%Y-%m-%d')
        start_dt = end_dt - timedelta(days=6)
        startDate = start_dt.strftime('%Y-%m-%d')
        
        records_dict = self.get_nutrition_records_range(userId, startDate, endDate)
        
        total_calories = 0.0
        total_carbs = 0.0
        total_protein = 0.0
        total_fat = 0.0
        day_count = 0
        
        for date, records in records_dict.items():
            day_calories = 0.0
            day_carbs = 0.0
            day_protein = 0.0
            day_fat = 0.0
            
            for record in records:
                for food in record.foods:
                    day_calories += food.calorie or 0.0
                    day_carbs += food.carbohydrate or 0.0
                    day_protein += food.protein or 0.0
                    day_fat += food.fat or 0.0
            
            if day_calories > 0:
                total_calories += day_calories
                total_carbs += day_carbs
                total_protein += day_protein
                total_fat += day_fat
                day_count += 1
        
        return {
            'totalCalories': total_calories,
            'totalCarbs': total_carbs,
            'totalProtein': total_protein,
            'totalFat': total_fat,
            'avgCaloriesPerDay': total_calories / max(day_count, 1),
            'avgCarbsPerDay': total_carbs / max(day_count, 1),
            'avgProteinPerDay': total_protein / max(day_count, 1),
            'avgFatPerDay': total_fat / max(day_count, 1),
            'daysWithRecords': day_count,
            'startDate': startDate,
            'endDate': endDate
        }
    
    def get_weekly_exercise_summary(self, userId: str, endDate: Optional[str] = None) -> Dict[str, int]:
        """
        获取一周运动消耗汇总
        
        Args:
            userId: 用户ID
            endDate: 结束日期，默认为今天
            
        Returns:
            运动消耗汇总数据，包含totalCalories, totalDuration, avgCaloriesPerDay等
        """
        if not endDate:
            endDate = datetime.now().strftime('%Y-%m-%d')
        
        # 计算开始日期（7天前）
        end_dt = datetime.strptime(endDate, '%Y-%m-%d')
        start_dt = end_dt - timedelta(days=6)
        startDate = start_dt.strftime('%Y-%m-%d')
        
        records_dict = self.get_exercise_records_range(userId, startDate, endDate)
        
        total_calories = 0
        total_duration = 0
        day_count = 0
        
        for date, records in records_dict.items():
            day_calories = 0
            day_duration = 0
            
            for record in records:
                day_calories += record.burnedCalorie or 0
                day_duration += record.duration or 0
            
            if day_calories > 0 or day_duration > 0:
                total_calories += day_calories
                total_duration += day_duration
                day_count += 1
        
        return {
            'totalCalories': total_calories,
            'totalDuration': total_duration,
            'avgCaloriesPerDay': total_calories // max(day_count, 1),
            'avgDurationPerDay': total_duration // max(day_count, 1),
            'daysWithRecords': day_count,
            'startDate': startDate,
            'endDate': endDate
        } 