# client.py - AI时钟服务Feign客户端
#
# @author: shaohua.sun
# @date: 2025-01-27
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD

from typing import Dict, Any, Optional, List
from ...core.feign_client import FeignClient, get_mapping, post_mapping
from ...config.integration_config import ServiceConfig, HttpConfig
from .models import (
    # 请求模型
    DateRangeRequest, NutritionClockRequest, HydrationClockRequest, 
    SleepClockRequest, ExerciseClockRequest,
    # 响应模型
    MealTimingResponse, NutritionDailyResponse, ExerciseListResponse,
    ExerciseDailyResponse, DailyDetailResponse,
    NutritionClockResponse, HydrationClockResponse, 
    SleepClockResponse, ExerciseClockResponse
)



class AiClockClient(FeignClient):
    """AI时钟服务Feign客户端"""
    
    def __init__(
        self, 
        service_config: Optional[ServiceConfig] = None, 
        http_config: Optional[HttpConfig] = None,
        request_interceptors: Optional[List] = None,
        response_interceptors: Optional[List] = None
    ):
        """
        初始化AI时钟服务客户端
        
        Args:
            service_config: 服务配置，为None时使用硬编码的默认配置
            http_config: HTTP配置，为None时使用硬编码的默认配置
            request_interceptors: 初始请求拦截器列表
            response_interceptors: 初始响应拦截器列表
        """
        # 硬编码的AI Clock服务配置
        if service_config is None:
            service_config = ServiceConfig(
                base_url="http://evyd-health-manage-routines",
                api_key=None,
                headers={
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                enabled=True
            )
        
        # 硬编码的HTTP配置
        if http_config is None:
            http_config = HttpConfig(
                connect_timeout=10,
                read_timeout=30,
                max_retries=3,
                retry_backoff_factor=0.3,
                enable_compression=True,
                pool_connections=10,
                pool_maxsize=50
            )
        
        super().__init__(
            "ai_clock", 
            service_config, 
            http_config,
            request_interceptors,
            response_interceptors
        )
    
    @get_mapping("/internal/ai-clock/meal-timing")
    def get_meal_timing(self) -> MealTimingResponse:
        """获取用餐时间段列表"""
        pass
    
    @get_mapping("/internal/ai-clock/nutrition-daily")
    def get_nutrition_daily(self, request: DateRangeRequest) -> NutritionDailyResponse:
        """获取用户营养日报"""
        pass
    
    @post_mapping("/internal/ai-clock/nutrition-clock")
    def nutrition_clock(self, request: NutritionClockRequest) -> NutritionClockResponse:
        """记录用户营养摄入信息"""
        pass
    
    @post_mapping("/internal/ai-clock/hydration-clock")
    def hydration_clock(self, request: HydrationClockRequest) -> HydrationClockResponse:
        """记录用户饮水信息"""
        pass
    
    @post_mapping("/internal/ai-clock/sleep-clock")
    def sleep_clock(self, request: SleepClockRequest) -> SleepClockResponse:
        """记录用户睡眠信息"""
        pass
    
    @get_mapping("/internal/ai-clock/exercise-daily")
    def get_exercise_daily(self, request: DateRangeRequest) -> ExerciseDailyResponse:
        """获取用户运动日报"""
        pass
    
    @get_mapping("/internal/ai-clock/all-exercise-list")
    def get_all_exercise_list(self) -> ExerciseListResponse:
        """获取所有运动类型列表"""
        pass
    
    @post_mapping("/internal/ai-clock/exercise-clock")
    def exercise_clock(self, request: ExerciseClockRequest) -> ExerciseClockResponse:
        """记录用户运动信息"""
        pass
    
    @get_mapping("/internal/ai-clock/daily-detail")
    def get_daily_detail(self, request: DateRangeRequest) -> DailyDetailResponse:
        """获取用户日常任务详情"""
        pass 