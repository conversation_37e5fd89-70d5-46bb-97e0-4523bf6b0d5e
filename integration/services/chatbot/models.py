# models.py - Chatbot服务模型定义
#
# @author: shaohua.sun
# @date: 2025-01-27  
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD

"""
Chatbot服务模型定义

本模块定义了Chatbot服务所有接口的请求和响应模型。
主要包含文件服务相关的数据结构。
设计原则：
1. 统一使用驼峰命名，与API规范一致
2. 明确的字段定义，避免过度抽象
3. 保持模型简洁性和可读性
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from ...core.models import BaseResponse


# ==================== 文件服务请求模型 ====================

class FilePreviewRequest(BaseModel):
    """文件预览信息请求"""
    bizUserId: str = Field(..., description="业务用户ID")
    ids: List[str] = Field(..., description="文件ID列表")
    
    model_config = {"populate_by_name": True}


class FileUploadInfoRequest(BaseModel):
    """文件上传信息请求"""
    bizUserId: str = Field(..., description="业务用户ID")
    fileName: str = Field(..., description="文件名")
    contentType: str = Field(..., description="文件类型")
    length: int = Field(..., description="文件大小")
    
    model_config = {"populate_by_name": True}


# ==================== 文件服务响应模型 ====================

class FileInfo(BaseModel):
    """文件信息"""
    id: str = Field(..., description="文件ID")
    path: str = Field(..., description="文件路径/URL")
    
    model_config = {"populate_by_name": True}


class FileDownloadInfo(BaseModel):
    """文件下载信息"""
    fileName: str = Field(..., description="文件名")
    contentType: str = Field(..., description="文件类型")
    size: int = Field(..., description="文件大小")
    url: str = Field(..., description="下载URL")
    
    model_config = {"populate_by_name": True}


class FileUploadInfo(BaseModel):
    """文件上传信息"""
    id: str = Field(..., description="文件ID")
    path: str = Field(..., description="上传路径")
    
    model_config = {"populate_by_name": True}


# ==================== 响应封装模型 ====================

class FilePreviewResponse(BaseResponse[List[FileInfo]]):
    """
    文件预览信息响应
    
    API返回示例：
    {
        "code": 0,
        "message": "success",
        "data": [
            {"id": "file123", "path": "https://example.com/file/url"}
        ]
    }
    """
    pass


class FileDownloadResponse(BaseResponse[FileDownloadInfo]):
    """
    文件下载信息响应
    
    API返回示例：
    {
        "code": 0,
        "message": "success",
        "data": {
            "fileName": "image.jpg",
            "contentType": "image/jpeg",
            "size": 12345,
            "url": "https://download.url"
        }
    }
    """
    pass


class FileUploadInfoResponse(BaseResponse[FileUploadInfo]):
    """
    文件上传信息响应
    
    API返回示例：
    {
        "code": 0,
        "message": "success",
        "data": {
            "id": "file123",
            "path": "/upload/path"
        }
    }
    """
    pass


# ==================== 用户信息相关模型 ====================

class BaseInfo(BaseModel):
    """用户基础信息"""
    bizId: str = Field(..., description="业务ID")
    thirdUserId: str = Field(..., description="第三方用户ID")
    username: Optional[str] = Field(None, description="用户名")
    gender: str = Field(..., description="性别")
    birthday: str = Field(..., description="生日")
    nickName: Optional[str] = Field(None, description="昵称")
    memberIdList: Optional[List[str]] = Field(None, description="会员ID列表")
    
    model_config = {"populate_by_name": True}


class RawData(BaseModel):
    """原始数据"""
    key1: str = Field(..., description="原始数据键1")
    key2: str = Field(..., description="原始数据键2")
    
    model_config = {"populate_by_name": True}


class DataHub(BaseModel):
    """数据中心信息"""
    bizId: str = Field(..., description="业务ID")
    source: str = Field(..., description="数据源")
    category: str = Field(..., description="数据分类")
    tags: List[str] = Field(..., description="标签列表")
    rawData: RawData = Field(..., description="原始数据")
    
    model_config = {"populate_by_name": True}


class UserInfoData(BaseModel):
    """用户信息数据"""
    baseInfo: BaseInfo = Field(..., description="基础信息")
    dataHub: Optional[DataHub] = Field(None, description="数据中心信息（可选）")
    
    model_config = {"populate_by_name": True}


class UserInfoResponse(BaseResponse[UserInfoData]):
    """
    用户信息响应
    
    API返回示例：
    {
        "code": 0,
        "message": "success", 
        "data": {
            "baseInfo": {...},
            "dataHub": {...}
        }
    }
    """
    pass


# ==================== 患者数据管理相关模型 ====================

# 指标数据查询相关模型
class IndicationQueryQo(BaseModel):
    """指标查询请求对象"""
    userId: str = Field(..., description="患者userId，唯一标识患者")
    keys: List[str] = Field(..., description="指标key列表，指定要查询的指标类型")
    
    model_config = {"populate_by_name": True}


class IndicationDTO(BaseModel):
    """指标数据对象"""
    key: str = Field(..., description="指标key，标识指标类型")
    value: str = Field(..., description="指标值")
    valueType: str = Field(..., description="值类型")
    extData: str = Field(..., description="扩展数据，JSON字符串格式")
    recordDateTime: str = Field(..., description="记录时间")
    
    model_config = {"populate_by_name": True}


class IndicationRecordVo(BaseModel):
    """指标记录对象"""
    id: int = Field(..., description="记录ID，自动生成的序号")
    indicationDTO: IndicationDTO = Field(..., description="指标数据对象")
    
    model_config = {"populate_by_name": True}


class IndicationQueryVo(BaseModel):
    """指标查询响应对象"""
    total: int = Field(..., description="总记录数")
    list: List[IndicationRecordVo] = Field(..., description="指标记录列表")
    
    model_config = {"populate_by_name": True}


class IndicationQueryResponse(BaseResponse[IndicationQueryVo]):
    """
    指标查询响应
    
    API返回示例：
    {
        "code": 0,
        "message": "success",
        "data": {
            "total": 3,
            "list": [
                {
                    "id": 1,
                    "indicationDTO": {
                        "key": "blood_pressure",
                        "value": "120/80",
                        "valueType": "string",
                        "extData": "{\"unit\":\"mmHg\"}",
                        "recordDateTime": "2025-07-30 10:30:00"
                    }
                }
            ]
        }
    }
    """
    pass


# 患者记录查询相关模型
class SqlQueryRequest(BaseModel):
    """SQL查询请求对象"""
    sql: str = Field(..., description="SQL查询语句，用于在OLAP数据库中执行查询")
    
    model_config = {"populate_by_name": True}


class PatientRecordQueryQo(BaseModel):
    """患者记录查询请求对象"""
    sqlVal: str = Field(..., description="SQL查询语句")
    
    model_config = {"populate_by_name": True}


class PatientRecordQueryVo(BaseModel):
    """患者记录查询响应对象"""
    code: int = Field(..., description="OLAP服务响应状态码，200表示成功")
    data: List[Dict[str, Any]] = Field(..., description="查询结果数据列表，每个Dict代表一行记录")
    message: str = Field(..., description="OLAP服务响应消息")
    timeStamp: int = Field(..., description="响应时间戳")
    
    model_config = {"populate_by_name": True}


class PatientRecordQueryResponse(BaseResponse[PatientRecordQueryVo]):
    """
    患者记录查询响应
    
    API返回示例：
    {
        "code": 0,
        "message": "success",
        "data": {
            "code": 200,
            "data": [
                {
                    "patient_id": "P001",
                    "record_date": "2025-07-30",
                    "record_type": "consultation",
                    "record_content": "患者血压正常，建议继续监测"
                }
            ],
            "message": "查询成功",
            "timeStamp": 1722326400000
        }
    }
    """
    pass 