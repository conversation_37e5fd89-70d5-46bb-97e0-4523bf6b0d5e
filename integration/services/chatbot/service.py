# service.py - Chatbot服务业务封装
#
# @author: shaohua.sun
# @date: 2025-01-27
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD

"""
Chatbot服务业务封装

提供Chatbot服务相关的高级业务接口封装，包括数据验证、类型转换等。
主要对接chatbot服务的文件相关接口。
"""

from typing import Dict, Any, Optional, List
from ...core.exceptions import IntegrationError
from utils.biz_logger import get_logger
from .client import ChatbotClient
from .models import (
    FilePreviewRequest, FileUploadInfoRequest,
    FileInfo, FileDownloadInfo, FileUploadInfo,
    UserInfoData,
    # 患者数据管理相关模型
    IndicationQueryQo, IndicationQueryVo, IndicationRecordVo, IndicationDTO,
    SqlQueryRequest, PatientRecordQueryQo, PatientRecordQueryVo
)

logger = get_logger(__name__)


class ChatbotService:
    """Chatbot服务业务封装类"""
    
    def __init__(self, client: Optional[ChatbotClient] = None):
        """
        初始化Chatbot服务
        
        Args:
            client: 可选的ChatbotClient实例，用于依赖注入。
                   如果为None，则创建默认客户端实例。
        """
        # 支持客户端依赖注入，保持向下兼容
        if client is not None:
            self.client = client
            logger.info("Chatbot服务初始化完成（使用注入的客户端）")
        else:
            self.client = ChatbotClient()
            logger.info("Chatbot服务初始化完成")
    
    def get_file_preview_info(self, file_ids: List[str], biz_user_id: str) -> Optional[List[FileInfo]]:
        """
        获取文件预览信息
        
        Args:
            file_ids: 文件ID列表
            biz_user_id: 业务用户ID
            
        Returns:
            文件信息列表，如果调用失败返回None
            
        Raises:
            IntegrationError: 服务调用失败时抛出
        """
        try:
            logger.info(f"获取文件预览信息: file_ids={file_ids}, biz_user_id={biz_user_id}")
            
            if not file_ids:
                raise ValueError("文件ID列表不能为空")
            if not biz_user_id:
                raise ValueError("业务用户ID不能为空")
            
            # 创建请求模型
            request = FilePreviewRequest(
                bizUserId=biz_user_id,
                ids=file_ids
            )
            
            response = self.client.get_file_preview_info(request)
            
            # 判断响应状态，直接返回data字段
            if hasattr(response, 'code') and response.code == 0:
                return response.data if hasattr(response, 'data') else None
            elif isinstance(response, dict) and response.get('code') == 0:
                return response.get('data')
            else:
                # 安全地获取错误码，避免字符串类型调用.get()方法
                error_code = "unknown"
                if hasattr(response, 'code'):
                    error_code = response.code
                elif isinstance(response, dict):
                    error_code = response.get('code', 'unknown')
                
                logger.warning(f"获取文件预览信息失败，code: {error_code}")
                return None
            
        except Exception as e:
            logger.error(f"获取文件预览信息失败: {e}")
            raise IntegrationError(f"获取文件预览信息失败: {str(e)}")
    
    def get_file_url_by_id(self, file_id: str, biz_user_id: str) -> Optional[str]:
        """
        根据文件ID获取文件URL（便利方法）
        
        Args:
            file_id: 文件ID
            biz_user_id: 业务用户ID
            
        Returns:
            文件URL，如果获取失败返回None
        """
        try:
            file_infos = self.get_file_preview_info([file_id], biz_user_id)
            
            if file_infos and len(file_infos) > 0:
                return file_infos[0].path
            
            return None
            
        except Exception as e:
            logger.error(f"根据文件ID获取URL失败: file_id={file_id}, error={str(e)}")
            return None
    
    def download_file(self, file_id: str) -> Optional[FileDownloadInfo]:
        """
        获取文件下载信息
        
        Args:
            file_id: 文件ID
            
        Returns:
            文件下载信息，如果调用失败返回None
            
        Raises:
            IntegrationError: 服务调用失败时抛出
        """
        try:
            logger.info(f"获取文件下载信息: file_id={file_id}")
            
            if not file_id:
                raise ValueError("文件ID不能为空")
            
            response = self.client.download_file(file_id)
            
            # 判断响应状态，直接返回data字段
            if hasattr(response, 'code') and response.code == 0:
                return response.data if hasattr(response, 'data') else None
            elif isinstance(response, dict) and response.get('code') == 0:
                return response.get('data')
            else:
                # 安全地获取错误码，避免字符串类型调用.get()方法
                error_code = "unknown"
                if hasattr(response, 'code'):
                    error_code = response.code
                elif isinstance(response, dict):
                    error_code = response.get('code', 'unknown')
                
                logger.warning(f"获取文件下载信息失败，code: {error_code}")
                return None
            
        except Exception as e:
            logger.error(f"获取文件下载信息失败: {e}")
            raise IntegrationError(f"获取文件下载信息失败: {str(e)}")
    
    def get_file_upload_info(self, file_name: str, content_type: str, 
                           file_size: int, biz_user_id: str) -> Optional[FileUploadInfo]:
        """
        获取文件上传信息
        
        Args:
            file_name: 文件名
            content_type: 文件类型
            file_size: 文件大小
            biz_user_id: 业务用户ID
            
        Returns:
            文件上传信息，如果调用失败返回None
            
        Raises:
            IntegrationError: 服务调用失败时抛出
        """
        try:
            logger.info(f"获取文件上传信息: file_name={file_name}, content_type={content_type}, "
                       f"file_size={file_size}, biz_user_id={biz_user_id}")
            
            if not file_name:
                raise ValueError("文件名不能为空")
            if not content_type:
                raise ValueError("文件类型不能为空")
            if file_size <= 0:
                raise ValueError("文件大小必须大于0")
            if not biz_user_id:
                raise ValueError("业务用户ID不能为空")
            
            # 创建请求模型
            request = FileUploadInfoRequest(
                bizUserId=biz_user_id,
                fileName=file_name,
                contentType=content_type,
                length=file_size
            )
            
            response = self.client.get_file_upload_info(request)
            
            # 判断响应状态，直接返回data字段
            if hasattr(response, 'code') and response.code == 0:
                return response.data if hasattr(response, 'data') else None
            elif isinstance(response, dict) and response.get('code') == 0:
                return response.get('data')
            else:
                # 安全地获取错误码，避免字符串类型调用.get()方法
                error_code = "unknown"
                if hasattr(response, 'code'):
                    error_code = response.code
                elif isinstance(response, dict):
                    error_code = response.get('code', 'unknown')
                
                logger.warning(f"获取文件上传信息失败，code: {error_code}")
                return None
            
        except Exception as e:
            logger.error(f"获取文件上传信息失败: {e}")
            raise IntegrationError(f"获取文件上传信息失败: {str(e)}")
    
    def upload_file(self, file_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        上传文件
        
        Args:
            file_data: 文件数据字典
            
        Returns:
            上传结果，如果调用失败返回None
            
        Raises:
            IntegrationError: 服务调用失败时抛出
        """
        try:
            logger.info(f"上传文件: file_data_keys={list(file_data.keys())}")
            
            if not file_data:
                raise ValueError("文件数据不能为空")
            
            response = self.client.upload_file(file_data)
            
            # 判断响应状态，直接返回data字段
            if hasattr(response, 'code') and response.code == 0:
                return response.data if hasattr(response, 'data') else None
            elif isinstance(response, dict) and response.get('code') == 0:
                return response.get('data')
            else:
                # 安全地获取错误码，避免字符串类型调用.get()方法
                error_code = "unknown"
                if hasattr(response, 'code'):
                    error_code = response.code
                elif isinstance(response, dict):
                    error_code = response.get('code', 'unknown')
                
                logger.warning(f"上传文件失败，code: {error_code}")
                return None
            
        except Exception as e:
            logger.error(f"上传文件失败: {e}")
            raise IntegrationError(f"上传文件失败: {str(e)}")
    
    # === 便利方法接口 ===
    
    def batch_get_file_urls(self, file_ids: List[str], biz_user_id: str) -> Dict[str, str]:
        """
        批量获取文件URL
        
        Args:
            file_ids: 文件ID列表
            biz_user_id: 业务用户ID
            
        Returns:
            文件ID到URL的映射字典
        """
        try:
            file_infos = self.get_file_preview_info(file_ids, biz_user_id)
            
            url_map = {}
            if file_infos:
                for file_info in file_infos:
                    url_map[file_info.id] = file_info.path
            
            return url_map
            
        except Exception as e:
            logger.error(f"批量获取文件URL失败: {str(e)}")
            return {}
    
    def is_file_accessible(self, file_id: str, biz_user_id: str) -> bool:
        """
        检查文件是否可访问
        
        Args:
            file_id: 文件ID
            biz_user_id: 业务用户ID
            
        Returns:
            文件是否可访问
        """
        try:
            file_url = self.get_file_url_by_id(file_id, biz_user_id)
            return file_url is not None
            
        except Exception as e:
            logger.error(f"检查文件可访问性失败: file_id={file_id}, error={str(e)}")
            return False
    
    def get_user_info(self, biz_user_id: str) -> Optional[UserInfoData]:
        """
        获取用户信息
        
        Args:
            biz_user_id: 业务用户ID
            
        Returns:
            用户信息数据，如果调用失败返回None
            
        Raises:
            IntegrationError: 服务调用失败时抛出
        """
        try:
            logger.info(f"获取用户信息: biz_user_id={biz_user_id}")
            
            if not biz_user_id:
                raise ValueError("业务用户ID不能为空")
            
            response = self.client.get_user_info(biz_user_id)
            
            # 判断响应状态，直接返回data字段
            if hasattr(response, 'code') and response.code == 0:
                user_info = response.data if hasattr(response, 'data') else None
                if user_info:
                    logger.info("获取用户信息成功！")
                    logger.info(f"基础信息: {user_info.baseInfo}")
                    if user_info.dataHub:
                        logger.info(f"数据中心信息: {user_info.dataHub}")
                    else:
                        logger.info("数据中心信息: 无数据")
                return user_info
            elif isinstance(response, dict) and response.get('code') == 0:
                data = response.get('data')
                if data:
                    user_info = UserInfoData.model_validate(data)
                    logger.info("获取用户信息成功！")
                    logger.info(f"基础信息: {user_info.baseInfo}")
                    if user_info.dataHub:
                        logger.info(f"数据中心信息: {user_info.dataHub}")
                    else:
                        logger.info("数据中心信息: 无数据")
                    return user_info
                return None
            elif isinstance(response, str):
                # 处理FeignClient反序列化失败返回的HTML字符串
                logger.warning(f"收到非JSON响应（可能是错误页面）: {response[:200]}...")
                logger.warning("这通常表示服务认证失败或网络路由问题")
                return None
            else:
                # 安全地获取错误码，避免字符串类型调用.get()方法
                error_code = "unknown"
                if hasattr(response, 'code'):
                    error_code = response.code
                elif isinstance(response, dict):
                    error_code = response.get('code', 'unknown')
                
                logger.warning(f"获取用户信息失败，code: {error_code}")
                return None
            
        except Exception as e:
            logger.error(f"获取用户信息失败: {e}")
            raise IntegrationError(f"获取用户信息失败: {str(e)}")
    
    # ==================== 患者数据管理相关方法 ====================
    
    def query_last_indication(self, user_id: str, keys: List[str]) -> Optional[IndicationQueryVo]:
        """
        查询患者最新指标数据
        
        Args:
            member_id: 患者memberId，唯一标识患者
            keys: 指标key列表，指定要查询的指标类型
            
        Returns:
            指标查询结果，如果调用失败返回None
            
        Raises:
            IntegrationError: 服务调用失败时抛出
        """
        try:
            logger.info(f"查询患者最新指标数据: user_id={user_id}, keys={keys}")
            
            if not user_id:
                raise ValueError("患者memberId不能为空")
            if not keys:
                raise ValueError("指标key列表不能为空")
            
            # 创建请求模型
            request = IndicationQueryQo(
                userId=user_id,
                keys=keys
            )
            
            response = self.client.query_last_indication(request)
            
            # 判断响应状态，直接返回data字段
            if hasattr(response, 'code') and response.code == 0:
                indication_data = response.data if hasattr(response, 'data') else None
                if indication_data:
                    logger.info(f"查询患者指标数据成功！总记录数: {indication_data.total}")
                    for record in indication_data.list:
                        logger.info(f"指标记录: ID={record.id}, Key={record.indicationDTO.key}, "
                                  f"Value={record.indicationDTO.value}")
                return indication_data
            elif isinstance(response, dict) and response.get('code') == 0:
                data = response.get('data')
                if data:
                    indication_data = IndicationQueryVo.model_validate(data)
                    logger.info(f"查询患者指标数据成功！总记录数: {indication_data.total}")
                    for record in indication_data.list:
                        logger.info(f"指标记录: ID={record.id}, Key={record.indicationDTO.key}, "
                                  f"Value={record.indicationDTO.value}")
                    return indication_data
                return None
            else:
                # 安全地获取错误码，避免字符串类型调用.get()方法
                error_code = "unknown"
                if hasattr(response, 'code'):
                    error_code = response.code
                elif isinstance(response, dict):
                    error_code = response.get('code', 'unknown')
                
                logger.warning(f"查询患者指标数据失败，code: {error_code}")
                return None
            
        except Exception as e:
            logger.error(f"查询患者指标数据失败: {e}")
            raise IntegrationError(f"查询患者指标数据失败: {str(e)}")
    
    def query_patient_records(self, sql: str) -> Optional[PatientRecordQueryVo]:
        """
        查询患者记录
        
        Args:
            sql: SQL查询语句，用于在OLAP数据库中执行查询
            
        Returns:
            患者记录查询结果，如果调用失败返回None
            
        Raises:
            IntegrationError: 服务调用失败时抛出
        """
        try:
            logger.info(f"查询患者记录: sql={sql[:100]}..." if len(sql) > 100 else f"查询患者记录: sql={sql}")
            
            if not sql:
                raise ValueError("SQL查询语句不能为空")
            
            # 创建请求模型
            request = SqlQueryRequest(sql=sql)
            
            response = self.client.query_patient_records(request)
            
            # 判断响应状态，直接返回data字段
            if hasattr(response, 'code') and response.code == 0:
                record_data = response.data if hasattr(response, 'data') else None
                if record_data:
                    logger.info(f"查询患者记录成功！OLAP响应码: {record_data.code}, "
                              f"记录数: {len(record_data.data) if record_data.data else 0}")
                    if record_data.data:
                        for i, record in enumerate(record_data.data[:3]):  # 只记录前3条
                            logger.info(f"记录{i+1}: {record}")
                        if len(record_data.data) > 3:
                            logger.info(f"... 还有 {len(record_data.data) - 3} 条记录")
                return record_data
            elif isinstance(response, dict) and response.get('code') == 0:
                data = response.get('data')
                if data:
                    record_data = PatientRecordQueryVo.model_validate(data)
                    logger.info(f"查询患者记录成功！OLAP响应码: {record_data.code}, "
                              f"记录数: {len(record_data.data) if record_data.data else 0}")
                    if record_data.data:
                        for i, record in enumerate(record_data.data[:3]):  # 只记录前3条
                            logger.info(f"记录{i+1}: {record}")
                        if len(record_data.data) > 3:
                            logger.info(f"... 还有 {len(record_data.data) - 3} 条记录")
                    return record_data
                return None
            else:
                # 安全地获取错误码，避免字符串类型调用.get()方法
                error_code = "unknown"
                if hasattr(response, 'code'):
                    error_code = response.code
                elif isinstance(response, dict):
                    error_code = response.get('code', 'unknown')
                
                logger.warning(f"查询患者记录失败，code: {error_code}")
                return None
            
        except Exception as e:
            logger.error(f"查询患者记录失败: {e}")
            raise IntegrationError(f"查询患者记录失败: {str(e)}")
    
    def get_patient_health_indicators(self, user_id: str, indicator_types: List[str]) -> Dict[str, Any]:
        """
        获取患者健康指标（便利方法）
        
        Args:
            member_id: 患者memberId
            indicator_types: 指标类型列表，如 ["blood_pressure", "blood_sugar", "weight"]
            
        Returns:
            健康指标数据字典，key为指标类型，value为指标值
        """
        try:
            indication_data = self.query_last_indication(user_id, indicator_types)
            
            indicators = {}
            if indication_data and indication_data.list:
                for record in indication_data.list:
                    indicators[record.indicationDTO.key] = {
                        'value': record.indicationDTO.value,
                        'valueType': record.indicationDTO.valueType,
                        'extData': record.indicationDTO.extData,
                        'recordDateTime': record.indicationDTO.recordDateTime
                    }
            
            logger.info(f"获取患者健康指标成功: {list(indicators.keys())}")
            return indicators
            
        except Exception as e:
            logger.error(f"获取患者健康指标失败: {str(e)}")
            return {}
    
    def get_patient_records_by_sql(self, sql: str) -> List[Dict[str, Any]]:
        """
        通过SQL查询获取患者记录（便利方法）
        
        Args:
            sql: SQL查询语句
            
        Returns:
            患者记录列表
        """
        try:
            record_data = self.query_patient_records(sql)
            
            if record_data and record_data.data:
                logger.info(f"通过SQL查询获取患者记录成功，共 {len(record_data.data)} 条记录")
                return record_data.data
            else:
                logger.info("通过SQL查询获取患者记录成功，无匹配记录")
                return []
            
        except Exception as e:
            logger.error(f"通过SQL查询获取患者记录失败: {str(e)}")
            return [] 