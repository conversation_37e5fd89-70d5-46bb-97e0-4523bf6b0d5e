# __init__.py - Chatbot服务模块
#
# @author: shao<PERSON>.sun
# @date: 2025-01-27
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD

"""
Chatbot服务模块

提供chatbot服务相关的远程接口对接，包括文件服务等。
包含：
- Feign客户端接口定义
- 业务服务封装
- 数据模型定义

使用示例:
    from integration.services.chatbot import ChatbotService
    
    service = ChatbotService()
    file_info = service.get_file_preview_info(["file_id"], "user_id")
"""

from .client import ChatbotClient
from .service import ChatbotService

__all__ = [
    "ChatbotClient",
    "ChatbotService"
] 