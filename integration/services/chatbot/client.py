# client.py - Chatbot服务Feign客户端
#
# @author: shaohua.sun
# @date: 2025-01-27
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD

from typing import Dict, Any, Optional, List
from ...core.feign_client import FeignClient, get_mapping, post_mapping
from ...config.integration_config import ServiceConfig, HttpConfig
from .models import (
    # 请求模型
    FilePreviewRequest, FileUploadInfoRequest,
    # 响应模型
    FilePreviewResponse, FileDownloadResponse, FileUploadInfoResponse,
    UserInfoResponse,
    # 患者数据管理相关模型
    IndicationQueryQo, IndicationQueryResponse,
    SqlQueryRequest, PatientRecordQueryQo, PatientRecordQueryResponse
)


class ChatbotClient(FeignClient):
    """Chatbot服务Feign客户端"""
    
    def __init__(
        self, 
        service_config: Optional[ServiceConfig] = None, 
        http_config: Optional[HttpConfig] = None,
        request_interceptors: Optional[List] = None,
        response_interceptors: Optional[List] = None
    ):
        """
        初始化Chatbot服务客户端
        
        Args:
            service_config: 服务配置，为None时使用硬编码的默认配置
            http_config: HTTP配置，为None时使用硬编码的默认配置
            request_interceptors: 初始请求拦截器列表
            response_interceptors: 初始响应拦截器列表
        """
        # 硬编码的Chatbot服务配置
        if service_config is None:
            service_config = ServiceConfig(
                base_url="http://evyd-chatbot-chatbot-app-web",  # 需要根据实际部署调整
                api_key=None,
                headers={
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                enabled=True
            )
        
        # 硬编码的HTTP配置
        if http_config is None:
            http_config = HttpConfig(
                connect_timeout=10,
                read_timeout=30,
                max_retries=3,
                retry_backoff_factor=0.3,
                enable_compression=True,
                pool_connections=10,
                pool_maxsize=50
            )
        
        super().__init__(
            "chatbot", 
            service_config, 
            http_config,
            request_interceptors,
            response_interceptors
        )
    
    @post_mapping("/file/info/preview")
    def get_file_preview_info(self, request: FilePreviewRequest) -> FilePreviewResponse:
        """获取文件预览信息"""
        pass
    
    @get_mapping("/file/download")
    def download_file(self, fileId: str) -> FileDownloadResponse:
        """获取文件下载信息"""
        pass
    
    @post_mapping("/file/info/upload")
    def get_file_upload_info(self, request: FileUploadInfoRequest) -> FileUploadInfoResponse:
        """获取文件上传信息"""
        pass
    
    @post_mapping("/file/action/upload")
    def upload_file(self, file_data: Dict[str, Any]) -> Dict[str, Any]:
        """上传文件"""
        pass
    
    @get_mapping("/internal/biz/user/info")
    def get_user_info(self, bizUserId: str) -> UserInfoResponse:
        """获取用户信息"""
        pass
    
    # ==================== 患者数据管理接口 ====================
    
    @post_mapping("/indication/query-last")
    def query_last_indication(self, request: IndicationQueryQo) -> IndicationQueryResponse:
        """
        查询患者最新指标数据
        
        根据患者ID和指标key列表查询最新的指标数据
        """
        pass
    
    @post_mapping("/patient/record/query")
    def query_patient_records(self, request: SqlQueryRequest) -> PatientRecordQueryResponse:
        """
        查询患者记录
        
        通过SQL查询语句获取患者相关记录数据
        """
        pass 