# integration - 第三方业务接口对接模块
#
# @author: shaohua.sun
# @email: sunsha<PERSON><PERSON>@evydtech.com
# @date: 2025-01-27
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD

"""
第三方业务接口对接模块

这个模块提供了企业级的第三方接口对接能力，包括：
- 类Feign的声明式HTTP客户端
- 统一的请求/响应处理
- 自动重试和错误处理
- 完整的日志记录
- 配置化的超时和连接管理

支持的业务模块：
- ai_clock: AI时钟服务（营养、运动、睡眠、饮水追踪）
- hsd: HSD医疗服务（预约管理、时间段查询）
- chatbot: Chatbot服务（用户信息查询、ID转换）
"""

__version__ = "1.0.0"
__author__ = "shaohua.sun"

# 导出核心组件
from .core.feign_client import FeignClient
from .core.http_client import HttpClient
from .core.base_client import BaseClient
from .config.integration_config import IntegrationConfig

__all__ = [
    "FeignClient",
    "HttpClient", 
    "BaseClient",
    "IntegrationConfig"
] 