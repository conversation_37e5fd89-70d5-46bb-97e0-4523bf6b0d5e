# integration_config.py - 第三方集成配置
#
# @author: shaohua.sun
# @date: 2025-01-27
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD

import os
from typing import Dict, Optional, Any
from pydantic import BaseModel, Field, validator
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(dotenv_path=Path(__file__).parent.parent.parent / '.env', override=True)


class HttpConfig(BaseModel):
    """HTTP客户端配置"""
    connect_timeout: int = Field(default=10, description="连接超时时间(秒)")
    read_timeout: int = Field(default=30, description="读取超时时间(秒)")
    max_retries: int = Field(default=3, description="最大重试次数")
    retry_backoff_factor: float = Field(default=0.3, description="重试退避因子")
    enable_compression: bool = Field(default=True, description="启用压缩")
    pool_connections: int = Field(default=10, description="连接池大小")
    pool_maxsize: int = Field(default=50, description="连接池最大连接数")

    @validator('connect_timeout', 'read_timeout')
    def validate_timeout(cls, v):
        if v <= 0:
            raise ValueError('超时时间必须大于0')
        return v

    @validator('max_retries')
    def validate_retries(cls, v):
        if v < 0:
            raise ValueError('重试次数不能小于0')
        return v


class ServiceConfig(BaseModel):
    """单个服务配置"""
    base_url: str = Field(..., description="服务基础URL")
    api_key: Optional[str] = Field(default=None, description="API密钥")
    headers: Dict[str, str] = Field(default_factory=dict, description="默认请求头")
    timeout_override: Optional[int] = Field(default=None, description="覆盖默认超时时间")
    enabled: bool = Field(default=True, description="是否启用该服务")

    @validator('base_url')
    def validate_base_url(cls, v):
        if not v.startswith(('http://', 'https://')):
            raise ValueError('base_url必须以http://或https://开头')
        return v.rstrip('/')  # 移除末尾的斜杠


class IntegrationConfig(BaseModel):
    """第三方集成配置总入口"""
    
    # HTTP客户端全局配置
    http: HttpConfig = Field(default_factory=HttpConfig, description="HTTP客户端配置")
    
    # 服务配置
    ai_clock: ServiceConfig = Field(..., description="AI时钟服务配置")
    hsd: ServiceConfig = Field(..., description="HSD医疗服务配置")
    chatbot: ServiceConfig = Field(..., description="Chatbot服务配置")
    
    # 全局开关
    global_enabled: bool = Field(default=True, description="全局启用开关")
    debug_mode: bool = Field(default=False, description="调试模式")
    
    @classmethod
    def from_env(cls) -> 'IntegrationConfig':
        """从环境变量加载配置"""
        
        # HTTP全局配置
        http_config = HttpConfig(
            connect_timeout=int(os.getenv('INTEGRATION_CONNECT_TIMEOUT', '10')),
            read_timeout=int(os.getenv('INTEGRATION_READ_TIMEOUT', '30')),
            max_retries=int(os.getenv('INTEGRATION_MAX_RETRIES', '3')),
            retry_backoff_factor=float(os.getenv('INTEGRATION_RETRY_BACKOFF_FACTOR', '0.3')),
            enable_compression=os.getenv('INTEGRATION_ENABLE_COMPRESSION', 'true').lower() == 'true',
            pool_connections=int(os.getenv('INTEGRATION_POOL_CONNECTIONS', '10')),
            pool_maxsize=int(os.getenv('INTEGRATION_POOL_MAXSIZE', '50'))
        )
        
        # AI Clock服务配置
        ai_clock_config = ServiceConfig(
            base_url=os.getenv('AI_CLOCK_BASE_URL', 'http://evyd-health-manage-routines'),
            api_key=os.getenv('AI_CLOCK_API_KEY'),
            headers={
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            enabled=os.getenv('AI_CLOCK_ENABLED', 'true').lower() == 'true'
        )
        

        
        # HSD服务配置
        hsd_config = ServiceConfig(
            base_url=os.getenv('HSD_BASE_URL', 'http://evyd-health-service-hsd-ehospital'),
            api_key=os.getenv('HSD_API_KEY'),
            headers={
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            enabled=os.getenv('HSD_ENABLED', 'true').lower() == 'true'
        )
        
        # Chatbot服务配置
        chatbot_config = ServiceConfig(
            base_url=os.getenv('CHATBOT_BASE_URL', 'http://evyd-chatbot-chatbot-app-web'),
            api_key=os.getenv('CHATBOT_API_KEY'),
            headers={
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            enabled=os.getenv('CHATBOT_ENABLED', True)
        )
        
        return cls(
            http=http_config,
            ai_clock=ai_clock_config,
            hsd=hsd_config,
            chatbot=chatbot_config,
            global_enabled=os.getenv('INTEGRATION_GLOBAL_ENABLED', 'true').lower() == 'true',
            debug_mode=os.getenv('INTEGRATION_DEBUG_MODE', 'false').lower() == 'true'
        )
    
    def get_service_config(self, service_name: str) -> ServiceConfig:
        """获取指定服务的配置"""
        service_mapping = {
            'ai_clock': self.ai_clock,
            'hsd': self.hsd,
            'chatbot': self.chatbot
        }
        
        if service_name not in service_mapping:
            raise ValueError(f"未知的服务名称: {service_name}")
        
        return service_mapping[service_name]
    
    def is_service_enabled(self, service_name: str) -> bool:
        """检查指定服务是否启用"""
        if not self.global_enabled:
            return False
        
        service_config = self.get_service_config(service_name)
        return service_config.enabled


# 全局配置实例
_integration_config: Optional[IntegrationConfig] = None


def get_integration_config() -> IntegrationConfig:
    """获取全局配置实例（单例模式）"""
    global _integration_config
    if _integration_config is None:
        _integration_config = IntegrationConfig.from_env()
    return _integration_config


def reload_integration_config() -> IntegrationConfig:
    """重新加载配置（用于配置更新场景）"""
    global _integration_config
    _integration_config = IntegrationConfig.from_env()
    return _integration_config 