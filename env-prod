# EvoMind sys
ENV=prod
HTTP_PROXY=http://*************:80
HTTPS_PROXY=http://*************:80
NO_PROXY=localhost,127.0.0.1,evyd-health-manage-routines,evyd-chatbot-chatbot-app-web,evyd-health-service-hsd-ehospital,ai-evyd-opa

# AI Platform

# OpenAI
OPENAI_API_KEY=***************************************************
OPENAI_MODEL=gpt-4.1
OPENAI_MODEL_VERSION=gpt-4.1
OPENAI_PROXY=http://0.0.0.0:9000

# DeepSeek
DEEPSEEK_API_KEY=***********************************
DEEPSEEK_MODEL=deepseek-reasoner

# Tracing
LANGSMITH_TRACING=true
LANGSMITH_ENDPOINT="https://api.smith.langchain.com"
LANGSMITH_API_KEY="***************************************************"
LANGSMITH_PROJECT="evo-mind"

# mongo
MONGO_URI=mongodb://localhost:27017
MONGO_DB=chatbot_test


PROMPT_SOURCE_TYPE=mongo
PROMPT_MONGO_COLLECTION=prompts
# 或
# PROMPT_SOURCE_TYPE=api
# PROMPT_API_URL=http://localhost:8000/api/prompts

# postgresql
POSTGRES_URI=postgresql://xiaobocui:@localhost:5432/postgres?sslmode=disable

# Biz service hosts
CHATBOT_BASE_URL=http://evyd-chatbot-chatbot-app-web
# OPA服务配置
OPA_BASE_URL=http://ai-evyd-opa
OPA_TIMEOUT=60