<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健康工作流测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 80vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 18px;
            font-weight: 600;
        }

        .workflow-selector {
            padding: 10px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .workflow-selector label {
            font-weight: 500;
            color: #495057;
        }

        .workflow-selector select {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 14px;
            background: white;
        }

        .session-control {
            padding: 10px 20px;
            background: #fff3cd;
            border-bottom: 1px solid #ffeaa7;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .session-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .session-label {
            font-weight: 500;
            color: #856404;
        }

        .session-id {
            font-family: 'Courier New', monospace;
            background: #fff;
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px solid #ffeaa7;
            color: #856404;
            font-size: 12px;
        }

        .new-session-btn {
            padding: 6px 12px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: background 0.3s;
        }

        .new-session-btn:hover {
            background: #218838;
        }

        .new-session-btn:active {
            transform: scale(0.95);
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: #007bff;
        }

        .message.assistant .message-avatar {
            background: #28a745;
        }

        .message-content {
            max-width: 70%;
            padding: 15px;
            border-radius: 15px;
            position: relative;
        }

        .message.user .message-content {
            background: #007bff;
            color: white;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
        }

        .message-text {
            margin-bottom: 10px;
            line-height: 1.5;
            white-space: pre-wrap;
        }

        .message-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            margin-top: 10px;
        }

        .message-card h4 {
            color: #495057;
            margin-bottom: 10px;
            font-size: 16px;
        }

        .message-card pre {
            background: #e9ecef;
            padding: 10px;
            border-radius: 6px;
            font-size: 12px;
            overflow-x: auto;
        }

        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 10px;
        }

        .chat-input input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #ced4da;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.3s;
        }

        .chat-input input:focus {
            border-color: #007bff;
        }

        .chat-input button {
            padding: 12px 24px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background 0.3s;
        }

        .chat-input button:hover {
            background: #0056b3;
        }

        .chat-input button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .typing-indicator {
            display: none;
            padding: 10px;
            font-style: italic;
            color: #6c757d;
        }

        .typing-indicator.show {
            display: block;
        }

        .status-info {
            padding: 10px 20px;
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            margin: 10px 0;
            font-size: 12px;
            color: #1976d2;
        }

        .error-info {
            padding: 10px 20px;
            background: #ffebee;
            border-left: 4px solid #f44336;
            margin: 10px 0;
            font-size: 12px;
            color: #c62828;
        }

        .quick-actions {
            padding: 10px 20px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .quick-action {
            padding: 6px 12px;
            background: #e9ecef;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
            color: #495057;
            transition: background 0.3s;
        }

        .quick-action:hover {
            background: #dee2e6;
        }

        .ocr-dialog {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .ocr-dialog.show {
            display: flex;
        }

        .ocr-dialog-content {
            background: white;
            border-radius: 15px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .ocr-dialog-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .ocr-dialog-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .ocr-dialog-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .ocr-dialog-close:hover {
            color: #333;
        }

        .ocr-form-group {
            margin-bottom: 20px;
        }

        .ocr-form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .ocr-form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            box-sizing: border-box;
        }

        .ocr-form-group input:focus {
            outline: none;
            border-color: #007bff;
        }

        .ocr-dialog-buttons {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .ocr-dialog-button {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background 0.3s;
        }

        .ocr-dialog-button.primary {
            background: #007bff;
            color: white;
        }

        .ocr-dialog-button.primary:hover {
            background: #0056b3;
        }

        .ocr-dialog-button.secondary {
            background: #6c757d;
            color: white;
        }

        .ocr-dialog-button.secondary:hover {
            background: #545b62;
        }

        @media (max-width: 768px) {
            .chat-container {
                width: 100%;
                height: 100vh;
                border-radius: 0;
            }
            
            .message-content {
                max-width: 85%;
            }

            .ocr-dialog-content {
                padding: 20px;
                margin: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            🏥 健康工作流测试页面
        </div>
        
        <div class="workflow-selector">
            <label for="workflowType">工作流类型:</label>
            <select id="workflowType">
                <option value="health_scene_workflow">健康场景工作流</option>
                <option value="default">默认工作流</option>
            </select>
            
            <label for="streamMode" style="margin-left: 20px;">输出模式:</label>
            <select id="streamMode">
                <option value="true">流式输出</option>
                <option value="false">一次性输出</option>
            </select>
        </div>

        <div class="session-control">
            <div class="session-info">
                <span class="session-label">会话ID:</span>
                <span class="session-id" id="sessionId">加载中...</span>
            </div>
            <button class="new-session-btn" id="newSessionBtn" onclick="startNewSession()">
                🔄 新会话
            </button>
        </div>

        <div class="quick-actions">
            <button class="quick-action" onclick="sendQuickMessage('我昨天晚上11点睡觉，今天早上7点起床，睡眠质量还不错')">睡眠记录</button>
            <button class="quick-action" onclick="sendQuickMessage('我今天早餐吃了一碗白米粥和一个煮鸡蛋')">营养分析</button>
            <button class="quick-action" onclick="sendQuickMessage('我今天喝了500ml水')">饮水记录</button>
            <button class="quick-action" onclick="sendQuickMessage('今天北京天气怎么样？')">天气查询</button>
            <button class="quick-action" onclick="showOcrDialog()" style="background: #28a745;">📷 OCR识别</button>
            <button class="quick-action" onclick="testAppendText()" style="background: #ff6b6b;">🧪 测试文本显示</button>
        </div>

        <div class="chat-messages" id="chatMessages">
            <div class="status-info">
                💡 提示：选择工作流类型后，输入相关消息进行测试。健康场景工作流支持睡眠、营养、饮水等功能。<br>
                🔄 支持新的统一流式数据格式：{"id":"", "type": "text|card|marker", "content":"信息块"}<br>
                📷 新增OCR图片识别功能：点击"OCR识别"按钮可以通过文件ID或图片URL识别图片内容。
            </div>
        </div>

        <div class="typing-indicator" id="typingIndicator">
            🤖 AI正在思考中...
        </div>

        <div class="chat-input">
            <input type="text" id="messageInput" placeholder="输入消息..." onkeypress="handleKeyPress(event)">
            <button id="sendButton" onclick="sendMessage()">发送</button>
        </div>
    </div>

    <!-- OCR对话框 -->
    <div class="ocr-dialog" id="ocrDialog">
        <div class="ocr-dialog-content">
            <div class="ocr-dialog-header">
                <div class="ocr-dialog-title">📷 OCR图片识别</div>
                <button class="ocr-dialog-close" onclick="hideOcrDialog()">×</button>
            </div>
            
            <div class="ocr-form-group">
                <label for="ocrFileId">文件ID：</label>
                <input type="text" id="ocrFileId" placeholder="请输入文件ID（可选）">
            </div>
            
            <div class="ocr-form-group">
                <label for="ocrImageUrl">图片URL：</label>
                <input type="url" id="ocrImageUrl" placeholder="请输入图片URL（可选）">
            </div>
            
            <div class="ocr-form-group">
                <label for="ocrUserId">用户ID：</label>
                <input type="text" id="ocrUserId" placeholder="请输入用户ID（可选）">
            </div>
            
            <div style="margin-bottom: 20px; padding: 10px; background: #f8f9fa; border-radius: 8px; font-size: 12px; color: #666;">
                💡 提示：文件ID和图片URL至少需要提供一个。用户ID可选，系统会自动生成。
            </div>
            
            <div class="ocr-dialog-buttons">
                <button class="ocr-dialog-button secondary" onclick="hideOcrDialog()">取消</button>
                <button class="ocr-dialog-button primary" onclick="processOcr()">识别</button>
            </div>
        </div>
    </div>

    <script>
        let currentMessageElement = null;
        let currentTextElement = null;
        let currentCardElement = null;
        let isReceiving = false;
        
        // 会话管理相关变量
        let currentSessionId = null;
        
        // 生成新的会话ID
        function generateSessionId() {
            return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }
        
        // 初始化会话ID
        function initializeSessionId() {
            currentSessionId = generateSessionId();
            updateSessionDisplay();
            addStatusInfo(`🆕 新会话已创建: ${currentSessionId.substring(0, 20)}...`);
        }
        
        // 更新会话ID显示
        function updateSessionDisplay() {
            const sessionElement = document.getElementById('sessionId');
            if (sessionElement && currentSessionId) {
                // 只显示前20个字符，避免显示过长
                sessionElement.textContent = currentSessionId.substring(0, 20) + '...';
                sessionElement.title = currentSessionId; // 完整ID作为悬浮提示
            }
        }
        
        // 开始新会话
        function startNewSession() {
            if (isReceiving) {
                addStatusInfo('⚠️ 请等待当前请求完成后再开始新会话', true);
                return;
            }
            
            // 生成新的会话ID
            const oldSessionId = currentSessionId;
            currentSessionId = generateSessionId();
            updateSessionDisplay();
            
            // 清空聊天消息
            const messagesContainer = document.getElementById('chatMessages');
            messagesContainer.innerHTML = '';
            
            // 添加新会话提示
            addStatusInfo(`🔄 已切换到新会话: ${currentSessionId.substring(0, 20)}...`);
            addStatusInfo(`📝 上一个会话ID: ${oldSessionId ? oldSessionId.substring(0, 20) + '...' : '无'}`);
            addStatusInfo('🎉 新会话已准备就绪，可以开始对话了！');
            
            // 聚焦输入框
            document.getElementById('messageInput').focus();
        }

        function addMessage(role, content = '', isStreaming = false) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            
            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = role === 'user' ? '👤' : '🤖';
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            
            const textDiv = document.createElement('div');
            textDiv.className = 'message-text';
            textDiv.textContent = content;
            
            contentDiv.appendChild(textDiv);
            messageDiv.appendChild(avatar);
            messageDiv.appendChild(contentDiv);
            messagesContainer.appendChild(messageDiv);
            
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            if (isStreaming) {
                currentMessageElement = contentDiv;
                currentTextElement = textDiv;
                currentCardElement = null;
            }
            
            return { messageDiv, contentDiv, textDiv };
        }

        function appendStreamText(text) {
            console.log(`🔤 appendStreamText调用: "${text}" (类型: ${typeof text}, 长度: ${text.length})`);
            
            if (!currentTextElement) {
                console.error('❌ currentTextElement为空，无法追加文本');
                return;
            }
            
            // 记录追加前的文本长度
            const beforeLength = currentTextElement.textContent.length;
            
            currentTextElement.textContent += text;
            
            // 记录追加后的文本长度
            const afterLength = currentTextElement.textContent.length;
            
            console.log(`📏 文本长度: ${beforeLength} -> ${afterLength} (增加: ${afterLength - beforeLength})`);
            
            // 滚动到底部
            document.getElementById('chatMessages').scrollTop = document.getElementById('chatMessages').scrollHeight;
            
            // 对于数字和短文本，显示详细调试信息
            if (text.length <= 3) {
                console.log(`🔍 短文本详情: "${text}" -> 页面显示文本: "${currentTextElement.textContent.slice(-10)}"`);
            }
        }

        function addStreamCard(cardData) {
            if (currentMessageElement && cardData) {
                // 每张卡片都创建新的卡片元素，不复用currentCardElement
                const newCardElement = document.createElement('div');
                newCardElement.className = 'message-card';
                
                // 直接显示完整的卡片数据，不过滤任何字段
                let displayData = cardData;
                console.log('🎴 显示完整卡片数据:', displayData);
                
                const cardTitle = document.createElement('h4');
                cardTitle.textContent = '📊 数据卡片';
                
                const cardContent = document.createElement('pre');
                cardContent.textContent = JSON.stringify(displayData, null, 2);
                
                newCardElement.appendChild(cardTitle);
                newCardElement.appendChild(cardContent);
                
                // 将新卡片添加到消息元素中
                currentMessageElement.appendChild(newCardElement);
                
                document.getElementById('chatMessages').scrollTop = document.getElementById('chatMessages').scrollHeight;
            }
        }

        function showTyping() {
            document.getElementById('typingIndicator').classList.add('show');
        }

        function hideTyping() {
            document.getElementById('typingIndicator').classList.remove('show');
        }

        function setButtonState(disabled) {
            const sendButton = document.getElementById('sendButton');
            const messageInput = document.getElementById('messageInput');
            sendButton.disabled = disabled;
            messageInput.disabled = disabled;
            isReceiving = disabled;
        }

        function addStatusInfo(message, isError = false) {
            const messagesContainer = document.getElementById('chatMessages');
            const statusDiv = document.createElement('div');
            statusDiv.className = isError ? 'error-info' : 'status-info';
            statusDiv.textContent = message;
            messagesContainer.appendChild(statusDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        async function handleStreamResponse(response) {
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';
            let currentEvent = null;

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                buffer += decoder.decode(value, { stream: true });
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';

                for (const line of lines) {
                    if (line.trim() === '') continue;

                    // 处理事件类型
                    if (line.startsWith('event: ')) {
                        currentEvent = line.substring(7).trim();
                        console.log('🎯 收到事件类型:', currentEvent);
                        continue;
                    }

                    // 处理数据内容
                    if (line.startsWith('data: ')) {
                        const data = line.substring(6);
                        console.log('📨 收到数据:', data, '事件类型:', currentEvent);
                        
                        try {
                            // 解析新格式的消息体: {"id":"", "type": "text|card|marker", "content":"信息块"}
                            const messageData = JSON.parse(data);
                            console.log('✅ 解析消息数据:', messageData);
                            
                            // 验证新格式
                            if (messageData.id && messageData.type && messageData.content !== undefined) {
                                console.log(`📋 新格式消息 - ID: ${messageData.id}, 类型: ${messageData.type}`);
                                
                                switch (messageData.type) {
                                    case 'text':
                                        // 处理文本内容
                                        if (currentEvent === 'message') {
                                            console.log('📝 追加文本内容:', messageData.content);
                                            appendStreamText(messageData.content);
                                        }
                                        break;
                                        
                                    case 'card':
                                        // 处理卡片内容  
                                        if (currentEvent === 'custom') {
                                            console.log('🎴 显示卡片内容:', messageData.content);
                                            addStreamCard(messageData.content);
                                        }
                                        break;
                                        
                                    case 'marker':
                                        // 处理标记内容
                                        if (currentEvent === 'custom') {
                                            console.log('🏷️ 收到标记:', messageData.content);
                                            addStatusInfo(`🏷️ ${messageData.content}`);
                                        }
                                        break;
                                        
                                    default:
                                        console.warn('⚠️ 未知的消息类型:', messageData.type);
                                }
                            } else {
                                // 兼容旧格式
                                console.log('🔄 处理兼容格式数据');
                                handleLegacyFormat(messageData, currentEvent);
                            }
                            
                        } catch (e) {
                            // 处理非JSON格式的纯文本数据
                            console.log('📄 收到纯文本数据:', data);
                            if (data.length > 0 && currentEvent === 'message') {
                                appendStreamText(data);
                            }
                        }
                    }
                }
            }
        }

        // 兼容旧格式处理函数
        function handleLegacyFormat(jsonData, eventType) {
            console.log('🔄 处理兼容格式:', jsonData, '事件:', eventType);
            
            // 处理文本块
            if (jsonData.text !== undefined) {
                let textContent = jsonData.text;
                
                // 跳过标记文本
                if (textContent === '[TEXT_START]' || textContent === '[TEXT_END]' || 
                    textContent === '[ALL_START]' || textContent === '[ALL_END]') {
                    console.log('⏭️ 跳过标记文本:', textContent);
                    return;
                }
                
                // 处理实际文本内容
                if (textContent !== null && textContent !== undefined && textContent !== '') {
                    let finalText = String(textContent);
                    
                    // 处理Unicode编码字符
                    if (finalText.includes('\\u')) {
                        try {
                            finalText = JSON.parse(`"${finalText}"`);
                        } catch {
                            // 如果解析失败，保持原文本
                        }
                    }
                    
                    console.log('📝 兼容模式追加文本:', finalText);
                    appendStreamText(finalText);
                }
            }
            
            // 处理卡片数据
            if (jsonData.card !== undefined) {
                console.log('🎴 兼容模式卡片数据:', jsonData.card);
                let cardData = jsonData.card;
                
                // 如果card是字符串，尝试解析
                if (typeof cardData === 'string') {
                    try {
                        cardData = JSON.parse(cardData);
                    } catch (e) {
                        console.warn('⚠️ 解析卡片数据失败:', e);
                        cardData = { raw_data: jsonData.card };
                    }
                }
                
                addStreamCard(cardData);
            }
            
            // 处理意图识别结果
            if (jsonData.intention_result) {
                addStatusInfo(`🎯 意图识别: ${jsonData.intention_result}`);
            }
        }

        async function handleSyncResponse(response) {
            const responseData = await response.json();
            console.log('🔄 处理同步响应:', responseData);
            
            // 处理标准API响应格式 {code, message, data}
            let actualData = responseData;
            if (responseData.code === 0 && responseData.data) {
                actualData = responseData.data;
            }
            
            // 检查是否是新格式 {"id":"", "type": "text|card|marker", "content":"信息块"}
            if (actualData.id && actualData.type && actualData.content !== undefined) {
                console.log('✅ 检测到新格式同步响应');
                handleNewFormatSyncData(actualData);
                return;
            }
            
            // 兼容旧格式处理
            console.log('🔄 使用兼容模式处理同步响应');
            handleLegacySyncResponse(actualData);
        }

        // 新格式同步数据处理
        function handleNewFormatSyncData(messageData) {
            console.log(`📋 新格式同步消息 - ID: ${messageData.id}, 类型: ${messageData.type}`);
            
            switch (messageData.type) {
                case 'text':
                    // 处理文本内容
                    if (currentTextElement) {
                        currentTextElement.textContent = messageData.content;
                        console.log('📝 设置文本内容:', messageData.content);
                    }
                    break;
                    
                case 'card':
                    // 处理卡片内容  
                    console.log('🎴 显示卡片内容:', messageData.content);
                    addStreamCard(messageData.content);
                    break;
                    
                case 'marker':
                    // 处理标记内容
                    console.log('🏷️ 收到标记:', messageData.content);
                    addStatusInfo(`🏷️ ${messageData.content}`);
                    break;
                    
                default:
                    console.warn('⚠️ 未知的消息类型:', messageData.type);
            }
        }

        // 兼容旧格式同步响应处理
        function handleLegacySyncResponse(actualData) {
            // 处理健康工作流的响应文本
            let textContent = '';
            if (actualData.response) {
                textContent = actualData.response;
            } else if (actualData.text) {
                textContent = actualData.text;
            } else if (typeof actualData === 'string') {
                textContent = actualData;
            }
            
            // 显示文本内容
            if (textContent && currentTextElement) {
                currentTextElement.textContent = textContent;
                console.log('📝 兼容模式设置文本:', textContent);
            }
            
            // 处理卡片数据
            if (actualData.card) {
                let cardData = actualData.card;
                
                // 如果card是字符串，尝试解析
                if (typeof cardData === 'string') {
                    try {
                        cardData = JSON.parse(cardData);
                    } catch (e) {
                        console.warn('解析卡片数据失败:', e);
                    }
                }
                
                console.log('🎴 兼容模式卡片:', cardData);
                addStreamCard(cardData);
            }
            
            // 处理嵌套数据中的卡片
            if (actualData.data && actualData.data.card) {
                let cardData = actualData.data.card;
                
                if (typeof cardData === 'string') {
                    try {
                        cardData = JSON.parse(cardData);
                    } catch (e) {
                        console.warn('解析嵌套卡片数据失败:', e);
                    }
                }
                
                console.log('🎴 兼容模式嵌套卡片:', cardData);
                addStreamCard(cardData);
            }
            
            // 处理意图识别结果
            let intentionResult = actualData.intention_result || 
                                (actualData.data && actualData.data.intention_result) ||
                                (actualData.metadata && actualData.metadata.intention);
            
            if (intentionResult) {
                addStatusInfo(`🎯 意图识别: ${intentionResult}`);
            }
            
            // 如果没有找到任何文本内容，显示调试信息
            if (!textContent && currentTextElement) {
                currentTextElement.textContent = `调试信息：\n${JSON.stringify(actualData, null, 2)}`;
            }
        }

        async function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();
            
            if (!message || isReceiving) return;
            
            const workflowType = document.getElementById('workflowType').value;
            const streamMode = document.getElementById('streamMode').value === 'true';
            
            // 添加用户消息
            addMessage('user', message);
            messageInput.value = '';
            
            // 显示正在输入状态
            showTyping();
            setButtonState(true);
            
            // 添加AI消息容器
            addMessage('assistant', '', streamMode);
            
            try {
                // 构建请求数据
                const requestData = {
                    biz_unique_id: currentSessionId,
                    biz_user_id: '1942791814925160448',
                    message: message,
                    workflow_type: workflowType,
                    custom_data: JSON.stringify({
                        user_name: '测试用户',
                        age: 30
                    }),
                    stream: streamMode
                };

                addStatusInfo(`🚀 发送请求: ${workflowType} - ${streamMode ? '🌊 流式输出' : '⚡ 一次性输出'} - ${message.substring(0, 50)}...`);
                addStatusInfo(`🔗 会话ID: ${currentSessionId.substring(0, 20)}...`);

                // 发送请求
                // const response = await fetch('/evo-mind/v1/agent/completion', {
                const response = await fetch('/v1/agent/completion', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                hideTyping();

                if (streamMode) {
                    // 处理流式响应
                    await handleStreamResponse(response);
                } else {
                    // 处理一次性响应
                    await handleSyncResponse(response);
                }

                addStatusInfo(`✅ 响应完成 - ${streamMode ? '🌊 流式输出' : '⚡ 一次性输出'}`);

            } catch (error) {
                console.error('Error:', error);
                addStatusInfo(`❌ 请求失败: ${error.message}`, true);
                
                // 清除当前消息
                if (currentTextElement) {
                    currentTextElement.textContent = `抱歉，请求失败: ${error.message}`;
                }
            } finally {
                hideTyping();
                setButtonState(false);
                currentMessageElement = null;
                currentTextElement = null;
                currentCardElement = null;
            }
        }

        function sendQuickMessage(message) {
            if (isReceiving) return;
            document.getElementById('messageInput').value = message;
            sendMessage();
        }

        function testAppendText() {
            // 测试appendStreamText函数
            console.log('🧪 开始测试appendStreamText函数');
            
            // 模拟添加AI消息
            addMessage('assistant', '', true);
            
            // 测试各种类型的文本
            const testTexts = ['H', 'e', 'l', 'l', 'o', ' ', '1', '2', '3', ' ', '你', '好'];
            
            testTexts.forEach((text, index) => {
                setTimeout(() => {
                    console.log(`🧪 测试追加文本 ${index + 1}: "${text}"`);
                    appendStreamText(text);
                }, index * 100);
            });
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // OCR相关函数
        function showOcrDialog() {
            document.getElementById('ocrDialog').classList.add('show');
        }

        function hideOcrDialog() {
            document.getElementById('ocrDialog').classList.remove('show');
            // 清空输入框
            document.getElementById('ocrFileId').value = '';
            document.getElementById('ocrImageUrl').value = '';
            document.getElementById('ocrUserId').value = '';
        }

        async function processOcr() {
            const fileId = document.getElementById('ocrFileId').value.trim();
            const imageUrl = document.getElementById('ocrImageUrl').value.trim();
            const userId = document.getElementById('ocrUserId').value.trim();

            // 参数验证
            if (!fileId && !imageUrl) {
                addStatusInfo('❌ 请至少提供文件ID或图片URL', true);
                return;
            }

            // 关闭对话框
            hideOcrDialog();

            // 显示OCR请求信息
            const requestInfo = `📷 OCR识别请求：${fileId ? `文件ID=${fileId}` : ''}${imageUrl ? `图片URL=${imageUrl}` : ''}${userId ? `用户ID=${userId}` : ''}`;
            addMessage('user', requestInfo);

            // 显示正在处理状态
            showTyping();
            setButtonState(true);

            // 添加AI响应容器
            addMessage('assistant', '', false);

            try {
                addStatusInfo('🚀 发送OCR请求...');

                // 构建请求数据
                const requestData = {
                    file_id: fileId || null,
                    image_url: imageUrl || null,
                    biz_user_id: userId || null
                };

                // 发送OCR请求
                const response = await fetch('/evo-mind/v1/ocr/process-simple', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                
                hideTyping();

                if (result.code === 0 && result.data) {
                    // 处理成功
                    const ocrText = result.data.text || '未识别到文本内容';
                    const processingTime = result.data.processing_time || 0;
                    
                    if (currentTextElement) {
                        currentTextElement.textContent = `✅ OCR识别成功！\n\n识别结果：\n${ocrText}\n\n处理时间：${processingTime.toFixed(2)}秒`;
                    }
                    
                    addStatusInfo(`✅ OCR识别成功 - 文本长度：${ocrText.length}字符，处理时间：${processingTime.toFixed(2)}秒`);
                    
                    // 如果有卡片数据，显示卡片
                    if (result.data.card) {
                        addStreamCard(result.data.card);
                    }
                } else {
                    // 处理失败
                    const errorMsg = result.msg || '未知错误';
                    if (currentTextElement) {
                        currentTextElement.textContent = `❌ OCR识别失败：${errorMsg}`;
                    }
                    addStatusInfo(`❌ OCR识别失败：${errorMsg}`, true);
                }

            } catch (error) {
                console.error('OCR请求失败:', error);
                addStatusInfo(`❌ OCR请求失败: ${error.message}`, true);
                
                if (currentTextElement) {
                    currentTextElement.textContent = `❌ OCR请求失败: ${error.message}`;
                }
            } finally {
                hideTyping();
                setButtonState(false);
                currentMessageElement = null;
                currentTextElement = null;
                currentCardElement = null;
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化会话ID
            initializeSessionId();
            
            addStatusInfo('🎉 页面加载完成，可以开始测试对话了！');
            document.getElementById('messageInput').focus();
        });
    </script>
</body>
</html> 