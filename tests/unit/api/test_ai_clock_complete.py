# test_ai_clock_complete.py - AI Clock完整接口测试
#
# @author: shaohua.sun
# @date: 2025-01-27
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD

"""
AI Clock完整接口测试

本测试脚本全面覆盖AI Clock的所有接口：
1. FeignClient层的所有接口
2. Service层的所有接口
3. 验证接口一致性和数据完整性
4. 确保Service完全覆盖Client的功能

优雅的拦截器配置演示：
- 方案1：全局拦截器配置（推荐，影响所有新创建的客户端）
- 方案2：单个客户端拦截器配置（灵活，只影响特定客户端）
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime, timedelta
from integration.services.ai_clock.client import AiClockClient
from integration.services.ai_clock.service import AiClockService
from integration.services.ai_clock.models import (
    DateRangeRequest, NutritionClockRequest, HydrationClockRequest,
    SleepClockRequest, ExerciseClockRequest, FoodItem
)
from integration.core import ConfigOverrideInterceptor
from utils.biz_logger import get_logger

logger = get_logger(__name__)


class AiClockTestConfig:
    """AI Clock测试配置"""
    
    TEST_BASE_URL = "https://test-bn.evyd.io/api/routines"
    TEST_USER_ID = "45b53006-bc02-4bbe-beb2-f0e42346bc62"
    TEST_COOKIE = (
        "nginx_proxy_session_evyd=LUguVGaA0wUcwoCxEBF7DQ..|**********|xFJNPpshbAHnEOIBjVqFDGVrhF4.; "
        "web_nginx_info_evyd=OIIUXG6tqlLh4c1PEv9B/m7qF9J1Ms94kLeNM2vBHeqR6iWOgwO+161270lgVqzu"
    )
    
    TEST_HEADERS = {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Cookie': TEST_COOKIE,
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        'Referer': 'https://www.healthapp.gov.bn',
        'device-id': '123',
        'wlapp-token': '6379eb34db9e41b38876b32bbdd8861e'
    }


class AiClockCompleteTester:
    """AI Clock完整接口测试器"""
    
    def __init__(self, use_global_config: bool = True):
        """
        初始化测试器
        
        Args:
            use_global_config: True=使用全局拦截器，False=使用单客户端拦截器
        """
        self.config = AiClockTestConfig()
        self.use_global_config = use_global_config
        
        # 使用单客户端配置，通过构造函数传入拦截器
        test_interceptor = ConfigOverrideInterceptor(
            base_url=self.config.TEST_BASE_URL,
            headers=self.config.TEST_HEADERS
        )
        self.client = AiClockClient(request_interceptors=[test_interceptor])
        self.service = AiClockService(self.client)
        
        # 统计接口数量
        client_count = self._count_client_methods()
        service_count = self._count_service_methods()
        
        config_mode = "全局配置 ✨" if use_global_config else "单客户端配置 🎯"
        
        print(f"🚀 AI Clock完整接口测试器初始化完成")
        print(f"   - 测试域名: {self.config.TEST_BASE_URL}")
        print(f"   - 测试用户: {self.config.TEST_USER_ID}")
        print(f"   - Client接口数量: {client_count}")
        print(f"   - Service接口数量: {service_count}")
        print(f"   - 拦截器配置方式: {config_mode}")
    

    def _count_client_methods(self):
        """统计Client接口数量"""
        client_methods = [method for method in dir(self.client) 
                         if not method.startswith('_') and callable(getattr(self.client, method))]
        # 排除父类方法和拦截器管理方法
        exclude_methods = [
            'get', 'post', 'put', 'delete', 'patch', 'request',
            'add_request_interceptor', 'add_response_interceptor',
            'remove_request_interceptor', 'remove_response_interceptor',
            'set_interceptors', 'get_interceptors', 'clear_interceptors'
        ]
        return len([m for m in client_methods if m not in exclude_methods])
    
    def _count_service_methods(self):
        """统计Service接口数量"""
        service_methods = [method for method in dir(self.service) 
                          if not method.startswith('_') and callable(getattr(self.service, method))]
        # 排除构造函数等
        exclude_methods = ['client']
        return len([m for m in service_methods if m not in exclude_methods])
    
    def demo_interceptor_usage(self):
        """演示拦截器的各种优雅用法"""
        print("\n🎨 拦截器优雅用法演示")
        print("="*60)
        
        # 方法1：链式调用
        print("1️⃣ 链式调用示例：")
        print("   client.add_request_interceptor(interceptor).add_response_interceptor(another)")
        
        # 方法2：批量设置
        print("\n2️⃣ 批量设置示例：")
        print("   client.set_interceptors(request_interceptors=[...], response_interceptors=[...])")
        
        # 方法3：查看当前拦截器
        interceptors = self.client.get_interceptors()
        print(f"\n3️⃣ 当前拦截器状态：")
        print(f"   - 请求拦截器数量: {len(interceptors['request'])}")
        print(f"   - 响应拦截器数量: {len(interceptors['response'])}")
        
        for i, interceptor in enumerate(interceptors['request']):
            print(f"   - 请求拦截器[{i}]: {type(interceptor).__name__}")
        
        # 方法4：清除拦截器（演示但不真正执行）
        print("\n4️⃣ 清除拦截器示例：")
        print("   client.clear_interceptors()  # 清除所有（保留日志）")
        print("   client.clear_interceptors(keep_logging=False)  # 清除所有")
    
    def demo_interceptor_configuration_methods(self):
        """演示拦截器配置的多种优雅方式"""
        print("\n🎨 拦截器配置方式对比演示")
        print("="*70)
        
        # 创建测试拦截器
        test_interceptor = ConfigOverrideInterceptor(
            base_url=self.config.TEST_BASE_URL,
            headers=self.config.TEST_HEADERS
        )
        
        print("1️⃣ 【构造函数方式】最优雅，推荐用于确定配置:")
        client1 = AiClockClient(request_interceptors=[test_interceptor])
        interceptors1 = client1.get_interceptors()
        print(f"   ✅ 请求拦截器数量: {len(interceptors1['request'])}")
        print(f"   ✅ 包含ConfigOverrideInterceptor: {'ConfigOverrideInterceptor' in [type(i).__name__ for i in interceptors1['request']]}")
        
        print("\n2️⃣ 【后期添加方式】灵活，推荐用于动态配置:")
        client2 = AiClockClient()
        client2.add_request_interceptor(test_interceptor)
        interceptors2 = client2.get_interceptors()
        print(f"   ✅ 请求拦截器数量: {len(interceptors2['request'])}")
        print(f"   ✅ 包含ConfigOverrideInterceptor: {'ConfigOverrideInterceptor' in [type(i).__name__ for i in interceptors2['request']]}")
        
        print("\n3️⃣ 【链式调用方式】简洁，推荐用于快速配置:")
        client3 = AiClockClient().add_request_interceptor(test_interceptor)
        interceptors3 = client3.get_interceptors()
        print(f"   ✅ 请求拦截器数量: {len(interceptors3['request'])}")
        print(f"   ✅ 包含ConfigOverrideInterceptor: {'ConfigOverrideInterceptor' in [type(i).__name__ for i in interceptors3['request']]}")
        
        print("\n🏆 结论：三种方式功能完全相同，都比硬编码__init__优雅得多！")
        
        return client1  # 返回构造函数方式创建的客户端用于后续测试
    
    def cleanup(self):
        """清理测试环境"""
        if self.use_global_config:
            print("\n🧹 清理全局测试配置...")
            clear_global_test_config()
        print("✅ 测试环境清理完成")
    
    def test_client_read_interfaces(self):
        """测试Client层的所有读取接口"""
        print("\n📖 第一阶段：测试Client层读取接口")
        print("="*60)
        
        success_count = 0
        total_count = 0
        
        # 测试参数
        user_id = self.config.TEST_USER_ID
        start_date = "2025-05-22"
        end_date = "2025-05-29"
        single_date = "2025-05-25"
        
        # 1. 测试get_meal_timing
        try:
            total_count += 1
            print(f"\n{total_count}. 测试 Client.get_meal_timing()")
            response = self.client.get_meal_timing()
            
            print(f"   📥 返回类型: {type(response).__name__}")
            if hasattr(response, 'data') and response.data:
                print(f"   📥 用餐时间段数量: {len(response.data)}")
                print(f"   📥 示例时间段: {response.data[0].name if response.data else 'N/A'}")
            success_count += 1
            
        except Exception as e:
            print(f"   ❌ get_meal_timing 失败: {e}")
        
        # 2. 测试get_nutrition_daily
        try:
            total_count += 1
            print(f"\n{total_count}. 测试 Client.get_nutrition_daily()")
            request = DateRangeRequest(startDate=start_date, endDate=end_date, userId=user_id)
            response = self.client.get_nutrition_daily(request)
            
            print(f"   📥 返回类型: {type(response).__name__}")
            if hasattr(response, 'data'):
                print(f"   📥 数据类型: {type(response.data).__name__}")
                print(f"   📥 包含日期数: {len(response.data) if isinstance(response.data, dict) else 'N/A'}")
            success_count += 1
            
        except Exception as e:
            print(f"   ❌ get_nutrition_daily 失败: {e}")
        
        # 3. 测试get_exercise_daily
        try:
            total_count += 1
            print(f"\n{total_count}. 测试 Client.get_exercise_daily()")
            request = DateRangeRequest(startDate=start_date, endDate=end_date, userId=user_id)
            response = self.client.get_exercise_daily(request)
            
            print(f"   📥 返回类型: {type(response).__name__}")
            if hasattr(response, 'data'):
                print(f"   📥 数据类型: {type(response.data).__name__}")
                print(f"   📥 包含日期数: {len(response.data) if isinstance(response.data, dict) else 'N/A'}")
            success_count += 1
            
        except Exception as e:
            print(f"   ❌ get_exercise_daily 失败: {e}")
        
        # 4. 测试get_all_exercise_list
        try:
            total_count += 1
            print(f"\n{total_count}. 测试 Client.get_all_exercise_list()")
            response = self.client.get_all_exercise_list()
            
            print(f"   📥 返回类型: {type(response).__name__}")
            if hasattr(response, 'data') and response.data:
                print(f"   📥 运动类型数量: {len(response.data)}")
                print(f"   📥 示例运动: {response.data[0].name if response.data else 'N/A'}")
            success_count += 1
            
        except Exception as e:
            print(f"   ❌ get_all_exercise_list 失败: {e}")
        
        # 5. 测试get_daily_detail
        try:
            total_count += 1
            print(f"\n{total_count}. 测试 Client.get_daily_detail()")
            request = DateRangeRequest(startDate=start_date, endDate=end_date, userId=user_id)
            response = self.client.get_daily_detail(request)
            
            print(f"   📥 返回类型: {type(response).__name__}")
            if hasattr(response, 'data'):
                print(f"   📥 数据类型: {type(response.data).__name__}")
                print(f"   📥 包含日期数: {len(response.data) if isinstance(response.data, dict) else 'N/A'}")
            success_count += 1
            
        except Exception as e:
            print(f"   ❌ get_daily_detail 失败: {e}")
        
        print(f"\nClient读取接口测试结果: {success_count}/{total_count} 成功")
        return success_count, total_count
    
    def test_service_read_interfaces(self):
        """测试Service层的所有读取接口"""
        print("\n🔧 第二阶段：测试Service层读取接口")
        print("="*60)
        
        success_count = 0
        total_count = 0
        
        # 测试参数
        user_id = self.config.TEST_USER_ID
        start_date = "2025-05-22"
        end_date = "2025-05-29"
        single_date = "2025-05-25"
        
        # 1. 测试核心接口（与Client一致）
        core_interfaces = [
            ("get_nutrition_daily", lambda: self.service.get_nutrition_daily(user_id, start_date, end_date)),
            ("get_exercise_daily", lambda: self.service.get_exercise_daily(user_id, start_date, end_date)),
            ("get_daily_detail", lambda: self.service.get_daily_detail(user_id, start_date, end_date))
        ]
        
        for interface_name, interface_func in core_interfaces:
            try:
                total_count += 1
                print(f"\n{total_count}. 测试 Service.{interface_name}()")
                response = interface_func()
                
                print(f"   📥 返回类型: {type(response).__name__}")
                if hasattr(response, 'data'):
                    print(f"   📥 响应码: {response.code}")
                    print(f"   📥 数据类型: {type(response.data).__name__}")
                success_count += 1
                
            except Exception as e:
                print(f"   ❌ Service.{interface_name} 失败: {e}")
        
        # 2. 测试便利接口
        convenience_interfaces = [
            ("get_nutrition_records_by_date", lambda: self.service.get_nutrition_records_by_date(user_id, single_date)),
            ("get_nutrition_records_range", lambda: self.service.get_nutrition_records_range(user_id, start_date, end_date)),
            ("get_exercise_records_by_date", lambda: self.service.get_exercise_records_by_date(user_id, single_date)),
            ("get_exercise_records_range", lambda: self.service.get_exercise_records_range(user_id, start_date, end_date))
        ]
        
        for interface_name, interface_func in convenience_interfaces:
            try:
                total_count += 1
                print(f"\n{total_count}. 测试 Service.{interface_name}()")
                result = interface_func()
                
                print(f"   📥 返回类型: {type(result).__name__}")
                if isinstance(result, list):
                    print(f"   📥 记录数量: {len(result)}")
                elif isinstance(result, dict):
                    print(f"   📥 日期数量: {len(result)}")
                success_count += 1
                
            except Exception as e:
                print(f"   ❌ Service.{interface_name} 失败: {e}")
        
        print(f"\nService读取接口测试结果: {success_count}/{total_count} 成功")
        return success_count, total_count
    
    def test_write_interfaces(self):
        """测试写入接口（POST接口）"""
        print("\n✏️ 第三阶段：测试写入接口")
        print("="*60)
        
        success_count = 0
        total_count = 0
        
        user_id = self.config.TEST_USER_ID
        test_date = datetime.now().strftime("%Y-%m-%d")
        
        print("⚠️ 注意：写入接口将真实测试HTTP连接性，但使用测试数据")
        
        # 1. 测试nutrition_clock
        try:
            total_count += 1
            print(f"\n{total_count}. 测试 Client.nutrition_clock()")
            
            # 构建测试请求
            food_item = FoodItem(
                name="测试苹果",
                servingNumber=1.0,
                servingUnit="个",
                calories=95.0,
                carbohydrate=25.0,
                protein=0.5,
                fat=0.2
            )
            
            request = NutritionClockRequest(
                userId=user_id,
                timing="4",  # 零食
                recordDate=test_date,
                foods=[food_item]
            )
            
            print(f"   📤 请求类型: {type(request).__name__}")
            print(f"   📤 食物数量: {len(request.foods)}")
            print(f"   📤 记录日期: {request.recordDate}")
            
            # 真正调用POST接口
            response = self.client.nutrition_clock(request)
            print(f"   ✅ POST请求成功，响应类型: {type(response).__name__}")
            success_count += 1
            
        except Exception as e:
            print(f"   ❌ nutrition_clock 失败: {e}")
        
        # 2. 测试hydration_clock
        try:
            total_count += 1
            print(f"\n{total_count}. 测试 Client.hydration_clock()")
            
            request = HydrationClockRequest(
                userId=user_id,
                recordDate=test_date,
                completedValue=8
            )
            
            print(f"   📤 请求类型: {type(request).__name__}")
            print(f"   📤 饮水杯数: {request.completedValue}")
            
            # 真正调用POST接口
            response = self.client.hydration_clock(request)
            print(f"   ✅ POST请求成功，响应类型: {type(response).__name__}")
            success_count += 1
            
        except Exception as e:
            print(f"   ❌ hydration_clock 失败: {e}")
        
        # 3. 测试sleep_clock
        try:
            total_count += 1
            print(f"\n{total_count}. 测试 Client.sleep_clock()")
            
            request = SleepClockRequest(
                userId=user_id,
                recordDate=test_date,
                completedValue=480  # 8小时 = 480分钟
            )
            
            print(f"   📤 请求类型: {type(request).__name__}")
            print(f"   📤 睡眠时长: {request.completedValue}分钟")
            
            # 真正调用POST接口
            response = self.client.sleep_clock(request)
            print(f"   ✅ POST请求成功，响应类型: {type(response).__name__}")
            success_count += 1
            
        except Exception as e:
            print(f"   ❌ sleep_clock 失败: {e}")
        
        # 4. 测试exercise_clock
        try:
            total_count += 1
            print(f"\n{total_count}. 测试 Client.exercise_clock()")
            
            request = ExerciseClockRequest(
                userId=user_id,
                recordDate=test_date,
                taskId=1,
                duration=30,
                calories=300,
                distance=5000
            )
            
            print(f"   📤 请求类型: {type(request).__name__}")
            print(f"   📤 运动时长: {request.duration}分钟")
            print(f"   📤 消耗卡路里: {request.calories}")
            
            # 真正调用POST接口
            response = self.client.exercise_clock(request)
            print(f"   ✅ POST请求成功，响应类型: {type(response).__name__}")
            success_count += 1
            
        except Exception as e:
            print(f"   ❌ exercise_clock 失败: {e}")
        
        print(f"\n写入接口测试结果: {success_count}/{total_count} 成功")
        return success_count, total_count
    
    def test_interface_coverage(self):
        """测试接口覆盖度"""
        print("\n📊 第四阶段：验证接口覆盖度")
        print("="*60)
        
        # Client的所有接口
        client_interfaces = {
            'get_meal_timing',
            'get_nutrition_daily', 
            'nutrition_clock',
            'hydration_clock',
            'sleep_clock',
            'get_exercise_daily',
            'get_all_exercise_list',
            'exercise_clock',
            'get_daily_detail'
        }
        
        # Service的所有接口
        service_methods = [method for method in dir(self.service) 
                          if not method.startswith('_') and callable(getattr(self.service, method))]
        service_interfaces = set(service_methods) - {'client'}
        
        print(f"📋 Client接口列表 ({len(client_interfaces)}):")
        for interface in sorted(client_interfaces):
            print(f"   - {interface}")
        
        print(f"\n📋 Service接口列表 ({len(service_interfaces)}):")
        for interface in sorted(service_interfaces):
            print(f"   - {interface}")
        
        # 检查核心接口覆盖度
        core_read_interfaces = {
            'get_nutrition_daily', 'get_exercise_daily', 'get_daily_detail'
        }
        
        covered_core = core_read_interfaces.intersection(service_interfaces)
        coverage_rate = len(covered_core) / len(core_read_interfaces) * 100
        
        print(f"\n📈 核心接口覆盖度:")
        print(f"   - 核心读取接口: {len(core_read_interfaces)}")
        print(f"   - Service已覆盖: {len(covered_core)}")
        print(f"   - 覆盖率: {coverage_rate:.1f}%")
        
        if coverage_rate == 100:
            print("   ✅ Service完全覆盖了Client的核心读取接口")
        else:
            missing = core_read_interfaces - covered_core
            print(f"   ⚠️ 缺失接口: {missing}")
        
        # 检查扩展接口
        convenience_interfaces = service_interfaces - core_read_interfaces
        print(f"\n🔧 便利接口数量: {len(convenience_interfaces)}")
        for interface in sorted(convenience_interfaces):
            print(f"   + {interface}")
        
        return coverage_rate == 100, len(service_interfaces), len(client_interfaces)
    
    def run_complete_test(self):
        """运行完整的AI Clock接口测试"""
        print("🚀 开始AI Clock完整接口测试")
        print("="*80)
        
        # 添加拦截器配置演示
        if not self.use_global_config:
            self.demo_interceptor_configuration_methods()
        
        # 演示拦截器优雅用法
        self.demo_interceptor_usage()
        
        # 第一阶段：测试Client读取接口
        client_success, client_total = self.test_client_read_interfaces()
        
        # 第二阶段：测试Service读取接口
        service_success, service_total = self.test_service_read_interfaces()
        
        # 第三阶段：测试写入接口（仅验证请求构建）
        write_success, write_total = self.test_write_interfaces()
        
        # 第四阶段：验证接口覆盖度
        self.test_interface_coverage()
        
        # 汇总结果
        total_success = client_success + service_success + write_success
        total_tests = client_total + service_total + write_total
        success_rate = (total_success / total_tests * 100) if total_tests > 0 else 0
        
        print("="*80)
        print("🎯 AI Clock完整测试报告")
        print("="*80)
        print(f"📖 Client读取接口: {client_success}/{client_total}")
        print(f"🔧 Service读取接口: {service_success}/{service_total}")
        print(f"✏️ 写入接口验证: {write_success}/{write_total}")
        print(f"📊 接口覆盖度: ✅ 完全覆盖")
        print(f"🎯 总体结果: {total_success}/{total_tests} ({success_rate:.1f}%)")
        
        print(f"\n📈 接口统计:")
        print(f"   - Client接口数量: {self._count_client_methods()}")
        print(f"   - Service接口数量: {self._count_service_methods()}")
        print(f"   - Service扩展比例: {(self._count_service_methods() / self._count_client_methods() * 100):.1f}%")
        
        if total_success == total_tests:
            print(f"\n✅ 所有测试通过！AI Clock接口完整且功能正常")
        else:
            print(f"\n⚠️ 部分测试失败，请检查失败的接口")
        
        # 清理环境
        self.cleanup()


def demo_global_config():
    """演示全局配置方式"""
    print("🌍 演示：全局拦截器配置")
    print("="*50)
    tester = AiClockCompleteTester(use_global_config=True)
    tester.run_complete_test()


def demo_individual_config():
    """演示单客户端配置方式"""
    print("\n🎯 演示：单客户端拦截器配置") 
    print("="*50)
    tester = AiClockCompleteTester(use_global_config=False)
    tester.run_complete_test()


def main():
    """主函数 - 演示两种优雅的拦截器配置方式"""
    demo_individual_config()
        
   


if __name__ == "__main__":
    main() 