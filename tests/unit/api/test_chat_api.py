import pytest
from fastapi.testclient import TestClient
from main import app

client = TestClient(app)

def test_chat_default():
    response = client.post("/v1/chat", json={"message": "你好"})
    assert response.status_code == 200
    assert "data" in response.json()

def test_agent_stream_sse():
    # 测试 /v1/chat/agent-stream SSE流
    with client.stream("POST", "/v1/chat/agent-stream", json={"message": "今天天气怎么样？"}) as response:
        assert response.status_code == 200
        # 检查响应头是否为 SSE
        assert response.headers["content-type"].startswith("text/event-stream")
        # 读取部分流式内容，验证格式
        first_chunk = next(response.iter_lines())
        assert first_chunk.startswith("data: ")
        assert "text" in first_chunk

def test_agent_mem_short_memory():
    """
    测试 /v1/chat/agent-mem-short 是否有记忆能力：
    连续两次提问，第二次回复应包含第一次内容的上下文。
    """
    # 第一次提问
    with client.stream("POST", "/v1/chat/agent-mem-short", json={"message": "我叫小明"}) as response1:
        assert response1.status_code == 200
        first_chunks = list(response1.iter_lines())
        # 取最后一条非空流内容
        first_text = ""
        for chunk in reversed(first_chunks):
            if chunk.strip():
                first_text = chunk
                break
        assert "小明" in first_text  # 检查模型是否记住了名字

    # 第二次提问，询问"我是谁"
    with client.stream("POST", "/v1/chat/agent-mem-short", json={"message": "你还记得我是谁吗？"}) as response2:
        assert response2.status_code == 200
        second_chunks = list(response2.iter_lines())
        second_text = ""
        for chunk in reversed(second_chunks):
            if chunk.strip():
                second_text = chunk
                break
        # 期望模型能记住"小明"
        assert ("小明" in second_text) or ("你叫小明" in second_text) 