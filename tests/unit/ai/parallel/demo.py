import asyncio
import time


async def hello():
    print("START")
    await asyncio.sleep(5)
    print("END")


async def task1():
    print("TASK1 START")
    await asyncio.sleep(2)
    print("TASK1 END")

async def task2():
    print("TASK2 START")
    await asyncio.sleep(2)
    print("TASK2 END")

def task_serial(i):
    print(f"TASK_{i} START")
    time.sleep(2)
    print(f"TASK_{i} END")

def run_serial():
    start_time = time.time()
    for i in range(3):
        task_serial(i)
    end_time = time.time()
    print(f"RUN SERIAL COST: {end_time - start_time}")


async def task_async(i):
    print(f"ASYNC_TASK_{i} START")
    await asyncio.sleep(2)
    print(f"ASYNC_TASK_{i} END")

async def run_async():
    start_time = time.time()
    tasks = [task_async(i) for i in range(3)]
    # 并发执行所有任务
    await asyncio.gather(*tasks)
    end_time = time.time()
    print(f"RUN ASYNC COST: {end_time - start_time}")


async def main():
    # await task1()
    # await task2

    # 异步任务
    task_1 = asyncio.create_task(task1())
    task_2 = asyncio.create_task(task2())
    # 等待执行完毕
    await task_1
    await task_2



# 多线程
def multi_thread():
    import threading
    import concurrent.futures

    def function_b():
        # 模拟耗时操作
        import time
        time.sleep(3)
        print("Function B finished")

    def function_a():
        # 创建并启动一个新线程来执行 function_b
        # thread = threading.Thread(target=function_b)
        # thread.start()
        # print("Function A returned without waiting for Function B")
        # return

        # 使用线程池实现
        with concurrent.futures.ThreadPoolExecutor() as executor:
            future = executor.submit(function_b)
            print("Function A returned without waiting for Function B")
            return future

    function_a()


# 协程
async def multi_coroutine():
    async def function_b():
        # 模拟耗时操作
        await asyncio.sleep(3)
        print("Function B finished")

    async def function_a():
        # 创建并启动一个新协程来执行 function_b
        task  = asyncio.create_task(function_b())
        print("Function A returned without waiting for Function B")
        # 等待执行, 否则目标函数不执行
        await task
        return
    await function_a()


if __name__ == "__main__":
    # asyncio.run(hello())
    # asyncio.run(main())
    # run_serial()
    # asyncio.run(run_async())
    multi_thread()
    # asyncio.run(multi_coroutine())



