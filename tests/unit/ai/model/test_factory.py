import pytest
from unittest.mock import patch, MagicMock
from ai.factory import Factory
from ai.config.ai_enum import AIPlatformEnum
from ai.config.ai_config import FactoryConfig, ModelConfig


class TestFactory:
    """AI Factory 测试类"""

    def test_factory_init_with_config(self):
        """测试工厂初始化"""
        config = FactoryConfig(platform=AIPlatformEnum.OPENAI)
        factory = Factory(config)
        assert factory.ai_factory_config == config
        assert factory.ai_properties is not None

    def test_factory_init_without_config(self):
        """测试工厂初始化（无配置）"""
        factory = Factory(None)
        assert factory.ai_factory_config.platform == AIPlatformEnum.OPENAI

    @patch('ai.factory.ChatOpenAI')
    def test_build_openai_with_config(self, mock_openai):
        """测试构建OpenAI实例（带配置）"""
        factory = Factory(FactoryConfig(platform=AIPlatformEnum.OPENAI))
        mock_openai.return_value = "openai_instance"
        
        model_config = ModelConfig(
            model_name="gpt-4",
            api_key="test_key",
            temperature=0.7,
            streaming=True
        )
        
        result = factory.build(model_config)
        
        assert result == "openai_instance"
        # 验证调用参数，但不检查具体的api_key值（因为它是SecretStr）
        mock_openai.assert_called_once()
        call_args = mock_openai.call_args[1]
        assert call_args["model"] == "gpt-4"
        assert call_args["temperature"] == 0.7
        assert call_args["streaming"] == True

    @patch('ai.factory.ChatOpenAI')
    def test_build_openai_without_config(self, mock_openai):
        """测试构建OpenAI实例（无配置）"""
        factory = Factory(FactoryConfig(platform=AIPlatformEnum.OPENAI))
        mock_openai.return_value = "openai_instance"
        
        result = factory.build(None)
        
        assert result == "openai_instance"
        mock_openai.assert_called_once()

    @patch('ai.factory.ChatQwen')
    def test_build_deepseek(self, mock_deepseek):
        """测试构建DeepSeek实例"""
        factory = Factory(FactoryConfig(platform=AIPlatformEnum.DEEPSEEK))
        mock_deepseek.return_value = "deepseek_instance"
        
        model_config = ModelConfig(
            model_name="deepseek-chat",
            api_key="test_key",
            temperature=0.5
        )
        
        result = factory.build(model_config)
        
        assert result == "deepseek_instance"
        mock_deepseek.assert_called_once()

    @patch('ai.factory.ChatQwen')
    def test_build_qwen(self, mock_qwen):
        """测试构建Qwen实例"""
        factory = Factory(FactoryConfig(platform=AIPlatformEnum.QWEN))
        mock_qwen.return_value = "qwen_instance"
        
        model_config = ModelConfig(
            model_name="qwen-turbo",
            api_key="test_key",
            temperature=0.3
        )
        
        result = factory.build(model_config)
        
        assert result == "qwen_instance"
        mock_qwen.assert_called_once()

    def test_build_unsupported_platform(self):
        """测试不支持的平台"""
        factory = Factory(FactoryConfig(platform="unsupported"))
        
        with pytest.raises(ValueError, match="Platform not supported: unsupported"):
            factory.build(ModelConfig())

    @patch('ai.factory.ChatOpenAI')
    def test_build_openai_default_values(self, mock_openai):
        """测试OpenAI默认值处理"""
        factory = Factory(FactoryConfig(platform=AIPlatformEnum.OPENAI))
        mock_openai.return_value = "openai_instance"
        
        # 测试部分配置为空的情况，使用默认值
        model_config = ModelConfig()
        
        result = factory.build(model_config)
        
        assert result == "openai_instance"
        mock_openai.assert_called_once()

    @patch('ai.factory.ChatQwen')
    def test_build_deepseek_default_values(self, mock_deepseek):
        """测试DeepSeek默认值处理"""
        factory = Factory(FactoryConfig(platform=AIPlatformEnum.DEEPSEEK))
        mock_deepseek.return_value = "deepseek_instance"
        
        model_config = ModelConfig()
        
        result = factory.build(model_config)
        
        assert result == "deepseek_instance"
        mock_deepseek.assert_called_once()

    @patch('ai.factory.ChatQwen')
    def test_build_qwen_default_values(self, mock_qwen):
        """测试Qwen默认值处理"""
        factory = Factory(FactoryConfig(platform=AIPlatformEnum.QWEN))
        mock_qwen.return_value = "qwen_instance"
        
        model_config = ModelConfig()
        
        result = factory.build(model_config)
        
        assert result == "qwen_instance"
        mock_qwen.assert_called_once()

    def test_platform_case_insensitive(self):
        """测试平台名称大小写不敏感"""
        factory = Factory(FactoryConfig(platform="OPENAI"))
        
        with patch('ai.factory.ChatOpenAI') as mock_openai:
            mock_openai.return_value = "openai_instance"
            result = factory.build(ModelConfig())
            assert result == "openai_instance"

    @pytest.mark.parametrize("platform", [
        AIPlatformEnum.OPENAI,
        AIPlatformEnum.DEEPSEEK,
        AIPlatformEnum.QWEN
    ])
    def test_all_supported_platforms(self, platform):
        """测试所有支持的平台"""
        factory = Factory(FactoryConfig(platform=platform))
        
        with patch('ai.factory.ChatOpenAI') as mock_openai, \
             patch('ai.factory.ChatQwen') as mock_qwen:
            
            mock_openai.return_value = "openai_instance"
            mock_qwen.return_value = "qwen_instance"
            
            result = factory.build(ModelConfig())
            
            if platform == AIPlatformEnum.OPENAI:
                assert result == "openai_instance"
                mock_openai.assert_called_once()
            else:
                assert result == "qwen_instance"
                mock_qwen.assert_called_once() 