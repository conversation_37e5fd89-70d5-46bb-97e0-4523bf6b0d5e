import pytest
from unittest.mock import patch, MagicMock
from ai.router import Router
from ai.config.ai_enum import TaskTypeEnum, AIPlatformEnum
from ai.config.ai_config import RouterConfig, FactoryConfig, ModelConfig


class TestRouter:
    """AI Router 测试类"""

    def test_router_init_with_config(self):
        """测试路由器初始化（带配置）"""
        config = RouterConfig(task_type=TaskTypeEnum.TEXT, platform=AIPlatformEnum.OPENAI)
        router = Router(config)
        assert router.ai_router_config == config
        assert router.ai_properties is not None

    def test_router_init_without_config(self):
        """测试路由器初始化（无配置）"""
        router = Router(None)
        assert router.ai_router_config.task_type == 'text'

    @patch('ai.router.Factory')
    def test_handle_text_openai(self, mock_factory_class):
        """测试处理文本任务（OpenAI平台）"""
        router = Router(RouterConfig(task_type=TaskTypeEnum.TEXT, platform=AIPlatformEnum.OPENAI))
        
        # 模拟Factory
        mock_factory = MagicMock()
        mock_ai_instance = MagicMock()
        mock_factory.build.return_value = mock_ai_instance
        mock_factory_class.return_value = mock_factory
        
        factory_config = FactoryConfig(platform=AIPlatformEnum.OPENAI)
        model_config = ModelConfig()
        
        result = router.handle(factory_config, model_config)
        
        assert result == mock_ai_instance
        mock_factory_class.assert_called_once()
        mock_factory.build.assert_called_once()

    @patch('ai.router.Factory')
    def test_handle_text_deepseek(self, mock_factory_class):
        """测试处理文本任务（DeepSeek平台）"""
        router = Router(RouterConfig(task_type=TaskTypeEnum.TEXT, platform=AIPlatformEnum.DEEPSEEK))
        
        # 模拟Factory
        mock_factory = MagicMock()
        mock_ai_instance = MagicMock()
        mock_factory.build.return_value = mock_ai_instance
        mock_factory_class.return_value = mock_factory
        
        factory_config = FactoryConfig(platform=AIPlatformEnum.OPENAI)
        model_config = ModelConfig()
        
        result = router.handle(factory_config, model_config)
        
        assert result == mock_ai_instance
        mock_factory_class.assert_called_once()
        mock_factory.build.assert_called_once()

    @patch('ai.router.Factory')
    def test_handle_text_unsupported_platform(self, mock_factory_class):
        """测试处理文本任务（不支持的平台）"""
        router = Router(RouterConfig(task_type=TaskTypeEnum.TEXT, platform="unsupported"))
        
        factory_config = FactoryConfig(platform=AIPlatformEnum.OPENAI)
        model_config = ModelConfig()
        
        with pytest.raises(ValueError, match="Platform not supported: unsupported"):
            router.handle(factory_config, model_config)

    @patch('ai.router.Factory')
    def test_handle_image_openai(self, mock_factory_class):
        """测试处理图像任务（OpenAI平台）"""
        router = Router(RouterConfig(task_type=TaskTypeEnum.IMAGE, platform=AIPlatformEnum.OPENAI))
        
        # 模拟Factory
        mock_factory = MagicMock()
        mock_ai_instance = MagicMock()
        mock_factory.build.return_value = mock_ai_instance
        mock_factory_class.return_value = mock_factory
        
        factory_config = FactoryConfig(platform=AIPlatformEnum.OPENAI)
        model_config = ModelConfig()
        
        result = router.handle(factory_config, model_config)
        
        assert result == mock_ai_instance
        mock_factory_class.assert_called_once()
        mock_factory.build.assert_called_once()

    @patch('ai.router.Factory')
    def test_handle_image_qwen(self, mock_factory_class):
        """测试处理图像任务（Qwen平台）"""
        router = Router(RouterConfig(task_type=TaskTypeEnum.IMAGE, platform=AIPlatformEnum.QWEN))

        # 模拟Factory
        mock_factory = MagicMock()
        mock_ai_instance = MagicMock()
        mock_factory.build.return_value = mock_ai_instance
        mock_factory_class.return_value = mock_factory

        factory_config = FactoryConfig(platform=AIPlatformEnum.QWEN)
        model_config = ModelConfig()

        result = router.handle(factory_config, model_config)

        result.invoke("test")

        assert result == mock_ai_instance
        mock_factory_class.assert_called_once()
        mock_factory.build.assert_called_once()

    @patch('ai.router.Factory')
    def test_handle_image_unsupported_platform(self, mock_factory_class):
        """测试处理图像任务（不支持的平台）"""
        router = Router(RouterConfig(task_type=TaskTypeEnum.IMAGE, platform="unsupported"))
        
        factory_config = FactoryConfig(platform="unsupported")  # 使用不支持的平台
        model_config = ModelConfig()
        
        with pytest.raises(ValueError, match="Platform not supported for image: unsupported"):
            router.handle(factory_config, model_config)

    def test_handle_unsupported_task_type(self):
        """测试不支持的任务类型"""
        router = Router(RouterConfig(task_type="unsupported", platform=AIPlatformEnum.OPENAI))
        
        factory_config = FactoryConfig(platform=AIPlatformEnum.OPENAI)
        model_config = ModelConfig()
        
        with pytest.raises(ValueError, match="Task type not supported: unsupported"):
            router.handle(factory_config, model_config)

    @patch('ai.router.Factory')
    def test_handle_text_default_platform_openai(self, mock_factory_class):
        """测试文本任务默认平台为OpenAI"""
        router = Router(RouterConfig(task_type=TaskTypeEnum.TEXT))
        
        # 模拟Factory
        mock_factory = MagicMock()
        mock_ai_instance = MagicMock()
        mock_factory.build.return_value = mock_ai_instance
        mock_factory_class.return_value = mock_factory
        
        factory_config = FactoryConfig(platform=AIPlatformEnum.OPENAI)
        model_config = ModelConfig()
        
        result = router.handle(factory_config, model_config)
        
        assert result == mock_ai_instance
        mock_factory_class.assert_called_once()

    @patch('ai.router.Factory')
    def test_handle_text_platform_override(self, mock_factory_class):
        """测试文本任务平台覆盖"""
        router = Router(RouterConfig(task_type=TaskTypeEnum.TEXT, platform=AIPlatformEnum.DEEPSEEK))
        
        # 模拟Factory
        mock_factory = MagicMock()
        mock_ai_instance = MagicMock()
        mock_factory.build.return_value = mock_ai_instance
        mock_factory_class.return_value = mock_factory
        
        factory_config = FactoryConfig(platform=AIPlatformEnum.OPENAI)
        model_config = ModelConfig()
        
        result = router.handle(factory_config, model_config)
        
        assert result == mock_ai_instance
        mock_factory_class.assert_called_once()

    @patch('ai.router.Factory')
    def test_handle_text_with_model_config(self, mock_factory_class):
        """测试文本任务带模型配置"""
        router = Router(RouterConfig(task_type=TaskTypeEnum.TEXT, platform=AIPlatformEnum.OPENAI))
        
        # 模拟Factory
        mock_factory = MagicMock()
        mock_ai_instance = MagicMock()
        mock_factory.build.return_value = mock_ai_instance
        mock_factory_class.return_value = mock_factory
        
        factory_config = FactoryConfig(platform=AIPlatformEnum.OPENAI)
        model_config = ModelConfig(
            api_key="test_key",
            model_name="gpt-4",
            temperature=0.7
        )
        
        result = router.handle(factory_config, model_config)
        
        assert result == mock_ai_instance
        mock_factory_class.assert_called_once()
        
        # 验证Factory调用参数
        factory_call_args = mock_factory_class.call_args[1]
        assert factory_call_args["factory_config"].platform == AIPlatformEnum.OPENAI

    @patch('ai.router.Factory')
    def test_handle_image_with_model_config(self, mock_factory_class):
        """测试图像任务带模型配置"""
        router = Router(RouterConfig(task_type=TaskTypeEnum.IMAGE, platform=AIPlatformEnum.OPENAI))
        
        # 模拟Factory
        mock_factory = MagicMock()
        mock_ai_instance = MagicMock()
        mock_factory.build.return_value = mock_ai_instance
        mock_factory_class.return_value = mock_factory
        
        factory_config = FactoryConfig(platform=AIPlatformEnum.OPENAI)
        model_config = ModelConfig(
            api_key="test_key",
            model_name="gpt-4-vision",
            temperature=0.5
        )
        
        result = router.handle(factory_config, model_config)
        
        assert result == mock_ai_instance
        mock_factory_class.assert_called_once()

    @patch('ai.router.Factory')
    def test_handle_text_platform_case_insensitive(self, mock_factory_class):
        """测试平台名称大小写不敏感"""
        router = Router(RouterConfig(task_type=TaskTypeEnum.TEXT, platform="openai"))
        
        # 模拟Factory
        mock_factory = MagicMock()
        mock_ai_instance = MagicMock()
        mock_factory.build.return_value = mock_ai_instance
        mock_factory_class.return_value = mock_factory
        
        factory_config = FactoryConfig(platform=AIPlatformEnum.OPENAI)
        model_config = ModelConfig()
        
        result = router.handle(factory_config, model_config)
        
        assert result == mock_ai_instance
        mock_factory_class.assert_called_once()

    @patch('ai.router.Factory')
    def test_handle_text_deepseek_platform_case_insensitive(self, mock_factory_class):
        """测试DeepSeek平台名称大小写不敏感"""
        router = Router(RouterConfig(task_type=TaskTypeEnum.TEXT, platform="deepseek"))
        
        # 模拟Factory
        mock_factory = MagicMock()
        mock_ai_instance = MagicMock()
        mock_factory.build.return_value = mock_ai_instance
        mock_factory_class.return_value = mock_factory
        
        factory_config = FactoryConfig(platform=AIPlatformEnum.OPENAI)
        model_config = ModelConfig()
        
        result = router.handle(factory_config, model_config)
        
        assert result == mock_ai_instance
        mock_factory_class.assert_called_once()

    @pytest.mark.parametrize("task_type,platform", [
        (TaskTypeEnum.TEXT, AIPlatformEnum.OPENAI),
        (TaskTypeEnum.TEXT, AIPlatformEnum.DEEPSEEK),
        (TaskTypeEnum.IMAGE, AIPlatformEnum.OPENAI),
    ])
    @patch('ai.router.Factory')
    def test_handle_all_supported_combinations(self, mock_factory_class, task_type, platform):
        """测试所有支持的任务类型和平台组合"""
        router = Router(RouterConfig(task_type=task_type, platform=platform))
        
        # 模拟Factory
        mock_factory = MagicMock()
        mock_ai_instance = MagicMock()
        mock_factory.build.return_value = mock_ai_instance
        mock_factory_class.return_value = mock_factory
        
        factory_config = FactoryConfig(platform=AIPlatformEnum.OPENAI)
        model_config = ModelConfig()
        
        result = router.handle(factory_config, model_config)
        
        assert result == mock_ai_instance
        mock_factory_class.assert_called_once()

    def test_handle_text_with_none_platform(self):
        """测试文本任务平台为None的情况"""
        # 由于RouterConfig不允许platform为None，我们测试默认行为
        router = Router(RouterConfig(task_type=TaskTypeEnum.TEXT))
        
        with patch('ai.router.Factory') as mock_factory_class:
            mock_factory = MagicMock()
            mock_ai_instance = MagicMock()
            mock_factory.build.return_value = mock_ai_instance
            mock_factory_class.return_value = mock_factory
            
            factory_config = FactoryConfig(platform=AIPlatformEnum.OPENAI)
            model_config = ModelConfig()
            
            result = router.handle(factory_config, model_config)
            
            assert result == mock_ai_instance
            mock_factory_class.assert_called_once()

    def test_handle_image_with_none_platform(self):
        """测试图像任务平台为None的情况"""
        # 由于RouterConfig不允许platform为None，我们测试默认行为
        router = Router(RouterConfig(task_type=TaskTypeEnum.IMAGE))
        
        with patch('ai.router.Factory') as mock_factory_class:
            mock_factory = MagicMock()
            mock_ai_instance = MagicMock()
            mock_factory.build.return_value = mock_ai_instance
            mock_factory_class.return_value = mock_factory
            
            factory_config = FactoryConfig(platform=AIPlatformEnum.OPENAI)
            model_config = ModelConfig()
            
            result = router.handle(factory_config, model_config)
            
            assert result == mock_ai_instance
            mock_factory_class.assert_called_once()

    @patch('ai.router.Factory')
    def test_handle_text_model_config_priority(self, mock_factory_class):
        """测试文本任务模型配置优先级"""
        router = Router(RouterConfig(task_type=TaskTypeEnum.TEXT, platform=AIPlatformEnum.OPENAI))
        
        # 模拟Factory
        mock_factory = MagicMock()
        mock_ai_instance = MagicMock()
        mock_factory.build.return_value = mock_ai_instance
        mock_factory_class.return_value = mock_factory
        
        factory_config = FactoryConfig(platform=AIPlatformEnum.OPENAI)
        model_config = ModelConfig(
            api_key="override_key",
            model_name="override_model",
            temperature=0.9
        )
        
        result = router.handle(factory_config, model_config)
        
        assert result == mock_ai_instance
        mock_factory_class.assert_called_once()
        
        # 验证Factory调用参数
        factory_call_args = mock_factory_class.call_args[1]
        assert factory_call_args["factory_config"].platform == AIPlatformEnum.OPENAI 