#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI模块测试运行器
用于运行AI Factory、Manager和Router的测试

@author: 崔晓波
@email: <EMAIL>
@date: 2025/1/17
"""

import pytest
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)


def run_factory_tests():
    """运行Factory测试"""
    print("=" * 50)
    print("运行 AI Factory 测试")
    print("=" * 50)
    
    result = pytest.main([
        "tests/unit/ai/model/test_factory.py",
        "-v",
        "--tb=short"
    ])
    
    return result


def run_manager_tests():
    """运行Manager测试"""
    print("=" * 50)
    print("运行 AI Manager 测试")
    print("=" * 50)
    
    result = pytest.main([
        "tests/unit/ai/model/test_manager.py",
        "-v",
        "--tb=short"
    ])
    
    return result


def run_router_tests():
    """运行Router测试"""
    print("=" * 50)
    print("运行 AI Router 测试")
    print("=" * 50)
    
    result = pytest.main([
        "tests/unit/ai/model/test_router.py",
        "-v",
        "--tb=short"
    ])
    
    return result


def run_all_tests():
    """运行所有AI模块测试"""
    print("=" * 50)
    print("运行所有 AI 模块测试")
    print("=" * 50)
    
    test_files = [
        "tests/unit/ai/model/test_factory.py",
        "tests/unit/ai/model/test_manager.py",
        "tests/unit/ai/model/test_router.py"
    ]
    
    result = pytest.main([
        *test_files,
        "-v",
        "--tb=short"
    ])
    
    return result


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="AI模块测试运行器")
    parser.add_argument(
        "--module",
        choices=["factory", "manager", "router", "all"],
        default="all",
        help="选择要运行的测试模块"
    )
    
    args = parser.parse_args()
    
    if args.module == "factory":
        exit_code = run_factory_tests()
    elif args.module == "manager":
        exit_code = run_manager_tests()
    elif args.module == "router":
        exit_code = run_router_tests()
    else:
        exit_code = run_all_tests()
    
    sys.exit(exit_code) 