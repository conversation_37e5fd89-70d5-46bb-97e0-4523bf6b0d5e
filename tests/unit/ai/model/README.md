# AI模块测试文档

本文档描述了AI模块（Factory、Manager、Router）的测试代码结构和使用方法。

## 测试文件结构

```
tests/unit/ai/model/
├── test_factory.py      # AI Factory 测试
├── test_manager.py      # AI Manager 测试
├── test_router.py       # AI Router 测试
├── test_runner.py       # 测试运行器
└── README.md           # 本文档
```

## 测试覆盖范围

### 1. AI Factory 测试 (`test_factory.py`)

**测试类**: `TestFactory`

**测试覆盖**:
- ✅ 工厂初始化（带配置/无配置）
- ✅ 构建OpenAI实例（带配置/无配置/默认值）
- ✅ 构建DeepSeek实例（带配置/默认值）
- ✅ 构建Qwen实例（带配置/默认值）
- ✅ 不支持的平台错误处理
- ✅ 平台名称大小写不敏感
- ✅ 所有支持平台的参数化测试

**主要测试方法**:
- `test_factory_init_with_config()` - 测试工厂初始化
- `test_build_openai_with_config()` - 测试构建OpenAI实例
- `test_build_deepseek()` - 测试构建DeepSeek实例
- `test_build_qwen()` - 测试构建Qwen实例
- `test_build_unsupported_platform()` - 测试不支持的平台
- `test_all_supported_platforms()` - 参数化测试所有平台

### 2. AI Manager 测试 (`test_manager.py`)

**测试类**: `TestAIManager`

**测试覆盖**:
- ✅ 管理器初始化
- ✅ 通过路由处理文本请求
- ✅ 通过路由处理图像请求
- ✅ 图像请求缺少URL的错误处理
- ✅ 无效任务类型的错误处理
- ✅ 消息构建（文本/图像）
- ✅ Agent处理（成功/空响应/异常）
- ✅ 流式Agent处理（成功/异常）
- ✅ 配置验证

**主要测试方法**:
- `test_handle_by_router_text()` - 测试文本请求处理
- `test_handle_by_router_image()` - 测试图像请求处理
- `test_handle_agent_success()` - 测试Agent成功处理
- `test_handle_agent_stream_success()` - 测试流式Agent处理
- `test_build_messages_text()` - 测试文本消息构建
- `test_build_messages_image()` - 测试图像消息构建

### 3. AI Router 测试 (`test_router.py`)

**测试类**: `TestRouter`

**测试覆盖**:
- ✅ 路由器初始化（带配置/无配置）
- ✅ 文本任务处理（OpenAI/DeepSeek）
- ✅ 图像任务处理（OpenAI）
- ✅ 不支持的平台错误处理
- ✅ 不支持的任务类型错误处理
- ✅ 平台覆盖和默认值
- ✅ 模型配置优先级
- ✅ 大小写不敏感处理
- ✅ 所有支持组合的参数化测试

**主要测试方法**:
- `test_handle_text_openai()` - 测试OpenAI文本处理
- `test_handle_text_deepseek()` - 测试DeepSeek文本处理
- `test_handle_image_openai()` - 测试OpenAI图像处理
- `test_handle_unsupported_task_type()` - 测试不支持的任务类型
- `test_handle_all_supported_combinations()` - 参数化测试所有组合

## 运行测试

### 方法1: 使用测试运行器

```bash
# 运行所有AI模块测试
python tests/unit/ai/model/test_runner.py

# 运行特定模块测试
python tests/unit/ai/model/test_runner.py --module factory
python tests/unit/ai/model/test_runner.py --module manager
python tests/unit/ai/model/test_runner.py --module router
```

### 方法2: 直接使用pytest

```bash
# 运行所有AI模块测试
pytest tests/unit/ai/model/ -v

# 运行特定测试文件
pytest tests/unit/ai/model/test_factory.py -v
pytest tests/unit/ai/model/test_manager.py -v
pytest tests/unit/ai/model/test_router.py -v

# 运行特定测试类
pytest tests/unit/ai/model/test_factory.py::TestFactory -v
pytest tests/unit/ai/model/test_manager.py::TestAIManager -v
pytest tests/unit/ai/model/test_router.py::TestRouter -v

# 运行特定测试方法
pytest tests/unit/ai/model/test_factory.py::TestFactory::test_build_openai_with_config -v
```

### 方法3: 带覆盖率报告

```bash
# 运行测试并生成覆盖率报告
pytest tests/unit/ai/model/ -v --cov=ai --cov-report=term-missing

# 生成HTML覆盖率报告
pytest tests/unit/ai/model/ -v --cov=ai --cov-report=html
```

## 测试依赖

确保安装了以下依赖：

```bash
pip install pytest pytest-cov pytest-mock
```

## 测试最佳实践

1. **Mock外部依赖**: 所有测试都使用Mock来隔离外部依赖（如OpenAI API）
2. **参数化测试**: 使用`@pytest.mark.parametrize`来测试多种输入组合
3. **边界条件**: 测试异常情况和边界条件
4. **配置验证**: 验证配置参数的正确传递
5. **错误处理**: 测试各种错误情况的处理

## 测试数据

测试中使用的模拟数据：
- API密钥: "test_key"
- 模型名称: "gpt-4", "deepseek-chat", "qwen-turbo"
- 温度值: 0.3, 0.5, 0.7, 0.9
- 业务ID: "test_unique_id", "test_user_id"

## 注意事项

1. 所有测试都是单元测试，不依赖外部服务
2. 使用Mock来模拟LangChain组件和外部API调用
3. 测试覆盖了正常流程和异常情况
4. 测试代码遵循项目的编码规范
5. 每个测试方法都有清晰的中文注释说明

## 扩展测试

如需添加新的测试用例：

1. 在对应的测试类中添加新的测试方法
2. 使用描述性的方法名和中文注释
3. 确保测试覆盖正常流程和异常情况
4. 运行测试确保通过
5. 更新本文档的测试覆盖范围 