import pytest
from unittest.mock import patch, MagicMock
from langchain_core.messages import HumanMessage
from ai.manager.ai_manager import AIManager
from ai.config.ai_enum import TaskTypeEnum, AIPlatformEnum
from ai.config.ai_config import FactoryConfig, ModelConfig, RouterConfig


class TestAIManager:
    """AI Manager 测试类"""

    def test_manager_init(self):
        """测试管理器初始化"""
        manager = AIManager()
        assert manager is not None

    @patch('ai.manager.ai_manager.Router')
    def test_handle_by_router_text(self, mock_router_class):
        """测试通过路由处理文本请求"""
        manager = AIManager()
        message = "Hello, how are you?"
        
        # 模拟Router实例
        mock_router = MagicMock()
        mock_ai_instance = MagicMock()
        mock_ai_instance.invoke.return_value = "AI response"
        mock_router.handle.return_value = mock_ai_instance
        mock_router_class.return_value = mock_router
        
        result = manager.handle_by_router(
            message=message,
            task_type=TaskTypeEnum.TEXT,
            platform="openai"
        )
        
        assert result == "AI response"
        mock_router_class.assert_called_once()
        mock_router.handle.assert_called_once()

    @patch('ai.manager.ai_manager.Router')
    def test_handle_by_router_image(self, mock_router_class):
        """测试通过路由处理图像请求"""
        manager = AIManager()
        message = "Describe this image"
        image_url = "http://example.com/image.jpg"
        
        # 模拟Router实例
        mock_router = MagicMock()
        mock_ai_instance = MagicMock()
        mock_ai_instance.invoke.return_value = "Image description"
        mock_router.handle.return_value = mock_ai_instance
        mock_router_class.return_value = mock_router
        
        result = manager.handle_by_router(
            message=message,
            task_type=TaskTypeEnum.IMAGE,
            platform="openai",
            image_url=image_url
        )
        
        assert result == "Image description"
        mock_router_class.assert_called_once()
        mock_router.handle.assert_called_once()

    def test_handle_by_router_image_without_url(self):
        """测试图像请求但没有提供URL"""
        manager = AIManager()
        
        with pytest.raises(ValueError, match="Params empty: image_url"):
            manager.handle_by_router(
                message="Describe this image",
                task_type=TaskTypeEnum.IMAGE,
                platform="openai"
            )

    @pytest.mark.parametrize("task_type", ["unsupported"])
    def test_handle_by_router_invalid_task_type(self, task_type):
        """测试无效的任务类型"""
        manager = AIManager()
        
        with pytest.raises(ValueError, match=f"Unsupported task_type: {task_type}"):
            manager.handle_by_router(
                message="test message",
                task_type=task_type,
                platform="openai"
            )

    def test_handle_by_router_invalid_task_type_int(self):
        """测试无效的任务类型（整数）"""
        manager = AIManager()
        
        with pytest.raises(Exception):  # 捕获ValidationError
            manager.handle_by_router(
                message="test message",
                task_type=123,
                platform="openai"
            )

    def test_handle_by_router_invalid_task_type_none(self):
        """测试无效的任务类型（None）"""
        manager = AIManager()
        
        with pytest.raises(Exception):  # 捕获ValidationError
            manager.handle_by_router(
                message="test message",
                task_type=None,
                platform="openai"
            )

    def test_build_messages_text(self):
        """测试构建文本消息"""
        manager = AIManager()
        message = "Hello world"
        
        messages = manager._build_messages(message, TaskTypeEnum.TEXT)
        
        assert len(messages) == 1
        assert isinstance(messages[0], HumanMessage)
        assert messages[0].content == message

    def test_build_messages_image(self):
        """测试构建图像消息"""
        manager = AIManager()
        message = "Describe this image"
        image_url = "http://example.com/image.jpg"
        
        messages = manager._build_messages(message, TaskTypeEnum.IMAGE, image_url)
        
        assert len(messages) == 1
        assert isinstance(messages[0], HumanMessage)
        assert len(messages[0].content) == 2
        assert messages[0].content[0]["type"] == "text"
        assert messages[0].content[0]["text"] == message
        assert messages[0].content[1]["type"] == "image_url"
        assert messages[0].content[1]["image_url"]["url"] == image_url

    def test_build_messages_image_invalid_type(self):
        """测试构建图像消息时无效的任务类型"""
        manager = AIManager()
        
        with pytest.raises(ValueError, match="Unsupported task_type: invalid"):
            manager._build_messages("test", "invalid", "http://example.com/image.jpg")

    @patch('ai.manager.ai_manager.Router')
    @patch('ai.manager.ai_manager.ReactAgent')
    def test_handle_agent_success(self, mock_agent_class, mock_router_class):
        """测试处理agent请求成功"""
        manager = AIManager()
        biz_unique_id = "test_unique_id"
        biz_user_id = "test_user_id"
        message = "What's the weather like?"
        
        # 模拟Router
        mock_router = MagicMock()
        mock_ai_instance = MagicMock()
        mock_router.handle.return_value = mock_ai_instance
        mock_router_class.return_value = mock_router
        
        # 模拟ReactAgent
        mock_agent = MagicMock()
        mock_response = {
            "messages": [
                MagicMock(content="Weather is sunny"),
                MagicMock(content="AI response content")
            ]
        }
        mock_agent.run.return_value = mock_response
        mock_agent_class.return_value = mock_agent
        
        result = manager.handle_agent(biz_unique_id, biz_user_id, message)
        
        assert result == "AI response content"
        mock_agent_class.assert_called_once()
        mock_agent.run.assert_called_once_with(message)

    @patch('ai.manager.ai_manager.Router')
    @patch('ai.manager.ai_manager.ReactAgent')
    def test_handle_agent_empty_response(self, mock_agent_class, mock_router_class):
        """测试处理agent请求返回空响应"""
        manager = AIManager()
        biz_unique_id = "test_unique_id"
        biz_user_id = "test_user_id"
        message = "What's the weather like?"
        
        # 模拟Router
        mock_router = MagicMock()
        mock_ai_instance = MagicMock()
        mock_router.handle.return_value = mock_ai_instance
        mock_router_class.return_value = mock_router
        
        # 模拟ReactAgent返回空响应
        mock_agent = MagicMock()
        mock_agent.run.return_value = {"messages": []}
        mock_agent_class.return_value = mock_agent
        
        result = manager.handle_agent(biz_unique_id, biz_user_id, message)
        
        assert result is None

    @patch('ai.manager.ai_manager.Router')
    @patch('ai.manager.ai_manager.ReactAgent')
    def test_handle_agent_no_messages(self, mock_agent_class, mock_router_class):
        """测试处理agent请求没有消息"""
        manager = AIManager()
        biz_unique_id = "test_unique_id"
        biz_user_id = "test_user_id"
        message = "What's the weather like?"
        
        # 模拟Router
        mock_router = MagicMock()
        mock_ai_instance = MagicMock()
        mock_router.handle.return_value = mock_ai_instance
        mock_router_class.return_value = mock_router
        
        # 模拟ReactAgent返回无消息的响应
        mock_agent = MagicMock()
        mock_agent.run.return_value = {}
        mock_agent_class.return_value = mock_agent
        
        result = manager.handle_agent(biz_unique_id, biz_user_id, message)
        
        assert result is None

    @patch('ai.manager.ai_manager.Router')
    @patch('ai.manager.ai_manager.ReactAgent')
    def test_handle_agent_exception(self, mock_agent_class, mock_router_class):
        """测试处理agent请求异常"""
        manager = AIManager()
        biz_unique_id = "test_unique_id"
        biz_user_id = "test_user_id"
        message = "What's the weather like?"
        
        # 模拟Router
        mock_router = MagicMock()
        mock_ai_instance = MagicMock()
        mock_router.handle.return_value = mock_ai_instance
        mock_router_class.return_value = mock_router
        
        # 模拟ReactAgent抛出异常
        mock_agent = MagicMock()
        mock_agent.run.side_effect = Exception("Agent error")
        mock_agent_class.return_value = mock_agent
        
        with pytest.raises(Exception, match="Agent error"):
            manager.handle_agent(biz_unique_id, biz_user_id, message)

    @patch('ai.manager.ai_manager.Router')
    @patch('ai.manager.ai_manager.ReactAgent')
    def test_handle_agent_stream_success(self, mock_agent_class, mock_router_class):
        """测试流式处理agent请求成功"""
        manager = AIManager()
        biz_unique_id = "test_unique_id"
        biz_user_id = "test_user_id"
        message = "What's the weather like?"
        
        # 模拟Router
        mock_router = MagicMock()
        mock_ai_instance = MagicMock()
        mock_router.handle.return_value = mock_ai_instance
        mock_router_class.return_value = mock_router
        
        # 模拟ReactAgent流式响应
        mock_agent = MagicMock()
        mock_chunks = ["Hello", " ", "world", "!"]
        mock_agent.run_stream.return_value = mock_chunks
        mock_agent_class.return_value = mock_agent
        
        result_generator = manager.handle_agent_stream(biz_unique_id, biz_user_id, message)
        result_list = list(result_generator)
        
        assert result_list == mock_chunks
        mock_agent_class.assert_called_once()
        mock_agent.run_stream.assert_called_once_with(message)

    @patch('ai.manager.ai_manager.Router')
    @patch('ai.manager.ai_manager.ReactAgent')
    def test_handle_agent_stream_exception(self, mock_agent_class, mock_router_class):
        """测试流式处理agent请求异常"""
        manager = AIManager()
        biz_unique_id = "test_unique_id"
        biz_user_id = "test_user_id"
        message = "What's the weather like?"
        
        # 模拟Router
        mock_router = MagicMock()
        mock_ai_instance = MagicMock()
        mock_router.handle.return_value = mock_ai_instance
        mock_router_class.return_value = mock_router
        
        # 模拟ReactAgent流式响应抛出异常
        mock_agent = MagicMock()
        mock_agent.run_stream.side_effect = Exception("Stream error")
        mock_agent_class.return_value = mock_agent
        
        with pytest.raises(Exception, match="Stream error"):
            list(manager.handle_agent_stream(biz_unique_id, biz_user_id, message))

    @patch('ai.manager.ai_manager.Router')
    @patch('ai.manager.ai_manager.ReactAgent')
    def test_handle_agent_config_verification(self, mock_agent_class, mock_router_class):
        """测试agent配置验证"""
        manager = AIManager()
        biz_unique_id = "test_unique_id"
        biz_user_id = "test_user_id"
        message = "What's the weather like?"
        
        # 模拟Router
        mock_router = MagicMock()
        mock_ai_instance = MagicMock()
        mock_router.handle.return_value = mock_ai_instance
        mock_router_class.return_value = mock_router
        
        # 模拟ReactAgent
        mock_agent = MagicMock()
        mock_response = {"messages": [MagicMock(content="Response")]}
        mock_agent.run.return_value = mock_response
        mock_agent_class.return_value = mock_agent
        
        manager.handle_agent(biz_unique_id, biz_user_id, message)
        
        # 验证Router配置
        mock_router_class.assert_called_once_with(RouterConfig())
        
        # 验证Router.handle被调用
        mock_router.handle.assert_called_once()
        
        # 验证ReactAgent配置
        mock_agent_class.assert_called_once()
        agent_call_args = mock_agent_class.call_args[1]
        assert agent_call_args["model"] == mock_ai_instance
        assert agent_call_args["store_type"] == "postgres"
        assert agent_call_args["system_prompt"] == "You are a weather specialist."
        assert agent_call_args["thread_id"] == biz_unique_id
        assert agent_call_args["biz_user_id"] == biz_user_id 