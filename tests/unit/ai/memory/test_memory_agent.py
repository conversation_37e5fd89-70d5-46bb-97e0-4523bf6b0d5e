import concurrent
import unittest
import os
import unittest
from datetime import datetime

from langchain_core.tools import tool
from langsmith import traceable

from ai.agent.react_agent import ReactAgent
from ai.config.ai_enum import AIPlatformEnum
from ai.router import Router
from ai.config.ai_config import RouterConfig, FactoryConfig, ModelConfig
from ai.config.ai_properties import AIProperties
from ai.tools.apt_slot_tool import get_apt_slot, recommend_slot
from ai.tools.weather import get_weather
from utils.biz_memory import BizMemory
from utils.biz_time import biz_format_time_ch
from utils.biz_logger import get_logger
from dotenv import load_dotenv
logger = get_logger(__name__)

class TestMemoryAgent(unittest.TestCase):
    def setUp(self) -> None:
        load_dotenv()
        logger.info("SET_UP: %s", biz_format_time_ch(datetime.now()))
        logger.info("PG_URI: %s", os.getenv("POSTGRES_URI"))
        ai_router = Router(RouterConfig())
        factory_config = FactoryConfig()
        factory_config.platform = AIPlatformEnum.OPENAI
        self.model = ai_router.handle(factory_config=factory_config, model_config=ModelConfig())

    def test_mem_agent(self):
        logger.info("test_mem_agent")
        agent = ReactAgent(
            model=self.model,
            tools=[],
            system_prompt="You are a health specialist.",
            store_type="postgres",
            history_strategy="trim",
            thread_id="100002",
            biz_user_id="10002"
        )
        user_input = "我是一名糖尿病患者"
        # user_input = "我现在正在服用门东胰岛素"
        # user_input = "我如何控制血糖"
        # ai_res = await agent.run_sync(user_input)
        ai_res = agent.run(user_input)
        logger.info(f"ai_res: {ai_res}")
        if not ai_res:
            assert False
        print(ai_res["messages"][-1].pretty_print())

    def test_mem_agent_add(self):
        logger.info("test_mem_agent_add")
        agent = ReactAgent(
            model=self.model,
            tools=[],
            system_prompt="You are a health specialist.",
            store_type="postgres",
            history_strategy="trim",
            thread_id="100002",
            biz_user_id="10002"
        )
        user_input = "我今天早上跑步 42KM"
        memory_messages = [{"role": "user", "content": user_input}]
        future = agent.create_memories(memory_messages)
        # if isinstance(future, concurrent.futures.Future):
        #     try:
        #         future.result()  # 等待任务完成
        #         logger.info(f"Memory creation completed. {future.result()}")
        #     except Exception as e:
        #         logger.error(f"Memory creation failed: {e}")

        # 等待所有异步任务完成
        if hasattr(agent, 'executor') and isinstance(agent.executor, concurrent.futures.ThreadPoolExecutor):
            agent.executor.shutdown(wait=True)


