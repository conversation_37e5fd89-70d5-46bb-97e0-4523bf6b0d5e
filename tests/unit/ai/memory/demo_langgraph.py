# demo_langgraph
#
# @author: 崔晓波
# @email: <EMAIL>
# @date: 2025/7/29 11:01
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
import json
import os
import time
import uuid

from dotenv import load_dotenv

from typing import Annotated, TypedDict, List

from langchain.embeddings import init_embeddings
from langgraph.graph import StateGraph, START
from langgraph.graph.message import add_messages
from langchain_openai import ChatOpenAI
from langgraph.store.base import BaseStore
from langgraph.store.postgres import PostgresStore
from langsmith import traceable
from mem0 import MemoryClient
from mem0 import Memory
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage, BaseMessage
from utils.biz_logger import get_logger

# Load environment variables
load_dotenv()

logger = get_logger(__name__)

# Configuration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
MEM0_API_KEY = os.getenv("MEM0_API_KEY")

# print(f"OPENAI_API_KEY: {OPENAI_API_KEY}")
# print(f"MEM0_API_KEY: {MEM0_API_KEY}")

# Initialize LangChain and Mem0
llm = ChatOpenAI(model="gpt-4.1-mini", api_key=OPENAI_API_KEY)


# llm = ChatOllama(model="mistral:7b", api_key=OPENAI_API_KEY)


class State(TypedDict):
    messages: Annotated[List[HumanMessage | AIMessage], add_messages]
    mem0_user_id: str
    mem0_agent_id: str


uri = os.getenv("POSTGRES_URI")

if __name__ == "__main__":
    print("Welcome to Customer Support! How can I assist you today?")
    mem0_user_id = "customer_test_0031"
    mem0_agent_id = "agent_001"
    with PostgresStore.from_conn_string(
            uri,
            index={
                "dims": 768,
                "embed": init_embeddings(model="nomic-embed-text", provider="ollama"),
                "fields": ["content"]
            }
    ) as store:
        try:
            store.setup()
        except Exception as e:
            print(f"Error: {e}")

        def chatbot(state: State, store: BaseStore):
            messages = state["messages"]
            user_id = state["mem0_user_id"]
            agent_id = state["mem0_agent_id"]

            # Retrieve relevant memories
            user_input = messages[-1].content
            sys_use_time_search_start = time.time()
            # Search memories
            namespace = ("user_id", user_id)
            memories_res = store.search(
                namespace,
                query=user_input,
                limit=3
            )

            sys_use_time_search_end = time.time()
            sys_use_time_search = sys_use_time_search_end - sys_use_time_search_start
            logger.info(f"Mem search time: {sys_use_time_search:.2f} seconds")
            logger.info(f"Mem data: {memories_res}")

            context = "Relevant information from previous conversations:\n"
            for memory in memories_res:
                context += f"- {memory.value}\n"

            # Add system message
            system_message = SystemMessage(
                content=f"""You are a helpful customer support assistant.\n         Use the provided context to personalize your responses and remember user preferences and past interactions.\n    {context}""")

            full_messages = [system_message] + messages
            sys_use_time_llm_start = time.time()
            response = llm.invoke(full_messages)
            sys_use_time_llm_end = time.time()
            sys_use_time_llm = sys_use_time_llm_end - sys_use_time_llm_start
            logger.info(f"LLM time: {sys_use_time_llm:.2f} seconds")

            # Store the interaction in Mem0
            try:
                mem0_dict = {"role": "user", "content": messages[0].content}
                logger.info(f"START_CREATE_MEM: {time.time()}")
                # trans list to dict

                # Add memory
                sys_use_time_create_start = time.time()
                store.put(namespace, str(uuid.uuid4()), mem0_dict)

                sys_use_time_create_end = time.time()
                sys_use_time_create = sys_use_time_create_end - sys_use_time_create_start
                logger.info(f"Mem create time: {sys_use_time_create:.2f} seconds")

                return {"messages": [response]}
            except Exception as e:
                logger.error(f"Mem create error: {e}")
                return {"messages": [
                    AIMessage(
                        content="Sorry, I'm having trouble understanding your question. Please try again later.")]}


        graph = StateGraph(State)
        graph.add_node("chatbot", chatbot)
        graph.add_edge(START, "chatbot")
        graph.add_edge("chatbot", "chatbot")
        compiled_graph = graph.compile(store=store)


        @traceable
        def run_conversation(user_input: str, mem0_user_id: str, mem0_agent_id: str, store: BaseStore):
            config = {"configurable": {"thread_id": mem0_user_id}}
            state = {"messages": [HumanMessage(content=user_input)], "mem0_user_id": mem0_user_id,
                     "mem0_agent_id": mem0_agent_id}

            for event in compiled_graph.stream(state, config):
                for value in event.values():
                    if value.get("messages"):
                        print("AI:", value["messages"][-1].content)
                        print("\n")
                        return


        while True:
            user_input = input("You: ")
            if user_input.lower() in ['quit', 'exit', 'bye']:
                print("AI: Thank you for contacting us. Have a great day!")
                break
            run_conversation(user_input, mem0_user_id, mem0_agent_id, store)


def messages_to_dict_list(messages: list[BaseMessage]) -> list[dict]:
    """
    将 LangGraph/LangChain 消息对象列表转换为 role-content 字典列表。
    """
    result = []
    for msg in messages:
        if isinstance(msg, HumanMessage):
            role = "user"
        elif isinstance(msg, AIMessage):
            role = "assistant"
        elif isinstance(msg, SystemMessage):
            role = "system"
        else:
            role = "unknown"
        result.append({"role": role, "content": msg.content})
    return result
