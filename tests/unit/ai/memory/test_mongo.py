# test_mongo
#
# @author: 崔晓波
# @email: <EMAIL>
# @date: 2025/6/24 16:04
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError, ConfigurationError
from pymongo.read_preferences import ReadPreference

from pymongo import MongoClient

# 构造连接 URI，指定副本集名称为 dbrs
uri = "mongodb://localhost"

client = MongoClient(uri, 27017)

try:
    dbs = client.list_database_names()
    print("✅ 成功连接副本集，数据库列表：", dbs)
except Exception as e:
    print("❌ 连接失败：", e)


