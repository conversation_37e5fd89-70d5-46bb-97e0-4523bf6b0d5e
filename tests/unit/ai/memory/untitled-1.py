import psycopg2

class PGVectorStore:
    def __init__(self, conn):
        self.conn = conn
        self.cur = self.conn.cursor()

    def list(self, filters, limit):
        try:
            # 构建 SQL 查询语句
            query = "SELECT * FROM your_table WHERE ... LIMIT %s"
            filter_params = [...]  # 根据 filters 构建参数
            self.cur.execute(query, (*filter_params, limit))
            result = self.cur.fetchall()
            self.conn.commit()  # 提交事务
            return result
        except psycopg2.Error as e:
            print(f"SQL error: {e}")
            self.conn.rollback()  # 回滚事务
            raise
