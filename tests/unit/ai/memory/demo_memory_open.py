# test_react_agent
#
# @author: 崔晓波
# @email: <EMAIL>
# @date: 2025/7/15 15:01
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
import os
import time

from dotenv import load_dotenv

from typing import Annotated, TypedDict, List
from langgraph.graph import StateGraph, START
from langgraph.graph.message import add_messages
from langchain_openai import ChatOpenAI
from langsmith import traceable
from mem0 import MemoryClient
from mem0 import Memory
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage, BaseMessage
from utils.biz_logger import get_logger

# Load environment variables
load_dotenv()

logger = get_logger(__name__)

# Configuration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
MEM0_API_KEY = os.getenv("MEM0_API_KEY")

print(f"OPENAI_API_KEY: {OPENAI_API_KEY}")
print(f"MEM0_API_KEY: {MEM0_API_KEY}")

# Initialize Lang<PERSON>hain and Mem0
llm = ChatOpenAI(model="gpt-4.1-mini", api_key=OPENAI_API_KEY)
# llm = ChatOllama(model="mistral:7b", api_key=OPENAI_API_KEY)

def build_mem0():
    config = {
        # "llm": {
        #     "provider": "ollama",
        #     "config": {
        #         "model": "mistral:7b",
        #         "temperature": 0.1,
        #         "max_tokens": 2000,
        #         "ollama_base_url": "https://evyd-ai-deepseek.evyd.tech"
        #     }
        # },
        "embedder": {
            "provider": "ollama",
            "config": {
                "model": "nomic-embed-text",
                "embedding_dims": 768,
                "ollama_base_url": "https://evyd-ai-deepseek.evyd.tech"
            }
        },
        "vector_store": {
            "provider": "pgvector",
            "config": {
                "user": "postgres",
                "password": "EVYD_AI_TEAM",
                "host": "127.0.0.1",
                "port": "5432",
                "embedding_model_dims": 768
            }
        }
    }
    return Memory.from_config(config)

# mem0 = MemoryClient(api_key=MEM0_API_KEY)
mem0 = build_mem0()

class State(TypedDict):
    messages: Annotated[List[HumanMessage | AIMessage], add_messages]
    mem0_user_id: str
    mem0_agent_id: str

graph = StateGraph(State)


def chatbot(state: State):
    messages = state["messages"]
    user_id = state["mem0_user_id"]
    agent_id = state["mem0_agent_id"]

    # Retrieve relevant memories
    sys_use_time_search_start = time.time()
    # Search memories
    memories_res = mem0.search(
        messages[-1].content,
        user_id=user_id,
        limit=3,
        threshold=0.4
    )

    memories = memories_res["results"] if memories_res else []
    sys_use_time_search_end = time.time()
    sys_use_time_search = sys_use_time_search_end - sys_use_time_search_start
    logger.info(f"Mem0 search time: {sys_use_time_search:.2f} seconds")
    logger.info(f"Mem0 data: {memories}")

    context = "Relevant information from previous conversations:\n"
    for memory in memories:
        context += f"- {memory['memory']}\n"


    # Add system message
    system_message = SystemMessage(content=f"""You are a helpful customer support assistant.
     Use the provided context to personalize your responses and remember user preferences and past interactions.
{context}""")

    full_messages = [system_message] + messages
    sys_use_time_llm_start = time.time()
    response = llm.invoke(full_messages)
    sys_use_time_llm_end = time.time()
    sys_use_time_llm = sys_use_time_llm_end - sys_use_time_llm_start
    logger.info(f"LLM time: {sys_use_time_llm:.2f} seconds")

    # Store the interaction in Mem0
    try:
        mem0_list = messages_to_dict_list(messages) + messages_to_dict_list([response])
        logger.info(f"START_CREATE_MEM: {time.time()}")
        sys_use_time_create_start = time.time()

        # Add memory
        mem0.add(mem0_list, user_id=user_id, agent_id=agent_id)

        # Update memory
        # sys_use_time_update_start = time.time()
        # memory_id = "8c007f1f-3064-4490-a0d8-c4b12c357ab2"
        # mem0.update(memory_id, data = "I am 170cm updated")
        # sys_use_time_update_end = time.time()
        # sys_use_time_update = sys_use_time_update_end - sys_use_time_update_start
        # logger.info(f"Mem0 update time: {sys_use_time_update:.2f} seconds")
        #
        # # Delete memory
        # sys_use_time_delete_start = time.time()
        # memory_id = "8c007f1f-3064-4490-a0d8-c4b12c357ab2"
        # mem0.delete(memory_id)
        # sys_use_time_delete_end = time.time()
        # sys_use_time_delete = sys_use_time_delete_end - sys_use_time_delete_start
        # logger.info(f"Mem0 delete time: {sys_use_time_delete:.2f} seconds")

        sys_use_time_create_end = time.time()
        sys_use_time_create = sys_use_time_create_end - sys_use_time_create_start
        logger.info(f"Mem0 create time: {sys_use_time_create:.2f} seconds")
        return {"messages": [response]}
    except Exception as e:
        logger.error(f"Mem0 create error: {e}")
        return {"messages": [
            AIMessage(content="Sorry, I'm having trouble understanding your question. Please try again later.")]}

graph.add_node("chatbot", chatbot)
graph.add_edge(START, "chatbot")
graph.add_edge("chatbot", "chatbot")

compiled_graph = graph.compile()

def messages_to_dict_list(messages: list[BaseMessage]) -> list[dict]:
    """
    将 LangGraph/LangChain 消息对象列表转换为 role-content 字典列表。
    """
    result = []
    for msg in messages:
        if isinstance(msg, HumanMessage):
            role = "user"
        elif isinstance(msg, AIMessage):
            role = "assistant"
        elif isinstance(msg, SystemMessage):
            role = "system"
        else:
            role = "unknown"
        result.append({"role": role, "content": msg.content})
    return result

@traceable
def run_conversation(user_input: str, mem0_user_id: str, mem0_agent_id: str):
    config = {"configurable": {"thread_id": mem0_user_id}}
    state = {"messages": [HumanMessage(content=user_input)], "mem0_user_id": mem0_user_id, "mem0_agent_id": mem0_agent_id}

    for event in compiled_graph.stream(state, config):
        for value in event.values():
            if value.get("messages"):
                print("AI:", value["messages"][-1].content)
                return

if __name__ == "__main__":
    print("Welcome to Customer Support! How can I assist you today?")
    mem0_user_id = "customer_test_0010"
    mem0_agent_id = "agent_001"
    while True:
        user_input = input("You: ")
        if user_input.lower() in ['quit', 'exit', 'bye']:
            print("AI: Thank you for contacting us. Have a great day!")
            break
        run_conversation(user_input, mem0_user_id, mem0_agent_id)
