# test_memory
#
# @author: 崔晓波
# @email: <EMAIL>
# @date: 2025/6/24 14:30
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
import datetime
import os
import unittest
import uuid

from dotenv import load_dotenv
from langchain_community.storage import MongoDBStore
from langgraph.checkpoint.redis import RedisSaver
# from langgraph.store.redis import MongoDBStore
from langgraph.store.redis import RedisStore
# from langgraph.checkpoint.mysql import BaseMySQLSaver
# from langgraph.store.mysql import PyMySQLStore
from langsmith import traceable
from langgraph.checkpoint.postgres import PostgresSaver
from langgraph.checkpoint.mongodb import MongoDBSaver
from psycopg.errors import DuplicateColumn
from sqlalchemy.dialects.postgresql import psycopg

from utils.biz_logger import get_logger
logger = get_logger(__name__)


class TestMemoryLong(unittest.TestCase):
    def setUp(self) -> None:
        load_dotenv()
        logger.info("SET_UP: %s", datetime.datetime.now())

    def test(self):
        model_name = os.environ.get("OPENAI_MODEL")
        logger.info("MODEL_NAME: %s", model_name)
        # response = do_test()
        response = do_test_pg()
        #  断言没有异常
        self.assertIsNone(response)

if __name__ == '__main__':
    unittest.main()

@traceable
def do_test():
    from langchain.chat_models import init_chat_model
    from langgraph.graph import StateGraph, MessagesState, START
    from langgraph.checkpoint.memory import InMemorySaver
    import uuid
    from typing_extensions import Annotated, TypedDict

    from langchain_core.runnables import RunnableConfig
    from langgraph.graph import StateGraph, MessagesState, START
    from langgraph.checkpoint.memory import InMemorySaver

    from langgraph.store.memory import InMemoryStore
    from langgraph.store.base import BaseStore


    model = init_chat_model(model='openai:gpt-4.1', api_key='***************************************************')

    # Store to DB
    # DB_URI="mongodb://127.0.0.1:27017"
    DB_URI_REDIS="redis://localhost:7379"
    DB_URI_MYSQL="localhost:3306"
    DB_NAME="evo-mind"
    COLLECTION_NAME="test-memory"

    # with (
    #     RedisStore.from_conn_string(DB_URI_REDIS) as store,
    #     RedisSaver.from_conn_string(DB_URI_REDIS) as checkpointer,
    # ):
    with (
        RedisStore.from_conn_string(DB_URI_REDIS) as store,
        RedisSaver.from_conn_string(DB_URI_REDIS) as checkpointer,
    ):
        store.setup()
        checkpointer.setup()

        def call_model(
                state: MessagesState,
                config: RunnableConfig,
                *,
                store: BaseStore,
        ):
            user_id = config["configurable"]["user_id"]
            namespace = ("memories", user_id)
            # memories = store.search(namespace, query=str(state["messages"][-1].content))
            memories = store.get(namespace, str(uuid.uuid4()))
            logger.info(f"MEM_LIST: {memories}")
            # memories = store.get(namespace, str(uuid.uuid4()))
            info = "\n".join([d.value["data"] for d in memories])
            system_msg = f"You are a helpful assistant talking to the user. User info: {info}"

            # Store new memories if the user asks the model to remember
            last_message = state["messages"][-1]
            if "remember" in last_message.content.lower():
                memory = "User name is Bob"
                store.put(namespace, str(uuid.uuid4()), {"data": memory})

            response = model.invoke(
                [{"role": "system", "content": system_msg}] + state["messages"]
            )
            return {"messages": response}

        builder = StateGraph(MessagesState)
        builder.add_node(call_model)
        builder.add_edge(START, "call_model")

        graph = builder.compile(
            checkpointer=checkpointer,
            store=store,
        )

        config = {
            "configurable": {
                "thread_id": "1",
                "user_id": "1",
            }
        }
        logger.info(f"CONFIG_1: %s", graph.get_state(config))
        for chunk in graph.stream(
                {"messages": [{"role": "user", "content": "Hi! Remember: my name is Bob"}]},
                config,
                stream_mode="values",
        ):
            chunk["messages"][-1].pretty_print()

        config = {
            "configurable": {
                "thread_id": "2",
                "user_id": "1",
            }
        }

        print("CONFIG_2: ", graph.get_state(config))
        for chunk in graph.stream(
                {"messages": [{"role": "user", "content": "what is my name?"}]},
                config,
                stream_mode="values",
        ):
            chunk["messages"][-1].pretty_print()


@traceable
def do_test_pg():
    from langchain_core.runnables import RunnableConfig
    from langchain.chat_models import init_chat_model
    from langgraph.graph import StateGraph, MessagesState, START
    from langgraph.checkpoint.postgres import PostgresSaver
    from langgraph.store.postgres import PostgresStore
    from langgraph.store.base import BaseStore
    from langchain_core.messages.utils import (
        trim_messages,
        count_tokens_approximately
    )

    model = init_chat_model(model='openai:gpt-4.1', api_key='***************************************************')
    # Summarization length
    summarization_model = model.bind(max_tokens=128)

    # Store to DB
    # DB_URI="mongodb://127.0.0.1:27017"
    DB_URI="postgresql://cuixiaobo:@localhost:5432/postgres?sslmode=disable"

    with (
        PostgresStore.from_conn_string(DB_URI) as store,
        PostgresSaver.from_conn_string(DB_URI) as checkpointer,
    ):
        try:
            store.setup()
            checkpointer.setup()
        except DuplicateColumn as e:
            logger.warning(f"Column already exists in database: {e}")
        except Exception as e:
            logger.error(f"Failed to setup database: {e}")
            raise

        def call_model(
                state: MessagesState,
                config: RunnableConfig,
                *,
                store: BaseStore,
        ):
            # Trim messages
            messages = trim_messages(
                state["messages"],
                strategy="last",
                token_counter=count_tokens_approximately,
                max_tokens=10,
                start_on="human",
                end_on=("human", "tool"),
            )
            if not messages:
                logger.info("Trimmed messages are empty")
                messages = state["messages"][-1:]
            logger.info(f"SOURCE_MSG: {state["messages"]}")
            logger.info(f"TRIM_MSG: {messages}")
            user_id = config["configurable"]["user_id"]
            namespace = ("memories", user_id)
            # TODO: 长期记忆长度控制
            memories = store.search(namespace, query=str(messages[-1].content))
            logger.info(f"MEM_LIST: {memories}")
            info = "\n".join([d.value["data"] for d in memories])
            system_msg = f"You are a helpful assistant talking to the user. User info: {info}"

            # Store new memories if the user asks the model to remember
            # last_message = state["messages"][-1]
            last_message = messages[-1]
            if "remember" in last_message.content.lower():
                memory = "User name is Bob" + datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                store.put(namespace, str(uuid.uuid4()), {"data": memory})

            response = model.invoke(
                # [{"role": "system", "content": system_msg}] + state["messages"]
                [{"role": "system", "content": system_msg}] + messages
            )
            return {"messages": response}

        builder = StateGraph(MessagesState)
        builder.add_node(call_model)
        builder.add_edge(START, "call_model")

        graph = builder.compile(
            checkpointer=checkpointer,
            store=store,
        )

        config = {
            "configurable": {
                "thread_id": "1",
                "user_id": "1",
            }
        }
        long_input = "Larry loved avocados more than anything—more than pizza, more than naps, even more than his cat, Sir Whiskerstein. So when the grocery store raised avocado prices, Larry took action.-He dressed in full ninja gear (including socks with little rubber ducks) and crept into the store at 2 a.m. His brilliant plan? Pretend to be a giant avocado. He wore a bumpy green costume, glued a brown hat on top for the pit, and rolled through the produce section.-Unfortunately, Larry forgot one key detail: the automatic doors. They didn’t open for fruit-shaped intruders. He smashed into the glass, groaned, and set off the alarm. Security found him wedged between bananas, muttering, “I just wanted guacamole…”-In court, Larry represented himself. “Your Honor,” he said, “I plead guacless.”-The judge sighed so hard it blew his papers off the bench. “Larry, for the love of salsa, just pay for the avocados next time.”-Larry nodded solemnly. “Yes, Your Honor. I’ve learned my guacin' lesson.”-He was banned from the store, but later found a new hobby: disguising as a pineapple to sneak into smoothie bars."
        for chunk in graph.stream(
                # {"messages": [{"role": "user", "content": "Hi! Remember: my name is Bob: " + datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}]},
                {"messages": [{"role": "user", "content": long_input + datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}]},
                config,
                stream_mode="values",
        ):
            chunk["messages"][-1].pretty_print()

        config = {
            "configurable": {
                "thread_id": "2",
                "user_id": "1",
            }
        }

        for chunk in graph.stream(
                {"messages": [{"role": "user", "content": "what is my name?: "+ datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}]},
                config,
                stream_mode="values",
        ):
            chunk["messages"][-1].pretty_print()
