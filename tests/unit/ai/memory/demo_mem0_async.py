import asyncio
import time

from dotenv import load_dotenv
from mem0 import AsyncMemory
from utils.biz_logger import get_logger
logger = get_logger(__name__)

# Or initialize with custom configuration
from mem0.configs.base import MemoryConfig
from mem0.vector_stores.configs import VectorStoreConfig

def init():
    load_dotenv()

config = {
        # "llm": {
        #     "provider": "ollama",
        #     "config": {
        #         "model": "mistral:7b",
        #         "temperature": 0.1,
        #         "max_tokens": 2000,
        #         "ollama_base_url": "https://evyd-ai-deepseek.evyd.tech"
        #     }
        # },
        "embedder": {
            "provider": "ollama",
            "config": {
                "model": "nomic-embed-text",
                "embedding_dims": 768,
                "ollama_base_url": "http://127.0.0.1:11434"
            }
        },
        "vector_store": {
            "provider": "pgvector",
            "config": {
                "user": "postgres",
                "password": "EVYD_AI_TEAM",
                "host": "127.0.0.1",
                "port": "5432",
                "embedding_model_dims": 1536
            }
        }
    }

async def main():
    logger.info("START_MAIN")
    init()
    memory = await AsyncMemory.from_config(config)
    start_time = time.time()

    # 创建异步任务
    task_add = asyncio.create_task(memory.add(
        messages=[
            {"role": "user", "content": "I'm travelling to US"},
            {"role": "assistant", "content": "That's great to hear!"}
        ],
        user_id="alice"
    ))
    logger.info("ASYNC_MEM0_ADD_DONE")

    # 等待异步任务完成
    # await task_add
    use_time = time.time() - start_time
    logger.info(f"ASYNC_USE_TIME: {use_time}")

if __name__ == "__main__":
    asyncio.run(main())
    logger.info("ASYNC_MEM0_DONE")
