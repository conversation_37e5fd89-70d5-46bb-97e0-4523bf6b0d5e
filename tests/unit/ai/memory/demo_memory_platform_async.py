# test_react_agent
#
# @author: 崔晓波
# @email: <EMAIL>
# @date: 2025/7/15 15:01
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
import asyncio
import os
import time

import httpx
from dotenv import load_dotenv
from typing import Annotated, TypedDict, List
from langgraph.graph import StateGraph, START
from langgraph.graph.message import add_messages
from langchain_openai import ChatOpenAI
from langsmith import traceable
from mem0 import MemoryClient, AsyncMemoryClient
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage, BaseMessage
from utils.biz_logger import get_logger

# Load environment variables
load_dotenv()

logger = get_logger(__name__)

# Configuration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
MEM0_API_KEY = os.getenv("MEM0_API_KEY")

print(f"OPENAI_API_KEY: {OPENAI_API_KEY}")
print(f"MEM0_API_KEY: {MEM0_API_KEY}")

# Initialize LangChain and Mem0
llm = ChatOpenAI(model="gpt-4.1-mini", api_key=OPENAI_API_KEY)


class State(TypedDict):
    messages: Annotated[List[HumanMessage | AIMessage], add_messages]
    mem0_user_id: str

graph = StateGraph(State)


def chatbot(state: State):
    mem0 = MemoryClient(api_key=MEM0_API_KEY)
    messages = state["messages"]
    user_id = state["mem0_user_id"]

    # Retrieve relevant memories
    #  Collect using time for search
    sys_use_time_search_start = time.time()
    memories = mem0.search(messages[-1].content, user_id=user_id)
    sys_use_time_search_end = time.time()
    sys_use_time_search = sys_use_time_search_end - sys_use_time_search_start
    logger.info(f"Mem0 search time: {sys_use_time_search:.2f} seconds")
    context = "Relevant information from previous conversations:\n"
    for memory in memories:
        context += f"- {memory['memory']}\n"

    system_message = SystemMessage(content=f"""You are a helpful customer support assistant. Use the provided context to personalize your responses and remember user preferences and past interactions.
{context}""")

    full_messages = [system_message] + messages
    sys_use_time_llm_start = time.time()
    response = llm.invoke(full_messages)
    sys_use_time_llm_end = time.time()
    sys_use_time_llm = sys_use_time_llm_end - sys_use_time_llm_start
    logger.info(f"LLM time: {sys_use_time_llm:.2f} seconds")

    # Store the interaction in Mem0
    try:
        mem0_list = messages_to_dict_list(messages) + messages_to_dict_list([response])
        logger.info(f"START_CREATE_MEM: {time.time()}")
        logger.info(f"Mem0 list: {mem0_list}")
        sys_use_time_create_start = time.time()
        mem0.add(mem0_list, user_id=user_id, output_format="v1.1")
        sys_use_time_create_end = time.time()
        sys_use_time_create = sys_use_time_create_end - sys_use_time_create_start
        logger.info(f"Mem0 create time: {sys_use_time_create:.2f} seconds")
        return {"messages": [response]}
    except Exception as e:
        logger.error(f"Mem0 create error: {e}")
        return {"messages": [AIMessage(content="Sorry, I'm having trouble understanding your question. Please try again later.")]}

# Using async memory client
async def chatbot_async(state: State):
    async_client = httpx.AsyncClient(
        # timeout=httpx.Timeout(40.0, connect=40.0, read=40.0)
        timeout=700
    )
    mem0 = AsyncMemoryClient(api_key=MEM0_API_KEY, client=async_client)
    messages = state["messages"]
    user_id = state["mem0_user_id"]

    # Retrieve relevant memories
    #  Collect using time for search
    sys_use_time_search_start = time.time()
    memories = await mem0.search(messages[-1].content, user_id=user_id)
    sys_use_time_search_end = time.time()
    sys_use_time_search = sys_use_time_search_end - sys_use_time_search_start
    logger.info(f"Mem0 search time: {sys_use_time_search:.2f} seconds")
    logger.info(f"Mem0 data: {memories}")
    context = "Relevant information from previous conversations:\n"
    for memory in memories:
        context += f"- {memory['memory']}\n"

    system_message = SystemMessage(content=f"""You are a helpful customer support assistant. Use the provided context to personalize your responses and remember user preferences and past interactions. \n\n
{context}""")

    full_messages = [system_message] + messages
    sys_use_time_llm_start = time.time()

    response = llm.invoke(full_messages)
    sys_use_time_llm_end = time.time()
    sys_use_time_llm = sys_use_time_llm_end - sys_use_time_llm_start
    logger.info(f"LLM time: {sys_use_time_llm:.2f} seconds")

    # Store the interaction in Mem0
    try:
        mem0_list = messages_to_dict_list(messages) + messages_to_dict_list([response])
        logger.info(f"START_CREATE_MEM: {time.time()}")
        sys_use_time_create_start = time.time()
        await mem0.add(mem0_list, user_id=user_id, output_format="v1.1")
        sys_use_time_create_end = time.time()
        sys_use_time_create = sys_use_time_create_end - sys_use_time_create_start
        logger.info(f"Mem0 create time: {sys_use_time_create:.2f} seconds")
        return {"messages": [response]}
    except Exception as e:
        raise e
        # logger.error(f"Mem0 create error: {e}")
        # return {"messages": [AIMessage(content="Sorry, I'm having trouble understanding your question. Please try again later.")]}


# graph.add_node("chatbot", chatbot)
# async
graph.add_node("chatbot", chatbot_async)
graph.add_edge(START, "chatbot")
graph.add_edge("chatbot", "chatbot")

compiled_graph = graph.compile()

def messages_to_dict_list(messages: list[BaseMessage]) -> list[dict]:
    """
    将 LangGraph/LangChain 消息对象列表转换为 role-content 字典列表。
    """
    result = []
    for msg in messages:
        if isinstance(msg, HumanMessage):
            role = "user"
        elif isinstance(msg, AIMessage):
            role = "assistant"
        elif isinstance(msg, SystemMessage):
            role = "system"
        else:
            role = "unknown"
        result.append({"role": role, "content": msg.content})
    return result

@traceable
async def run_conversation(user_input: str, mem0_user_id: str):
    from langchain_core.runnables import RunnableConfig
    config = RunnableConfig(configurable={"thread_id": mem0_user_id})
    state = {"messages": [HumanMessage(content=user_input)], "mem0_user_id": mem0_user_id}

    async for event in compiled_graph.astream(state, config):
        for value in event.values():
            if value.get("messages"):
                print("AI:", value["messages"][-1].content)
                return

if __name__ == "__main__":
    print("Welcome to AI! How can I assist you today?")
    mem0_user_id = "customer_test_01"  # You can generate or retrieve this based on your user management system
    async def main():
        while True:
            user_input = input("You: ")
            if user_input.lower() in ['quit', 'exit', 'bye']:
                print("AI: Thank you for contacting us. Have a great day!")
                break
            await run_conversation(user_input, mem0_user_id)
    # 使用 asyncio.run 运行异步主函数
    asyncio.run(main())
