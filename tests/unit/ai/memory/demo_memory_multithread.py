# test_memory_multithread
#
# @author: 崔晓波
# @email: <EMAIL>
# @date: 2025/7/15 15:01
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
import os
import time
import threading
from dotenv import load_dotenv
from typing import List
from mem0 import Memory
from langchain_core.messages import HumanMessage, AIMessage
from utils.biz_logger import get_logger
import sys

# 加载环境变量
load_dotenv()

logger = get_logger(__name__)

# 配置
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
MEM0_API_KEY = os.getenv("MEM0_API_KEY")

print(f"OPENAI_API_KEY: {OPENAI_API_KEY}")
print(f"MEM0_API_KEY: {MEM0_API_KEY}")

def build_mem0():
    config = {
        "embedder": {
            "provider": "ollama",
            "config": {
                "model": "nomic-embed-text",
                "embedding_dims": 768,
                "ollama_base_url": "https://evyd-ai-deepseek.evyd.tech"
            }
        },
        "vector_store": {
            "provider": "pgvector",
            "config": {
                "user": "postgres",
                "password": "EVYD_AI_TEAM",
                "host": "127.0.0.1",
                "port": "5432",
                "embedding_model_dims": 768
            }
        }
    }
    return Memory.from_config(config)

mem0 = build_mem0()

def messages_to_dict_list(messages: List[HumanMessage | AIMessage]) -> List[dict]:
    result = []
    for msg in messages:
        if isinstance(msg, HumanMessage):
            role = "user"
        elif isinstance(msg, AIMessage):
            role = "assistant"
        else:
            role = "unknown"
        result.append({"role": role, "content": msg.content})
    return result

def memory_create_thread(thread_id: int, user_id: str, content: str):
    try:
        messages = [HumanMessage(content=content), AIMessage(content=f"AI reply: {content}")]
        mem_data = messages_to_dict_list(messages)
        logger.info(f"[Thread-{thread_id}] START_CREATE_MEM: {time.time()}")
        start = time.time()
        mem0.add(mem_data, user_id=user_id)
        end = time.time()
        logger.info(f"[Thread-{thread_id}] Mem0 create time: {end - start:.2f} seconds")
    except Exception as e:
        logger.error(f"[Thread-{thread_id}] Mem0 create error: {e}")
        logger.error(f"[Thread-{thread_id}] Create failed: {e}")

def multithread_test(thread_num=10, test_content="Test content"):
    threads = []
    user_id_prefix = "test_user_mt_"
    start_all = time.time()
    for i in range(thread_num):
        t = threading.Thread(
            target=memory_create_thread,
            args=(i, f"{user_id_prefix}{i}", f"{test_content} {i}")
        )
        threads.append(t)
        t.start()
    for t in threads:
        t.join()
    end_all = time.time()
    logger.info(f"All threads finished! Total elapsed: {end_all - start_all:.2f} seconds")

if __name__ == "__main__":
    logger.info("Multi-threaded Memory creation test started...")
    logger.info("Type quit/exit/bye to exit.")
    while True:
        user_input = input("You: ")
        if user_input.strip().lower() in ["quit", "exit", "bye"]:
            logger.info("Test exited.")
            break
        parts = user_input.strip().split(maxsplit=1)
        if not parts:
            logger.info("Input cannot be empty!")
            continue
        thread_num=10
        test_content = parts[1] if len(parts) > 1 else "Test content"
        logger.info(f"Thread num: {thread_num}, Test content: {test_content}")
        multithread_test(thread_num=thread_num, test_content=test_content)