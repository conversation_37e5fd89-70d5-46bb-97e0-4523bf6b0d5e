# test_memory
#
# @author: 崔晓波
# @email: <EMAIL>
# @date: 2025/6/24 14:30
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
import unittest

from langchain.chat_models import init_chat_model
from langgraph.checkpoint.mongodb import MongoDBSaver
from langgraph.graph import StateGraph, MessagesState, START

from langgraph.checkpoint.memory import InMemorySaver

class TestMemoryShort(unittest.TestCase):
    def test(self):
        response = do_test_db_mem()
        #  断言没有异常
        self.assertIsNone(response)



def do_test_db_mem():
    from langchain.chat_models import init_chat_model
    from langgraph.graph import StateGraph, MessagesState, START

    from langgraph.checkpoint.memory import InMemorySaver

    model = init_chat_model(model='openai:gpt-4.1', api_key='***************************************************')

    checkpointer = InMemorySaver()
    def call_model(state: MessagesState):
        response = model.invoke(state["messages"])
        return {"messages": response}

    builder = StateGraph(MessagesState)
    builder.add_node(call_model)
    builder.add_edge(START, "call_model")

    graph = builder.compile(checkpointer=checkpointer)

    config = {
        "configurable": {
            "thread_id": "1"
        }
    }

    # for chunk in graph.stream(
    #         {"messages": [{"role": "user", "content": "hi! I'm bob"}]},
    #         config,
    #         stream_mode="values",
    # ):
    #     chunk["messages"][-1].pretty_print()

    for chunk in graph.stream(
            {"messages": [{"role": "user", "content": "what's my name?"}]},
            config,
            stream_mode="values",
    ):
        chunk["messages"][-1].pretty_print()

def do_test_db_mongo():
    from langchain.chat_models import init_chat_model
    from langgraph.graph import StateGraph, MessagesState, START

    from langgraph.checkpoint.memory import InMemorySaver

    model = init_chat_model(model='openai:gpt-4.1', api_key='***************************************************')

    # checkpointer = InMemorySaver()
    DB_URI = "localhost:27017"
    with MongoDBSaver.from_conn_string(DB_URI) as checkpointer:

        def call_model(state: MessagesState):
            response = model.invoke(state["messages"])
            return {"messages": response}

        builder = StateGraph(MessagesState)
        builder.add_node(call_model)
        builder.add_edge(START, "call_model")

        graph = builder.compile(checkpointer=checkpointer)

        config = {
            "configurable": {
                "thread_id": "1"
            }
        }

        # for chunk in graph.stream(
        #         {"messages": [{"role": "user", "content": "hi! I'm bob"}]},
        #         config,
        #         stream_mode="values",
        # ):
        #     chunk["messages"][-1].pretty_print()

        for chunk in graph.stream(
                {"messages": [{"role": "user", "content": "what's my name?"}]},
                config,
                stream_mode="values",
        ):
            chunk["messages"][-1].pretty_print()

