import os
import time

from dotenv import load_dotenv
from mem0 import Memory

def build_mem0():
    config = {
        # "llm": {
        #     "provider": "ollama",
        #     "config": {
        #         "model": "mistral:7b",
        #         "temperature": 0.1,
        #         "max_tokens": 2000,
        #         "ollama_base_url": "https://evyd-ai-deepseek.evyd.tech"
        #     }
        # },
        "embedder": {
            "provider": "ollama",
            "config": {
                "model": "nomic-embed-text",
                "embedding_dims": 768,
                "ollama_base_url": "http://127.0.0.1:11434"
            }
        },
        "vector_store": {
            "provider": "pgvector",
            "config": {
                "user": "postgres",
                "password": "EVYD_AI_TEAM",
                "host": "127.0.0.1",
                "port": "5432",
                "embedding_model_dims": 768
            }
        }
    }
    return Memory.from_config(config)
def main():
    # Load environment variables
    load_dotenv()
    # Initialize Memory with the configuration
    m = build_mem0()

    # Add a memory
    sys_use_time_mem_add_start = time.time()
    # m.add("I'm visiting Paris", user_id="john")
    m.add(
        messages=[
            {"role": "user", "content": "I'm travelling to SF"},
            {"role": "assistant", "content": "That's great to hear!"}
        ],
        user_id="john"
    )
    sys_use_time_mem_add_end = time.time() - sys_use_time_mem_add_start
    print(f"SYS_USE_TIME_MEM_ADD: {sys_use_time_mem_add_end:.2f} seconds")

    # Retrieve memories
    # memories = m.get_all(user_id="john")
    # print(f"memories: {memories}")

if __name__ == "__main__":
    main()