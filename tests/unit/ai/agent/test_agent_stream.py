# test_agent_stream
#
# @author: 崔晓波
# @email: <EMAIL>
# @date: 2025/6/26 21:51
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
import unittest
from datetime import datetime
from typing import TypedDict

from langgraph.constants import START, END
from langgraph.graph import StateGraph
from langsmith import traceable

from utils.biz_time import biz_format_time_ch
from utils.biz_logger import get_logger
from dotenv import load_dotenv
logger = get_logger(__name__)

class TestAgentStream(unittest.TestCase):
    def setUp(self) -> None:
        load_dotenv()
        logger.info("SET_UP: %s", biz_format_time_ch(datetime.now()))

    def test(self):
        do_test(self)

    def test_stream_modes(self):
        """
        分别测试 stream_mode="updates" 和 stream_mode="values" 的流式输出结构
        """
        class State(TypedDict):
            topic: str
            joke: str

        def refine_topic(state: State):
            return {"topic": state["topic"] + " and cats"}

        def generate_joke(state: State):
            return {"joke": f"This is a joke about {state['topic']}"}

        graph = (
            StateGraph(State)
            .add_node(refine_topic)
            .add_node(generate_joke)
            .add_edge(START, "refine_topic")
            .add_edge("refine_topic", "generate_joke")
            .add_edge("generate_joke", END)
            .compile()
        )

        # 测试 updates 模式
        updates_nodes = []
        updates_results = []
        for chunk in graph.stream({"topic": "ice cream"}, stream_mode="updates"):
            assert isinstance(chunk, dict)
            assert len(chunk) == 1  # 每次只有一个节点有输出
            node = list(chunk.keys())[0]
            updates_nodes.append(node)
            updates_results.append(chunk[node])
        # 应该至少包含 refine_topic 和 generate_joke 两个节点
        assert "refine_topic" in updates_nodes
        assert "generate_joke" in updates_nodes
        # 输出内容结构校验
        for result in updates_results:
            assert isinstance(result, dict)

        # 测试 values 模式
        values_results = []
        for chunk in graph.stream({"topic": "ice cream"}, stream_mode="values"):
            assert isinstance(chunk, dict)
            values_results.append(chunk)
        # values 模式下最后一个 chunk 应该有 joke 字段
        assert any("joke" in c for c in values_results)

@traceable
def do_test(self):
    class State(TypedDict):
        topic: str
        joke: str

    def refine_topic(state: State):
        return {"topic": state["topic"] + " and cats"}

    def generate_joke(state: State):
        return {"joke": f"This is a joke about {state['topic']}"}

    graph = (
        StateGraph(State)
        .add_node(refine_topic)
        .add_node(generate_joke)
        .add_edge(START, "refine_topic")
        .add_edge("refine_topic", "generate_joke")
        .add_edge("generate_joke", END)
        .compile()
    )

    logger.info("ONE")
    print("ONE")
    for chunk in graph.stream(
            {"topic": "ice cream"},
            stream_mode="updates",
    ):
        print(chunk)

    logger.info("TWO")
    print("TWO")
    for chunk in graph.stream(
            {"topic": "ice cream"},
            stream_mode="values",
    ):
        print(chunk)

