# LnagGraphCore
#
# @author: 崔晓波
# @email: <EMAIL>
# @date: 2025/6/17 16:07
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD


from typing import Any, Dict, Generator, TypedDict, Annotated

from langchain.chat_models.base import BaseChatModel
from langgraph.graph import StateGraph, START
from langgraph.graph.message import add_messages

from utils.biz_logger import get_logger

logger = get_logger(__name__)


class State(TypedDict):
    """对话状态类型定义"""
    messages: Annotated[list, add_messages]


class LangGraphCore:
    def __init__(self, llm: BaseChatModel):
        """
        :param llm: 注入的LLM实例（兼容LangChain BaseChatModel接口）
        """
        self.llm = llm
        self.graph = self._build_graph()
        logger.info("LangGraphCore initialized")

    def _build_graph(self) -> StateGraph:
        """构建状态图骨架"""
        builder = StateGraph(State)

        # 注册默认节点
        builder.add_node("chatbot", self._chatbot_node)
        builder.add_edge(START, "chatbot")

        logger.info("LangGraphCore state graph built")
        return builder.compile()

    def _chatbot_node(self, state: State) -> Dict[str, Any]:
        """默认对话节点逻辑"""
        logger.debug(f"LangGraphCore._chatbot_node input: {state}")
        return {"messages": [self.llm.invoke(state["messages"])]}

    def stream_updates(self, user_input: str) -> Generator[str, None, None]:
        """流式输出生成器"""
        inputs = {"messages": [{"role": "user", "content": user_input}]}
        logger.info(f"LangGraphCore.stream_updates input: {user_input}")
        try:
            for event in self.graph.stream(inputs):
                for value in event.values():
                    logger.debug(f"LangGraphCore.stream_updates output: {value['messages'][-1]['content']}")
                    yield value["messages"][-1]["content"]
        except Exception as e:
            logger.error(f"LangGraphCore.stream_updates exception: {e}", exc_info=True)
            raise
