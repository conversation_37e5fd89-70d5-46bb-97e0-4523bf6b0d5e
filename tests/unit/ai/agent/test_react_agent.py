# test_react_agent
#
# @author: 崔晓波
# @email: <EMAIL>
# @date: 2025/6/24 15:01
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
import os
import unittest
from datetime import datetime

from langchain_core.tools import tool
from langsmith import traceable

from ai.agent.react_agent import ReactAgent
from ai.config.ai_enum import AIPlatformEnum
from ai.router import Router
from ai.config.ai_config import RouterConfig, FactoryConfig, ModelConfig
from ai.config.ai_properties import AIProperties
from ai.tools.apt_slot_tool import get_apt_slot, recommend_slot
from ai.tools.weather import get_weather
from utils.biz_time import biz_format_time_ch
from utils.biz_logger import get_logger
from dotenv import load_dotenv
logger = get_logger(__name__)


class TestReactAgent(unittest.TestCase):
    def setUp(self) -> None:
        load_dotenv()
        logger.info("SET_UP: %s", biz_format_time_ch(datetime.now()))
        logger.info("PG_URI: %s", os.getenv("POSTGRES_URI"))
        ai_router = Router(RouterConfig())
        factory_config = FactoryConfig()
        factory_config.platform = AIPlatformEnum.OPENAI
        self.model = ai_router.handle(factory_config=factory_config, model_config=ModelConfig())


    @traceable
    def test_react_agent(self):
        from ai.tools.weather import get_weather

        # Debug only tool
        # tool_call = {
        #     "type": "tool_call",
        #     "id": "1",
        #     "args": {"city": "Beijing"}
        # }
        # toll_res_weather = get_weather_new.invoke(tool_call)
        # logger.info(f"toll_res_weather: {toll_res_weather}")

        # # Debug with AI llm
        ai_router = Router(RouterConfig())
        factory_config = FactoryConfig()
        factory_config.platform = AIPlatformEnum.OPENAI
        ai_instance = ai_router.handle(factory_config=factory_config, model_config=ModelConfig())
        # ai_agent = ReactAgent(
        #     model=ai_instance,
        #     tools=[get_weather_new],
        #     system_prompt="You are a weather specialist.",
        #     store_type="postgres"
        # )
        with ReactAgent(
            model=ai_instance,
            tools=[get_weather],
            system_prompt="You are a weather specialist.",
            store_type="postgres",
            thread_id="101",
            biz_user_id="1"
        ) as ai_agent:
            ai_agent.draw_graph()

            # Run a agent
            # response = ai_agent.agent.invoke({"messages": [{"role": "user", "content": "what is the weather in Beijing"}]})
            # response = ai_agent.run("what is the weather in Beijing")
            # logger.info(f"agent_chat response: {response["messages"][-1].content}")

            # agent_res = ai_agent.run_stream_new("what is the weather in Beijing? : " + format_time_ch(datetime.now()))
            # for chunk in ai_agent.run_stream_new("what is the weather in Beijing?" + "03"):
            for chunk in ai_agent.run_stream("Hi, there! My name is Alice"):
                logger.info(f"chunk: {chunk}")
            # count_token = count_tokens_approximately(agent_res["messages"])
            # logger.info(f"count_token: {count_token}")

            # agent_res = ai_agent.run_stream_new("Hi, I am bob." + "04")
            # logger.info(f"agent_res: {agent_res}")
            #
            # agent_res = ai_agent.run_stream_new("What's my name?" + "04")
            # logger.info(f"agent_res: {agent_res}")


            # # Stream chat
            # for chunk in ai_agent.run_stream("Hi, I am bob"):
            #     chunk["messages"][-1].pretty_print()
            #
            # for chunk in ai_agent.run_stream("What's my name?"):
            #     chunk["messages"][-1].pretty_print()
        with ReactAgent(
            model=ai_instance,
            tools=[get_weather],
            system_prompt="You are a weather specialist.",
            store_type="postgres",
            thread_id="102",
            biz_user_id="1"
        ) as ai_agent:
            ai_agent.draw_graph()

            # Run a agent
            # response = ai_agent.agent.invoke({"messages": [{"role": "user", "content": "what is the weather in Beijing"}]})
            # response = ai_agent.run("what is the weather in Beijing")
            # logger.info(f"agent_chat response: {response["messages"][-1].content}")

            # agent_res = ai_agent.run_stream_new("what is the weather in Beijing? : " + format_time_ch(datetime.now()))
            # for chunk in ai_agent.run_stream_new("what is the weather in Beijing?" + "03"):
            for chunk in ai_agent.run_stream("What's my name?"):
                logger.info(f"chunk: {chunk}")
        #
        # # Dynamically add a new tool
        # def get_time(timezone: str) -> str:
        #     """Simulate time lookup."""
        #     return f"Current time in {timezone}: 12:00 PM"
        #
        #
        # ai_agent.add_tool(get_time)
        # time_response = ai_agent.run("what is the time in Beijing")
        # print(f"Time response: %s", time_response)

    @traceable
    def test_react_agent_demo(self):
        from langgraph.prebuilt import create_react_agent

        # @tool(name_or_callable="get_weather", description="Get weather by city")
        # def get_weather(city: str) -> str:
        #     return f"Weather in {city}: Sunny, 25°C"

        agent = create_react_agent(
            model=self.model,
            tools=[get_weather],
            prompt="You are a helpful assistant"
        )

        response = agent.invoke({"messages": [{"role": "user", "content": "what is the weather in Beijing"}]})
        logger.info("agent_chat response: %s", response["messages"][-1].content)

    @traceable
    def test_multi_tool(self):
        agent = ReactAgent(
            model=self.model,
            tools=[get_apt_slot, recommend_slot],
            system_prompt="You are a health specialist.",
            store_type="postgres",
            thread_id="10000",
            biz_user_id="10000"
        )
        # user_input = "I want to make an appointment"
        # user_input = "I want to make an appointment at 2025-07-11 afternoon"
        user_input = "I want to go to the nearest hospital"
        ai_res = agent.run(user_input)
        logger.info(f"ai_res: {ai_res}")
        if not ai_res:
            assert False
        print(ai_res["messages"][-1].pretty_print())

    @traceable
    def test_normal_tool(self):
        agent = ReactAgent(
            model=self.model,
            tools=[],
            system_prompt="You are a health specialist.",
            history_strategy="trim",
            store_type="postgres",
            thread_id="10000",
            biz_user_id="10000"
        )
        # user_input = "I want to make an appointment"
        # user_input = "I want to make an appointment at 2025-07-11 afternoon"
        user_input = "Hi there! My name is Alice"
        ai_res = agent.run(user_input)
        logger.info(f"ai_res: {ai_res}")
        if not ai_res:
            assert False
        print(ai_res["messages"][-1].pretty_print())
