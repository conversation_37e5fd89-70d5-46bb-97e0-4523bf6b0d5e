import unittest


class TestEnv(unittest.TestCase):
    def setUp(self):
        pass
    def test_env(self):
        import os
        print("CONDA_SHLVL: ", os.getenv("CONDA_SHLVL"))

    def test_use_time(self):
        times_list_platform = [5.47, 3.61, 4.11, 5, 5.67, 4.78, 6.82, 3.17, 6.64, 5.29, 7.20, 5.33, 3.81, 5.20, 5.16]
        avg_platform = sum(times_list_platform) / len(times_list_platform)
        print("avg_platform: ", avg_platform)

        times_list_opensource = [3.69, 3.50, 5.23, 6.09, 6.12, 6.00, 12.84, 6.43, 9.47, 5.91, 6.92, 8.37, 8.17, 7.52, 7.98]
        avg_opensource = sum(times_list_opensource) / len(times_list_opensource)
        print("avg_opensource: ", avg_opensource)


