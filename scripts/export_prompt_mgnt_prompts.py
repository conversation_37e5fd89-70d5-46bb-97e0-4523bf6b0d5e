import asyncio
import json
import os

from dotenv import load_dotenv
from motor.motor_asyncio import AsyncIOMotorClient

async def main():
    load_dotenv()
    mongo_uri = os.getenv("MONGO_URI")
    mongo_db = os.getenv("MONGO_DB")
    client = AsyncIOMotorClient(mongo_uri)
    db = client[mongo_db]
    collection = db["prompt"]

    # 读取所有数据
    docs = await collection.find({}).to_list(length=None)
    # 去除MongoDB的ObjectId等不可序列化字段
    for doc in docs:
        doc.pop("_id", None)

    # 导出为json文件
    with open("prompt_export.json", "w", encoding="utf-8") as f:
        json.dump(docs, f, ensure_ascii=False, indent=2)
    print(f"已导出{len(docs)}条数据到prompt_export.json")

if __name__ == "__main__":
    asyncio.run(main()) 