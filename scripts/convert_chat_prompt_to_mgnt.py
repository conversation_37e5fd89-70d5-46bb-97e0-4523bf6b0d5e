import asyncio
import os

from dotenv import load_dotenv
from motor.motor_asyncio import AsyncIOMotorClient

# 数据转换函数
def convert_chat_prompt_to_mgnt(chat_prompt):
    return {
        "type": chat_prompt.get("type"),
        "content": chat_prompt.get("prompt"),
        "description": chat_prompt.get("type"),
        "name": chat_prompt.get("type")
    }

async def main():
    load_dotenv()
    mongo_uri = os.getenv("MONGO_URI")
    mongo_db = os.getenv("MONGO_DB")
    # 连接MongoDB
    client = AsyncIOMotorClient(mongo_uri)
    db = client[mongo_db]
    chat_prompt_col = db["chat_prompt"]
    prompt_col = db["prompt"]

    # 读取chat_prompt表所有数据
    chat_prompts = await chat_prompt_col.find({}).to_list(length=None)
    mgnt_prompts = [convert_chat_prompt_to_mgnt(item) for item in chat_prompts]

    # 批量插入到prompt表
    if mgnt_prompts:
        await prompt_col.insert_many(mgnt_prompts)
        print(f"已插入{len(mgnt_prompts)}条数据")
    else:
        print("无数据可插入")

if __name__ == "__main__":
    asyncio.run(main()) 