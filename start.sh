#!/bin/bash

# 根据 PAAS_TARGET 环境变量选择 env 文件
case "$PAAS_TARGET" in
    test|preview)
        ENV_FILE="env-test"
        ;;
    prod)
        ENV_FILE="env-prod"
        ;;
    dev)
        ENV_FILE="env-dev"
        ;;
    *)
        ENV_FILE=".env"
        ;;
esac

# 如果对应的 env 文件存在，则复制为 .env
if [ -f "$ENV_FILE" ]; then
    cp "$ENV_FILE" .env
    echo "已使用 $ENV_FILE 作为环境配置"
else
    echo "未找到 $ENV_FILE，使用默认 .env（如有）"
fi

uv run uvicorn main:app --host 0.0.0.0 --port 8080 --workers 4 --timeout-keep-alive 120 --log-level info 