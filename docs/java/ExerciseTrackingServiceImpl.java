package com.evydtech.chatbot.scene.service.impl;

import com.evydtech.chatbot.scene.domain.SceneHandlerQo;
import com.evydtech.chatbot.scene.function.ExerciseTrackingFunctionTools;
import com.evydtech.chatbot.tool.domain.pojo.qo.AiChatGptQo;
import com.evydtech.chatbot.chat.domain.pojo.qo.MessageQo;
import com.evydtech.chatbot.chat.domain.pojo.vo.MessageVo;
import com.evydtech.chatbot.tool.domain.pojo.vo.AiChatGptVo;
import com.evydtech.chatbot.tool.domain.pojo.dto.StreamChatDto;
import com.evydtech.chatbot.tool.manager.AiInterface;
import com.evydtech.chatbot.tool.manager.AiCallbackService;
import com.evydtech.chatbot.chat.domain.enums.BizMessageTypeEnum;
import com.evydtech.chatbot.im.domain.qo.integrate.ImBotMessageQo;
import com.evydtech.chatbot.im.enums.ImMessageStatusEnum;
import com.theokanning.openai.completion.chat.ChatCompletionRequest;
import com.theokanning.openai.completion.chat.ChatFunction;
import com.theokanning.openai.service.FunctionExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.evydtech.chatbot.common.constant.MongoPromptsTemplateEnum;
import com.evydtech.chatbot.tool.service.impl.PromptsTemplateTool;
import cn.hutool.core.util.StrUtil;

/**
 * 运动追踪场景服务实现
 * 
 * @author: AI Assistant
 * @description: 处理运动记录和运动健康咨询，结合function calling能力
 */
@Service
@Slf4j
public class ExerciseTrackingServiceImpl extends BaseAbstractSceneService {

    @Autowired
    private ExerciseTrackingFunctionTools exerciseTrackingFunctionTools;

    /**
     * 多卡片数据存储字段名
     */
    private static final String MULTIPLE_CARD_DATA_FIELD = "multipleCardData";
    
    /**
     * 多卡片数据的静态存储，以请求ID为key
     */
    private static final Map<String, List<String>> MULTIPLE_CARD_DATA_STORAGE = new ConcurrentHashMap<>();

    @Override
    public AiChatGptVo handle(AiInterface<AiChatGptQo, AiChatGptVo> aiInterface, SceneHandlerQo handlerQo) {
        log.info("EXERCISE_TRACKING_SCENE_START|USER: {}|PROMPTS: {}", 
                handlerQo.getUserInfo() != null ? handlerQo.getUserInfo().getBizId() : "UNKNOWN", 
                handlerQo.getPrompts());
        
        // 生成唯一的请求ID
        String requestId = "exercise_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
        
        try {
            // 优先从数据库获取提示词
            String originalPrompts = PromptsTemplateTool.getPromptsTemplate(MongoPromptsTemplateEnum.EXERCISE_TRACKING_SYSTEM_PROMPTS);
            
            // 构建增强的运动追踪系统提示词
            String enhancedSystemPrompts = buildEnhancedSystemPrompts(handlerQo, originalPrompts);
            
            // 构建AI请求，集成function calling
            AiChatGptQo aiChatGptQo = this.buildSystemPrompts(handlerQo, enhancedSystemPrompts)
                    .buildUserPrompts(handlerQo, handlerQo.getPrompts())
                    .build(handlerQo);
            
            // 配置Function Calling
            setupFunctionCalling(aiChatGptQo, handlerQo);
            
            // 创建finalResult对象，用于在回调中访问
            AiChatGptVo finalResult = new AiChatGptVo();
            finalResult.setId(requestId); // 设置请求ID用于数据关联
            
            // 使用优化后的流式工具链路调用（V6版本：工具非流式+最终文本流式）
            AiChatGptVo result = aiInterface.handleWithToolChainingStream(aiChatGptQo, new AiCallbackService() {

                @Override
                public void onHandle(StreamChatDto streamChatDto) {

                }

                @Override
                public void onSuccess(StreamChatDto streamChatDto) {
                    // 发送卡片到IM
                    sendCardToImIfExists(finalResult, handlerQo);
                }

                @Override
                public void onError(Object errorInfo) {

                }
            });

            // 处理营养记录卡片
            processExerciseRecordCard(result, aiChatGptQo, finalResult);

            finalResult.setData(result.getData());
            finalResult.setDataType(result.getDataType());
            result.setData(null);

            // 【BUG修复】清空aiGc文本内容，避免afterHandle重复发送IM文本消息
            // 因为我们已经在onSuccess回调中通过sendCardToImIfExists发送了卡片消息
            // 如果不清空aiGc，afterHandle会检测到message不为空而发送额外的文本消息
            log.info("EXERCISE_TRACKING_SCENE_END|USER: {}|RESULT_LENGTH: {}|CLEARING_AI_GC_TO_PREVENT_DUPLICATE_IM_MESSAGE", 
                    handlerQo.getUserInfo() != null ? handlerQo.getUserInfo().getBizId() : "UNKNOWN",
                    result.getAiGc() != null ? result.getAiGc().length() : 0);
            result.setAiGc(null); // 清空文本内容，避免afterHandle重复发送IM消息
            return result;
            
        } catch (Exception e) {
            log.error("EXERCISE_TRACKING_SCENE_ERROR|USER: {}", 
                    handlerQo.getUserInfo() != null ? handlerQo.getUserInfo().getBizId() : "UNKNOWN", e);
            // 降级处理：如果function calling失败，使用简单聊天
            return handleFallback(aiInterface, handlerQo, e);
        } finally {
            // 清理ThreadLocal和静态存储
            exerciseTrackingFunctionTools.clearCurrentUserId();
        }
    }

    /**
     * 构建增强的系统提示词
     * 优先使用MongoDB中的完整提示词，只有为空时才使用fallback
     */
    private String buildEnhancedSystemPrompts(SceneHandlerQo handlerQo, String originalPrompts) {
        // 生成精确到时分秒的当前时间信息，强化时间约束
        LocalDateTime now = LocalDateTime.now();
        String currentDateTime = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String currentDate = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String currentTime = now.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        String currentDayOfWeek = now.getDayOfWeek().toString();
        String currentYear = String.valueOf(now.getYear());
        
        String systemTimeInfo = "🚨 **CRITICAL SYSTEM TIME INFORMATION - MUST FOLLOW STRICTLY** 🚨\n\n" +
                "**CURRENT SYSTEM DATE**: " + currentDate + "\n" +
                "**CURRENT SYSTEM TIME**: " + currentTime + "\n" +
                "**CURRENT DAY OF WEEK**: " + currentDayOfWeek + "\n" +
                "**CURRENT YEAR**: " + currentYear + "\n" +
                "**FULL TIMESTAMP**: " + currentDateTime + "\n\n" +
                
                "⚠️ **MANDATORY DATE/TIME RULES - NO EXCEPTIONS** ⚠️\n" +
                "1. TODAY IS EXACTLY: " + currentDate + " (" + currentDayOfWeek + ")\n" +
                "2. CURRENT YEAR IS: " + currentYear + " - NEVER use 2023 or other years\n" +
                "3. When user says 'today', 'now', 'current': ALWAYS use " + currentDate + "\n" +
                "4. When user says 'yesterday': use " + now.minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + "\n" +
                "5. When user says 'last week': calculate from " + currentDate + " backwards\n" +
                "6. When user says 'recent week': use " + now.minusDays(6).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " to " + currentDate + "\n" +
                "7. NEVER use dates from 2023 or any year other than " + currentYear + "\n" +
                "8. ALL date calculations MUST be based on " + currentDate + " as the reference point\n" +
                "9. If unsure about dates, ASK for clarification rather than guessing\n\n";
        
        // 获取敏感话题处理提示词（优先级高）
        String sensitiveTopicPrompt = PromptsTemplateTool.getPromptsTemplate(MongoPromptsTemplateEnum.SENSITIVE_TOPIC_PROMPT);
        String sensitiveTopicInfo = "";
        if (StrUtil.isNotBlank(sensitiveTopicPrompt)) {
            sensitiveTopicInfo = sensitiveTopicPrompt + "\n\n";
        }
        
        // 移动端友好的Markdown格式约束
        String markdownFormatRules = "📱 **MOBILE-FRIENDLY MARKDOWN FORMAT RULES - STRICTLY REQUIRED** 📱\n\n" +
                "**MANDATORY HEADING STRUCTURE:**\n" +
                "- Use ONLY ## (H2) and ### (H3) headings - NO # (H1) or #### (H4+)\n" +
                "- Main sections: ## Section Name\n" +
                "- Subsections: ### Subsection Name\n" +
                "- Keep headings SHORT (max 6 words) for mobile screens\n\n" +
                
                "**CONTENT FORMATTING:**\n" +
                "- Use **bold** for emphasis, not headings\n" +
                "- Use bullet points (- ) for lists, keep items concise\n" +
                "- Use numbered lists (1. ) for step-by-step instructions\n" +
                "- Keep paragraphs SHORT (2-3 sentences max)\n" +
                "- Use line breaks between sections for readability\n\n" +
                
                "**MOBILE OPTIMIZATION:**\n" +
                "- Avoid long horizontal text blocks\n" +
                "- Use emojis sparingly for visual breaks\n" +
                "- Keep tables simple or convert to lists\n" +
                "- Ensure content is scannable on small screens\n\n" +
                
                "**DATE PRESENTATION RULES:**\n" +
                "📅 **MANDATORY:** When displaying multiple dates in lists or summaries, ALWAYS arrange them in CHRONOLOGICAL ORDER (oldest to newest)\n" +
                "- Example: 2025-01-01, 2025-01-02, 2025-01-03 (NOT random order)\n" +
                "- This applies to exercise history, workout summaries, progress tracking, and any multi-date content\n" +
                "- Ensure consistent ascending date sorting for better user experience and data clarity\n\n";

        // 如果MongoDB中有完整的提示词（包括基础提示词+工具指导），直接使用
        if (StrUtil.isNotBlank(originalPrompts)) {
            // 将系统时间信息、敏感话题处理提示词和格式约束插入到提示词最开始
            originalPrompts = systemTimeInfo + sensitiveTopicInfo + markdownFormatRules + originalPrompts;
            return originalPrompts;
        }
        
        // 只有当MongoDB取不到值时才使用代码中的fallback提示词
        log.warn("EXERCISE_TRACKING_USING_FALLBACK_PROMPTS|MONGODB_PROMPTS_EMPTY");
        if (StrUtil.isBlank(originalPrompts)) {
            StringBuilder originalPromptsBuilder = new StringBuilder();
            originalPromptsBuilder.append("You are a fitness expert and exercise tracking assistant. Your core mission is to help users track their physical activities, monitor exercise progress, and provide personalized fitness recommendations based on exercise science and healthy lifestyle principles.\n\n");
            originalPromptsBuilder.append("Core Code of Conduct (must be strictly followed):\n\n");
            originalPromptsBuilder.append("Determine reply language based on user input:\n\n");
            originalPromptsBuilder.append("You must accurately recognize and respond fluently in the following languages: English, Chinese (Simplified), Bahasa Melayu.\n\n");
            originalPromptsBuilder.append("If the user's input is in any language other than these three, always reply in English.\n\n");
            originalPromptsBuilder.append("Ensure your expression in the selected language is natural and accurate.\n\n");
            originalPromptsBuilder.append("Use language that is simple, courteous and layman friendly.\n\n");
            originalPromptsBuilder.append("Exercise tracking accuracy:\n\n");
            originalPromptsBuilder.append("Make every effort to accurately identify exercise types, duration, intensity, and performance metrics from user descriptions.\n\n");
            originalPromptsBuilder.append("For exercise information that cannot be precisely determined, ask clarifying questions or provide general fitness guidance.\n\n");
            originalPromptsBuilder.append("Exercise data sources and recommendations:\n\n");
            originalPromptsBuilder.append("Your exercise recommendations should be based on widely recognized fitness guidelines and exercise science.\n\n");
            originalPromptsBuilder.append("Understand that exercise needs vary by fitness level, age, health status, and individual goals.\n\n");
            originalPromptsBuilder.append("Exercise evaluation criteria:\n\n");
            originalPromptsBuilder.append("Evaluate exercise adequacy based on duration, frequency, intensity, and alignment with fitness goals.\n\n");
            originalPromptsBuilder.append("Provide actionable advice for improving exercise consistency and effectiveness.\n\n");
            originalPromptsBuilder.append("Output format:\n\n");
            originalPromptsBuilder.append("All output must strictly follow the markdown format specified in the subsequent \"User Request Processing Protocol\".\n\n");
            originalPromptsBuilder.append("Exercise analysis and recommendations must be clear, supportive, and evidence-based.\n\n");
            originalPromptsBuilder.append("Field focus & disclaimer:\n\n");
            originalPromptsBuilder.append("Your analysis focuses on exercise tracking, fitness optimization, and general wellness recommendations.\n\n");
            originalPromptsBuilder.append("Clearly state that your analysis does not constitute medical advice for exercise-related health issues. For specific health conditions or injuries, recommend consulting healthcare professionals.\n\n");
            
            originalPrompts = originalPromptsBuilder.toString();
        }

        StringBuilder contextBuilder = new StringBuilder();
        contextBuilder.append("**CRITICAL RULES:**\n")
                .append("- **Language Selection (HIGHEST PRIORITY)**: \n")
                .append("  1. Determine reply language based on user input\n")
                .append("  2. You must accurately recognize and respond fluently in the following languages: English, Chinese (Simplified), Bahasa Melayu\n")
                .append("  3. If the user's input is in any language other than these three, always reply in English\n")
                .append("  4. Use language that is simple, courteous and layman friendly\n")
                .append("- **Tools**: ALWAYS call tools, NEVER refuse with 'cannot access data'\n")
                .append("- **Intent Recognition**: FIRST determine user intent:\n")
                .append("  * NEW EXERCISE REPORTING: User reports completing NEW exercise → Call generateExerciseRecord()\n")
                .append("  * EXERCISE CONSULTATION: User asks about EXISTING exercise performance/analysis → Call getDailyDetail() or getExerciseDaily() FIRST\n")
                .append("- **History Queries**: Call getDailyDetail() or getExerciseDaily() for past exercise questions, performance analysis, and exercise consultation\n")
                .append("- **Text Output**: When providing exercise summaries in text response, DO NOT include icon images or emoji. Focus on clear text descriptions only.\n\n")
                
                .append("🧠 **ENHANCED EXERCISE CONVERSATION INTELLIGENCE SYSTEM** 🧠\n\n")
                
                .append("**🎯 CORE MISSION: INTELLIGENT CONVERSATION HISTORY UTILIZATION**\n")
                .append("Your primary strength is leveraging EVERY piece of exercise-related information from conversation history.\n")
                .append("NEVER start fresh when the user has already provided exercise information in previous messages.\n\n")
                
                .append("**🔍 SYSTEMATIC CONVERSATION SCANNING PROTOCOL**\n")
                .append("- **MANDATORY FIRST STEP**: Thoroughly scan ALL previous messages for ANY exercise-related mentions\n")
                .append("- **Extract ALL exercise data**: type, date, duration, distance, heart rate, intensity, user feelings\n")
                .append("- **Track partial responses**: user confirmations, clarifications, corrections to previous questions\n")
                .append("- **Maintain data continuity**: connect current input with historical information seamlessly\n")
                .append("- **Preserve context**: never lose previously shared exercise information across conversation turns\n\n")
                
                .append("**🔄 INTELLIGENT SESSION STATE MANAGEMENT**\n")
                .append("- **State 1 - Information Collection**: Actively gathering exercise data through multi-turn conversation\n")
                .append("  • Scan conversation history for all mentioned exercise details\n")
                .append("  • Identify which required fields are still missing\n")
                .append("  • Ask targeted questions referencing previously shared information\n")
                .append("  • Continue until ALL required fields are collected\n")
                .append("- **State 2 - Record Generation**: All data collected, ready to call generateExerciseRecord()\n")
                .append("  • Validate all required fields are present and complete\n")
                .append("  • Generate exercise record with collected data\n")
                .append("  • Display confirmation of successful record creation\n")
                .append("- **State 3 - Session Complete**: Exercise record successfully created and confirmed\n")
                .append("  • Record generation completed with card display\n")
                .append("  • Session boundary established for future conversations\n")
                .append("  • Ready to start fresh for next exercise reporting request\n\n")
                
                .append("**🚦 SMART SESSION BOUNDARY DETECTION**\n")
                .append("Automatically detect session boundaries based on conversation flow:\n")
                .append("- **Fresh Start Indicators**: New exercise reporting requests after successful record completion\n")
                .append("- **Continuation Indicators**: Additional details for ongoing incomplete exercise data collection\n")
                .append("- **Completion Indicators**: Successful generateExerciseRecord() function call with card generation\n")
                .append("- **Context Switching**: Clear topic changes to entirely different exercise activities\n\n")
                
                .append("**💡 ADAPTIVE INFORMATION INTEGRATION STRATEGIES**\n")
                .append("Use these intelligent approaches to leverage conversation history:\n")
                .append("- **Historical Reference**: Acknowledge previously shared information when asking new questions\n")
                .append("- **Progressive Building**: Build on existing information rather than starting from scratch\n")
                .append("- **Context Bridging**: Connect current user input with historical exercise mentions\n")
                .append("- **Memory Validation**: Confirm understanding of previously shared details\n")
                .append("- **Gap Targeting**: Focus questions only on missing information, not already provided data\n\n")
                
                .append("**📚 CONVERSATION MEMORY BEST PRACTICES**\n")
                .append("- **Comprehensive Scanning**: Review ENTIRE conversation history for exercise-related content\n")
                .append("- **Intelligent Extraction**: Identify exercise type, timing, duration, distance, heart rate, intensity\n")
                .append("- **Context Awareness**: Understand user confirmations, corrections, and additional details\n")
                .append("- **Seamless Integration**: Merge current input with historical conversation elements\n")
                .append("- **Progress Tracking**: Maintain clear awareness of what information has been collected\n\n")
                
                .append("**⚡ ENHANCED MULTI-TURN CONVERSATION FLOW**\n")
                .append("- **Turn 1**: User mentions exercise activity → Extract ALL available details from statement\n")
                .append("- **Turn 2**: Acknowledge extracted information → Ask for specific missing fields only\n")
                .append("- **Turn 3**: Integrate new details with existing data → Continue targeted questioning\n")
                .append("- **Turn N**: Complete data collection → Validate and call generateExerciseRecord()\n")
                .append("- **Post-Generation**: Confirm successful record creation → Establish session boundary\n\n")
                
                .append("**🎨 NATURAL CONTEXTUAL QUESTIONING TECHNIQUES**\n")
                .append("Instead of generic questions, use conversation history to create contextual inquiries:\n")
                .append("- Reference previously mentioned exercise type when asking for missing details\n")
                .append("- Acknowledge confirmed information while requesting additional data\n")
                .append("- Build on established context rather than repetitive questioning\n")
                .append("- Use conversational memory to create coherent dialogue progression\n")
                .append("- Maintain friendly, natural flow while systematically collecting required data\n\n")
                
                .append("**🚫 CRITICAL PROHIBITIONS**\n")
                .append("- **NEVER ignore conversation history**: Always scan and utilize previously shared information\n")
                .append("- **NEVER ask redundant questions**: Don't request information already provided\n")
                .append("- **NEVER auto-estimate values**: Always ask user for missing required fields\n")
                .append("- **NEVER lose context**: Maintain conversation memory across all turns\n")
                .append("- **NEVER start completely fresh**: Build on existing conversation foundation\n\n")
                
                .append("**✅ REQUIRED BEHAVIORS**\n")
                .append("- **ALWAYS scan conversation history**: Review all messages for exercise-related content\n")
                .append("- **ALWAYS reference historical data**: Acknowledge previously shared information\n")
                .append("- **ALWAYS maintain context**: Preserve conversation memory across multiple turns\n")
                .append("- **ALWAYS use targeted questions**: Ask only for missing information\n")
                .append("- **ALWAYS validate completeness**: Ensure all required fields before record generation\n");

        return systemTimeInfo + sensitiveTopicInfo + markdownFormatRules + originalPrompts + contextBuilder.toString();
    }

    /**
     * 配置Function Calling工具
     */
    private void setupFunctionCalling(AiChatGptQo aiChatGptQo, SceneHandlerQo handlerQo) {
        try {
            // 设置用户上下文到工具类
            String currentUserId = handlerQo.getUserInfo() != null ? handlerQo.getUserInfo().getThirdUserId() : null;
            exerciseTrackingFunctionTools.setCurrentUserId(currentUserId);
            
            // 创建Function列表
            List<ChatFunction> chatFunctionList = Arrays.asList(
                    ChatFunction.builder()
                            .name("getDailyDetail")
                            .description("MANDATORY: Get user's daily exercise progress, goals, and historical exercise data. Call for ANY query about past exercise OR exercise performance consultation: 'did I exercise today', 'my exercise progress', 'exercise history', 'how was my workout', 'am I walking fast enough', '今天运动了吗', '我的运动进度', '我是不是走得有点快', '运动表现怎么样'. NEVER refuse with 'cannot access data' - ALWAYS call this tool first for historical exercise queries and performance analysis.")
                            .executor(ExerciseTrackingFunctionTools.GetDailyDetailRequest.class, 
                                    request -> exerciseTrackingFunctionTools.getDailyDetail(request))
                            .build(),
                    ChatFunction.builder()
                            .name("generateExerciseRecord")
                            .description("🚨 **ADVANCED EXERCISE RECORD GENERATION TOOL** 🚨\n\n" +
                                        "Generate exercise record JSON card ONLY when user REPORTS COMPLETING exercise activities.\n\n" +
                                        "**DO NOT CALL FOR**: exercise consultation, analysis, or questions about existing performance.\n\n" +
                                        "🧠 **ENHANCED CONVERSATION INTELLIGENCE SYSTEM** 🧠\n" +
                                        "- **MANDATORY CONVERSATION SCAN**: Before EVERY function call, systematically scan ALL conversation history\n" +
                                        "- **INTELLIGENT DATA EXTRACTION**: Extract ALL exercise-related information from previous messages\n" +
                                        "- **CONTEXT INTEGRATION**: Connect current user input with historical conversation elements\n" +
                                        "- **MEMORY PRESERVATION**: Never lose previously shared exercise information across turns\n\n" +
                                        "🔄 **SMART SESSION STATE MANAGEMENT** 🔄\n" +
                                        "- **Session Detection**: Automatically distinguish between new exercise sessions vs continuing current session\n" +
                                        "- **Boundary Recognition**: Identify when previous exercise records are complete vs ongoing data collection\n" +
                                        "- **State Transitions**: Track progression from data collection → record generation → session completion\n" +
                                        "- **Context Preservation**: Maintain conversation memory while respecting session boundaries\n\n" +
                                        "**📊 PROGRESSIVE INFORMATION COLLECTION PROTOCOL**:\n" +
                                        "- **Phase 1**: Scan conversation history for ALL exercise mentions and partial data\n" +
                                        "- **Phase 2**: Inventory collected vs missing required fields\n" +
                                        "- **Phase 3**: Request ONLY missing information using conversation context\n" +
                                        "- **Phase 4**: Validate completeness before function execution\n" +
                                        "- **Phase 5**: Generate record and establish session boundary\n\n" +
                                        "**MANDATORY FIELD COLLECTION**:\n" +
                                        "- Required: recordDate(yyyy-MM-dd), taskId(integer), duration(seconds), avgHeartRate(integer), intensityLevel(string)\n" +
                                        "- Conditional: distance(meters) for distance-tracking exercises (running, cycling, etc.)\n" +
                                        "- Date constraint: recordDate MUST NOT be future date - only today or past dates\n\n" +
                                        "**🚫 CRITICAL PROHIBITIONS**:\n" +
                                        "- **NEVER auto-estimate**: Don't guess heart rate, intensity, or other missing values\n" +
                                        "- **NEVER ignore history**: Always utilize conversation context for data collection\n" +
                                        "- **NEVER ask redundantly**: Don't request information already provided in conversation\n" +
                                        "- **NEVER call incomplete**: Ensure ALL required fields before function execution\n\n" +
                                        "**✅ MANDATORY BEHAVIORS**:\n" +
                                        "- **ALWAYS scan comprehensively**: Review entire conversation for exercise information\n" +
                                        "- **ALWAYS reference context**: Use conversation history in follow-up questions\n" +
                                        "- **ALWAYS validate data**: Confirm completeness before record generation\n" +
                                        "- **ALWAYS maintain memory**: Preserve conversation context across multiple turns\n" +
                                        "**TIME CONVERSION**: 30 minutes = 1800 seconds, 1 hour = 3600 seconds")
                            .executor(ExerciseTrackingFunctionTools.GenerateExerciseRecordRequest.class, 
                                    request -> exerciseTrackingFunctionTools.generateExerciseRecord(request))
                            .build(),
                    ChatFunction.builder()
                            .name("getAllExerciseList")
                            .description("MANDATORY: Get all available exercise types with taskIDs, names, and icons. Call when you need to match user's exercise type to valid taskID before calling generateExerciseRecord. Use taskId=99999 for unknown exercise types not in the list.")
                            .executor(ExerciseTrackingFunctionTools.GetAllExerciseListRequest.class,
                                    request -> exerciseTrackingFunctionTools.getAllExerciseList(request))
                            .build(),
                    ChatFunction.builder()
                            .name("getExerciseDaily")
                            .description("MANDATORY: Get user's detailed exercise records for specified date range. Call for ANY query about detailed exercise history OR exercise performance analysis: 'what exercises did I do', 'my workout details', 'exercise performance', 'how fast did I walk', 'analyze my exercise', '我做了什么运动', '运动详情', '我走得快不快', '分析我的运动表现'. NEVER refuse with 'cannot access data' - ALWAYS call this tool first for exercise analysis and consultation.")
                            .executor(ExerciseTrackingFunctionTools.GetExerciseDailyRequest.class,
                                    request -> exerciseTrackingFunctionTools.getExerciseDaily(request))
                            .build()
            );
            
            // 创建Function Executor
            FunctionExecutor functionExecutor = new FunctionExecutor(chatFunctionList);

            // 设置到AI请求中
            aiChatGptQo.setFunctionExecutor(functionExecutor);
            aiChatGptQo.setFunctionCall(ChatCompletionRequest.ChatCompletionRequestFunctionCall.of("auto"));
            
            log.info("EXERCISE_TRACKING_FUNCTION_CALLING_SETUP_SUCCESS|USER_ID: {}|FUNCTIONS: {}", 
                    currentUserId, chatFunctionList.size());
            
        } catch (Exception e) {
            log.error("EXERCISE_TRACKING_FUNCTION_CALLING_SETUP_ERROR", e);
            throw new RuntimeException("Failed to setup function calling", e);
        }
    }

    /**
     * 降级处理：当function calling失败时的备用方案
     */
    private AiChatGptVo handleFallback(AiInterface<AiChatGptQo, AiChatGptVo> aiInterface, 
                                       SceneHandlerQo handlerQo, Exception originalError) {
        log.warn("Using fallback mode due to function calling error: {}", originalError.getMessage());
        
        try {
            // 获取原始提示词用于fallback
            String originalPrompts = PromptsTemplateTool.getPromptsTemplate(MongoPromptsTemplateEnum.EXERCISE_TRACKING_SYSTEM_PROMPTS);
            
            // 使用简化的系统提示词
            String fallbackSystemPrompts = buildEnhancedSystemPrompts(handlerQo, originalPrompts);
            
            // 构建简单的AI请求（不使用function calling）
            AiChatGptQo fallbackQo = this.buildSystemPrompts(handlerQo, fallbackSystemPrompts)
                .buildUserPrompts(handlerQo, handlerQo.getPrompts())
                .build(handlerQo);
            
            // 使用简单聊天
            AiChatGptVo result = aiInterface.simpleChat(fallbackQo);
            
            log.info("EXERCISE_TRACKING_FALLBACK_SUCCESS|{}", result.getAiGc());
            return result;
            
        } catch (Exception fallbackError) {
            log.error("Fallback also failed", fallbackError);
            
            // 最终降级：返回错误信息
            AiChatGptVo errorResult = new AiChatGptVo();
            errorResult.setAiGc("Sorry, the exercise tracking service is temporarily unavailable. Please try again later.");
            return errorResult;
        }
    }

    /**
     * 处理运动记录卡片生成（支持多张卡片）
     */
    private AiChatGptVo processExerciseRecordCard(AiChatGptVo originalResult, AiChatGptQo aiChatGptQo, AiChatGptVo finalResult) {
        try {
            log.info("PROCESS_EXERCISE_RECORD_CARD_START|CHECKING_FOR_EXERCISE_RECORDS");
            
            // 检查是否调用了generateExerciseRecord工具，支持多个记录
            List<String> exerciseRecordDataList = extractMultipleExerciseRecordData(aiChatGptQo);
            
            if (exerciseRecordDataList != null && !exerciseRecordDataList.isEmpty()) {
                log.info("EXERCISE_RECORD_CARDS_FOUND|COUNT:{}|FIRST_RECORD_PREVIEW:{}", 
                        exerciseRecordDataList.size(),
                        exerciseRecordDataList.get(0).length() > 200 ? 
                            exerciseRecordDataList.get(0).substring(0, 200) + "..." : 
                            exerciseRecordDataList.get(0));
                
                // 如果只有一张卡片，使用原有逻辑
                if (exerciseRecordDataList.size() == 1) {
                    String completeCardData = buildCompleteExerciseCard(exerciseRecordDataList.get(0));
                originalResult.setData(completeCardData);
                originalResult.setDataType(BizMessageTypeEnum.CARD.getCode());
                    log.info("SINGLE_EXERCISE_RECORD_CARD_GENERATED|CARD_LENGTH:{}", completeCardData.length());
                } else {
                    // 多张卡片：直接设置标识，在回调中处理
                    String multiCardIdentifier = "MULTIPLE_CARDS:" + exerciseRecordDataList.size();
                    originalResult.setData(multiCardIdentifier);
                    originalResult.setDataType(BizMessageTypeEnum.CARD.getCode());
                    
                    // 将卡片数据存储到finalResult中供回调使用
                    finalResult.setData(multiCardIdentifier);
                    finalResult.setDataType(BizMessageTypeEnum.CARD.getCode());
                    
                    // 将多卡片数据存储到静态Map中，以请求ID为key
                    String requestId = finalResult.getId();
                    MULTIPLE_CARD_DATA_STORAGE.put(requestId, exerciseRecordDataList);
                    
                    log.info("MULTIPLE_EXERCISE_RECORD_CARDS_PREPARED|COUNT:{}|IDENTIFIER:{}|REQUEST_ID:{}|STORAGE_SET:true", 
                            exerciseRecordDataList.size(), multiCardIdentifier, requestId);
                    
                    // 验证静态存储数据是否正确设置
                    List<String> verifyData = MULTIPLE_CARD_DATA_STORAGE.get(requestId);
                    log.info("STORAGE_VERIFICATION|STORED_COUNT:{}|MATCHES_ORIGINAL:{}", 
                            verifyData != null ? verifyData.size() : 0,
                            verifyData != null && verifyData.size() == exerciseRecordDataList.size());
                }
            } else {
                log.info("NO_EXERCISE_RECORD_CARDS_FOUND|SKIPPING_CARD_GENERATION");
            }
            
            return originalResult;
            
        } catch (Exception e) {
            log.error("PROCESS_EXERCISE_RECORD_CARD_ERROR", e);
            // 处理失败时，返回原始响应
            return originalResult;
        }
    }

    /**
     * 从静态存储中获取多卡片数据
     */
    private List<String> getMultipleCardDataFromStorage(String requestId) {
        try {
            return MULTIPLE_CARD_DATA_STORAGE.get(requestId);
        } catch (Exception e) {
            log.error("GET_MULTIPLE_CARD_DATA_FROM_STORAGE_ERROR|REQUEST_ID:{}", requestId, e);
            return null;
        }
    }

    /**
     * 检查是否有卡片数据，如果有则推送到IM（支持多张卡片循环推送）
     * 参考ProductRecommendSceneServiceImpl的实现
     */
    private void sendCardToImIfExists(AiChatGptVo result, SceneHandlerQo handlerQo) {
        try {
            log.info("SEND_CARD_TO_IM_START|HAS_DATA:{}|DATA_TYPE:{}|DATA_CONTENT:{}", 
                    result.getData() != null, 
                    result.getDataType(),
                    result.getData() != null ? 
                        (result.getData().length() > 100 ? result.getData().substring(0, 100) + "..." : result.getData()) : 
                        "null");
            
            // 检查是否有卡片数据
            if (result.getData() != null && BizMessageTypeEnum.CARD.getCode().equals(result.getDataType())) {
                String cardData = result.getData();
                log.info("EXERCISE_TRACKING_CARD_DETECTED|DATA_LENGTH:{}|CARD_DATA:{}", 
                        cardData.length(),
                        cardData.length() > 200 ? cardData.substring(0, 200) + "..." : cardData);
                
                // 检查是否是多卡片标识
                if (cardData.startsWith("MULTIPLE_CARDS:")) {
                    log.info("MULTIPLE_EXERCISE_CARDS_DETECTED|PROCESSING_MULTI_CARD_PUSH|IDENTIFIER:{}", cardData);
                    
                    // 在推送前再次验证静态存储数据
                    String requestId = result.getId();
                    List<String> preVerifyData = getMultipleCardDataFromStorage(requestId);
                    log.info("PRE_PUSH_STORAGE_VERIFICATION|REQUEST_ID:{}|STORED_COUNT:{}|DATA_AVAILABLE:{}", 
                            requestId,
                            preVerifyData != null ? preVerifyData.size() : 0,
                            preVerifyData != null && !preVerifyData.isEmpty());
                    
                    sendMultipleCardsDirectly(handlerQo, requestId);
                    //清缓存
                    MULTIPLE_CARD_DATA_STORAGE.remove(requestId);
                    log.info("CLEANED_UP_MULTIPLE_CARD_DATA_STORAGE|REQUEST_ID:{}", requestId);
                } else {
                    log.info("SINGLE_EXERCISE_CARD_DETECTED|PROCESSING_SINGLE_CARD_PUSH");
                    // 单张卡片，使用原有逻辑
                    sendSingleCardToIm(cardData, handlerQo);
                }
                
                // 重置data避免二次发送卡片
                result.setData(null);
                log.info("EXERCISE_TRACKING_CARDS_SENT_TO_IM_COMPLETED|DATA_RESET");
            } else {
                log.info("NO_CARD_DATA_TO_SEND|SKIPPING_IM_PUSH");
            }
        } catch (Exception e) {
            log.error("SEND_EXERCISE_CARD_TO_IM_ERROR", e);
            // 推送失败不影响主流程，只记录错误日志
        }
    }
    
    /**
     * 推送单张卡片到IM
     */
    private void sendSingleCardToIm(String cardData, SceneHandlerQo handlerQo) {
        try {
            // 构建IM卡片消息
            ImBotMessageQo imBotMessageQo = ImBotMessageQo.of(
                handlerQo.getDoChatHandlerBaseDto(), 
                null, 
                cardData, 
                BizMessageTypeEnum.CARD.getCode(), 
                ImMessageStatusEnum.FINISHED.getCode()
            );
            
            // 推送卡片消息到IM
            this.sendImMessage(imBotMessageQo);
            log.info("SINGLE_EXERCISE_CARD_SENT_TO_IM");
        } catch (Exception e) {
            log.error("SEND_SINGLE_EXERCISE_CARD_ERROR", e);
        }
    }
    
    /**
     * 直接推送多张卡片到IM
     */
    private void sendMultipleCardsDirectly(SceneHandlerQo handlerQo, String requestId) {
        try {
            log.info("SEND_MULTIPLE_CARDS_DIRECTLY_START|RETRIEVING_FROM_STORAGE|REQUEST_ID:{}", requestId);
            
            // 从静态存储获取多卡片数据
            List<String> exerciseRecordDataList = getMultipleCardDataFromStorage(requestId);
            
            if (exerciseRecordDataList != null && !exerciseRecordDataList.isEmpty()) {
                log.info("SENDING_MULTIPLE_EXERCISE_CARDS_DIRECTLY|COUNT:{}|FIRST_RECORD_LENGTH:{}", 
                        exerciseRecordDataList.size(),
                        exerciseRecordDataList.get(0).length());
                
                // 循环推送每张卡片
                for (int i = 0; i < exerciseRecordDataList.size(); i++) {
                    String exerciseData = exerciseRecordDataList.get(i);
                    log.info("PROCESSING_CARD|INDEX:{}|RAW_DATA_LENGTH:{}|RAW_DATA_PREVIEW:{}", 
                            i + 1, 
                            exerciseData.length(),
                            exerciseData.length() > 100 ? exerciseData.substring(0, 100) + "..." : exerciseData);
                    
                    // 构建完整的卡片数据
                    String completeCardData = buildCompleteExerciseCard(exerciseData, i + 1);
                    log.info("CARD_BUILT|INDEX:{}|COMPLETE_CARD_LENGTH:{}|COMPLETE_CARD_PREVIEW:{}", 
                            i + 1, 
                            completeCardData.length(),
                            completeCardData.length() > 200 ? completeCardData.substring(0, 200) + "..." : completeCardData);
                    
                    // 构建IM卡片消息
                    ImBotMessageQo imBotMessageQo = ImBotMessageQo.of(
                        handlerQo.getDoChatHandlerBaseDto(), 
                        null, 
                        completeCardData, 
                        BizMessageTypeEnum.CARD.getCode(), 
                        ImMessageStatusEnum.FINISHED.getCode()
                    );
                    
                    log.info("IM_MESSAGE_BUILT|INDEX:{}|ABOUT_TO_SEND", i + 1);
                    
                    // 推送卡片消息到IM
                    this.sendImMessage(imBotMessageQo);
                    
                    log.info("MULTIPLE_EXERCISE_CARD_SENT_DIRECTLY|INDEX:{}|TOTAL:{}", i + 1, exerciseRecordDataList.size());
                    
                    // 添加短暂延迟，避免推送过快
                    if (i < exerciseRecordDataList.size() - 1) {
                        try {
                            Thread.sleep(100); // 100ms延迟
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            log.warn("CARD_PUSH_DELAY_INTERRUPTED");
                        }
                    }
                }
                
                log.info("ALL_MULTIPLE_EXERCISE_CARDS_SENT_DIRECTLY|TOTAL_COUNT:{}", exerciseRecordDataList.size());
            } else {
                log.warn("NO_MULTIPLE_CARD_DATA_FOUND_IN_STORAGE|REQUEST_ID:{}|DATA_IS_NULL:{}", requestId, exerciseRecordDataList == null);
            }
        } catch (Exception e) {
            log.error("SEND_MULTIPLE_EXERCISE_CARDS_DIRECTLY_ERROR|REQUEST_ID:{}", requestId, e);
        }
    }

    /**
     * 从消息历史中提取运动记录数据
     */
    private List<String> extractMultipleExerciseRecordData(AiChatGptQo aiChatGptQo) {
        try {
            List<String> exerciseRecordDataList = new ArrayList<>();
            // 检查消息历史中是否有generateExerciseRecord的function调用结果
            for (AiChatGptQo.MessageDetail message : aiChatGptQo.getMessageDetailList()) {
                if (message.getRole() == com.theokanning.openai.completion.chat.ChatMessageRole.FUNCTION) {
                    String content = message.getContent();
                    // 只有当内容是JSON格式且包含运动记录字段时才认为是运动记录数据
                    // 使用与运动工具生成的JSON结构完全匹配的检测条件
                    if (content != null && content.trim().startsWith("{") && content.trim().endsWith("}")) {
                        // 检测运动记录的核心字段：status, recordDate, taskId, name, duration
                        // 这些字段是运动工具generateExerciseRecord生成的JSON的必备字段
                        if (content.contains("\"status\"") && content.contains("\"recordDate\"") && 
                            content.contains("\"taskId\"") && content.contains("\"name\"") && 
                            content.contains("\"duration\"") && content.contains("\"durationUnit\"")) {
                            // 这是运动记录数据，添加到列表中
                            exerciseRecordDataList.add(content);
                            log.info("EXERCISE_RECORD_DATA_EXTRACTED|DATA_LENGTH:{}|PREVIEW:{}", 
                                    content.length(), 
                                    content.length() > 100 ? content.substring(0, 100) + "..." : content);
                        }
                    }
                }
            }
            
            log.info("EXTRACT_MULTIPLE_EXERCISE_RECORDS_COMPLETED|TOTAL_COUNT:{}", exerciseRecordDataList.size());
            return exerciseRecordDataList;
        } catch (Exception e) {
            log.error("EXTRACT_EXERCISE_RECORD_DATA_ERROR", e);
            return null;
        }
    }

    /**
     * 构建完整的运动记录卡片结构
     * 包含外层固定字段和内层数据字段
     */
    private String buildCompleteExerciseCard(String exerciseDataJson) {
        return buildCompleteExerciseCard(exerciseDataJson, 0);
    }
    
    /**
     * 构建完整的运动记录卡片结构（支持序号）
     * 包含外层固定字段和内层数据字段
     */
    private String buildCompleteExerciseCard(String exerciseDataJson, int sequenceNumber) {
        try {
            StringBuilder cardBuilder = new StringBuilder();
            cardBuilder.append("{\n");
            cardBuilder.append("  \"content\": {\n");
            cardBuilder.append("    \"title\": \"Exercise Record");
            if (sequenceNumber > 0) {
                cardBuilder.append(" #").append(sequenceNumber);
            }
            cardBuilder.append("\",\n");
            cardBuilder.append("    \"subTitle\": \"Workout Tracking\",\n");
            cardBuilder.append("    \"summary\": \"Your exercise activity has been recorded\",\n");
            cardBuilder.append("    \"coverImgUrl\": \"\",\n");
            cardBuilder.append("    \"jumpUrl\": \"\",\n");
            cardBuilder.append("    \"buttonConfirmText\": \"Confirm\",\n");
            cardBuilder.append("    \"buttonCancelText\": \"\",\n");
            cardBuilder.append("    \"status\": 1,\n");
            cardBuilder.append("    \"contentId\": \"exercise_").append(System.currentTimeMillis());
            if (sequenceNumber > 0) {
                cardBuilder.append("_").append(sequenceNumber);
            }
            cardBuilder.append("\",\n");
            cardBuilder.append("    \"type\": 3,\n");
            // 将 exerciseDataJson 转义为字符串，而不是直接拼接JSON对象
            String escapedDataJson = exerciseDataJson.replace("\"", "\\\"").replace("\n", "\\n").replace("\r", "\\r");
            cardBuilder.append("    \"data\": \"").append(escapedDataJson).append("\"\n");
            cardBuilder.append("  },\n");
            cardBuilder.append("  \"bizMsgType\": 5,\n");
            cardBuilder.append("  \"msgStatus\": 1\n");
            cardBuilder.append("}");
            
            return cardBuilder.toString();
        } catch (Exception e) {
            log.error("BUILD_COMPLETE_EXERCISE_CARD_ERROR", e);
            return exerciseDataJson; // 降级返回原始数据
        }
    }

    /**
     * 运动追踪场景的流式回调处理器
     * 实现分阶段流式处理的用户体验
     */
} 