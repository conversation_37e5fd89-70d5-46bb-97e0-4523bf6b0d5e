package com.evydtech.chatbot.scene.function;

import com.evydtech.chatbot.feign.domain.DailyDetailVo;
import com.evydtech.chatbot.feign.domain.ExerciseDailyVo;
import com.evydtech.chatbot.feign.domain.ExerciseListVo;
import com.evydtech.chatbot.feign.service.AiClockService;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 运动追踪场景Function Calling工具类
 * 
 * @author: AI Assistant
 * @description: 提供运动追踪相关的函数调用工具
 */
@Slf4j
@Component
public class ExerciseTrackingFunctionTools {

    @Autowired
    private AiClockService aiClockService;
    
    /**
     * 当前用户ID的线程本地存储
     */
    private static final ThreadLocal<String> CURRENT_USER_ID = new ThreadLocal<>();
    
    /**
     * 设置当前用户ID
     * 
     * @param userId 用户ID
     */
    public void setCurrentUserId(String userId) {
        CURRENT_USER_ID.set(userId);
        log.debug("Set current user ID: {}", userId);
    }
    
    /**
     * 获取当前用户ID
     * 
     * @return 用户ID
     */
    private String getCurrentUserId() {
        return CURRENT_USER_ID.get();
    }
    
    /**
     * 清除当前用户ID
     */
    public void clearCurrentUserId() {
        CURRENT_USER_ID.remove();
    }

    /**
     * 获取用户日常任务详情（包含运动信息）
     * 根据指定日期范围获取用户的运动记录和目标信息，用于提供个性化运动分析上下文
     * 
     * @param request 获取日常任务详情的请求参数
     * @return 日常任务详情信息的JSON字符串
     */
    public String getDailyDetail(GetDailyDetailRequest request) {
        // 自动获取当前用户ID
        String userId = getCurrentUserId();
        log.info("Function call: getDailyDetail with userId: {}, startDate: {}, endDate: {}", 
                userId, request.startDate, request.endDate);
        
        try {
            // 参数验证
            if (userId == null || userId.trim().isEmpty()) {
                return "\"Tool call failed: User ID is not available. Please ensure user is properly authenticated and try again.\"";
            }
            
            // 如果没有指定日期，默认查询今天
            String startDate = request.startDate;
            String endDate = request.endDate;
            
            if (startDate == null || startDate.trim().isEmpty()) {
                startDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }
            if (endDate == null || endDate.trim().isEmpty()) {
                endDate = startDate;
            }
            
            DailyDetailVo dailyDetailVo = aiClockService.getDailyDetail(startDate, endDate, userId);
            
            if (dailyDetailVo != null && dailyDetailVo.getCode() == 0 && dailyDetailVo.getData() != null) {
                StringBuilder result = new StringBuilder();
                result.append("User's daily task details (").append(startDate).append(" to ").append(endDate).append("):\\n\\n");
                
                for (String date : dailyDetailVo.getData().keySet()) {
                    result.append(date).append(":\\n");
                    
                    List<DailyDetailVo.TaskCategory> categories = dailyDetailVo.getData().get(date);
                    if (categories.isEmpty()) {
                        result.append("  No task records\\n\\n");
                        continue;
                    }
                    
                    for (DailyDetailVo.TaskCategory category : categories) {
                        if ("physical_activity".equals(category.getType())) {
                            result.append("  ").append(category.getTitle()).append(":\\n");
                            
                            if (category.getGoals() != null && !category.getGoals().isEmpty()) {
                                for (DailyDetailVo.TaskGoal goal : category.getGoals()) {
                                    result.append("    ").append(goal.getTitle()).append(":\\n");
                                    result.append("      Completed: ").append(goal.getCompletedValue()).append(" ")
                                          .append(goal.getGoal().getGoalUnit()).append("\\n");
                                    result.append("      Progress: ").append(goal.getCompletedPct()).append("%\\n");
                                    if (goal.getGoal().getGoalMinValue() != null && goal.getGoal().getGoalMaxValue() != null) {
                                        result.append("      Goal: ").append(goal.getGoal().getGoalMinValue()).append("-")
                                              .append(goal.getGoal().getGoalMaxValue()).append(" ")
                                              .append(goal.getGoal().getGoalUnit()).append("\\n");
                                    } else {
                                        result.append("      Goal: ").append(goal.getGoal().getGoalValue()).append(" ")
                                              .append(goal.getGoal().getGoalUnit()).append("\\n");
                                    }
                                }
                            }
                            result.append("\\n");
                        }
                    }
                }
                
                log.info("getDailyDetail success for user: {}", userId);
                // 返回JSON字符串格式
                return "\"" + result.toString().replace("\"", "\\\"") + "\"";
            } else {
                log.warn("getDailyDetail failed, code: {}", 
                        dailyDetailVo != null ? dailyDetailVo.getCode() : "null");
                return "\"Tool call failed: Unable to retrieve user's daily task details. Please inform the user that their exercise history is currently unavailable and continue with general exercise analysis.\"";
            }
        } catch (Exception e) {
            log.error("getDailyDetail error", e);
            return "\"Tool call failed: Daily task details service is currently unavailable. Please inform the user that their exercise history cannot be accessed at the moment and continue with general exercise analysis.\"";
        }
    }

    /**
     * 生成运动记录参数
     * 用于收集用户描述的运动信息并生成记录运动的JSON参数
     * 
     * @param request 运动记录请求参数
     * @return 运动记录参数的JSON字符串
     */
    public String generateExerciseRecord(GenerateExerciseRecordRequest request) {
        log.info("Function call: generateExerciseRecord with recordDate: {}, taskId: {}, duration: {}", 
                request.recordDate, request.taskId, request.duration);
        
        try {
            // 使用增强版统一校验助手进行参数校验（包含缓存机制）
            ValidationHelper.ValidationResult validation = ValidationHelper.validateExerciseRecord(
                request.recordDate, request.taskId, request.duration, 
                request.distance, request.avgHeartRate, request.intensityLevel,
                aiClockService
            );
            
            if (!validation.isValid()) {
                // 如果有智能询问提示，优先返回友好的中文提示
                if (validation.getSmartQuestionPrompt() != null) {
                    log.info("generateExerciseRecord validation failed, returning smart question prompt");
                    return "\"" + ValidationHelper.escapeJsonString(validation.getSmartQuestionPrompt()) + "\"";
                }
                // 否则返回标准错误信息
                return "\"" + validation.getErrorMessage() + "\"";
            }
            
            // 获取用户当前的运动目标信息
            int goalValue = 30; // 默认目标30分钟
            String goalUnit = "Min";
            
            String userId = getCurrentUserId();
            if (userId != null && !userId.trim().isEmpty()) {
                try {
                    DailyDetailVo dailyDetailVo = aiClockService.getDailyDetail(request.recordDate, request.recordDate, userId);
                    if (dailyDetailVo != null && dailyDetailVo.getCode() == 0 && dailyDetailVo.getData() != null) {
                        for (String date : dailyDetailVo.getData().keySet()) {
                            List<DailyDetailVo.TaskCategory> categories = dailyDetailVo.getData().get(date);
                            for (DailyDetailVo.TaskCategory category : categories) {
                                if ("physical_activity".equals(category.getType()) && category.getGoals() != null) {
                                    for (DailyDetailVo.TaskGoal goal : category.getGoals()) {
                                        if (goal.getGoal() != null) {
                                            goalValue = goal.getGoal().getGoalValue();
                                            goalUnit = goal.getGoal().getGoalUnit();
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("Failed to get exercise goal data, using defaults", e);
                }
            }
            
            // 从缓存获取运动类型信息（name、icon和trackRoute）
            String exerciseName = "Unknown Exercise";
            String exerciseIcon = "";
            boolean trackRoute = false;
            
            ValidationHelper.ExerciseTypeInfo exerciseInfo = ValidationHelper.getExerciseTypeInfo(request.taskId);
            if (exerciseInfo != null) {
                exerciseName = exerciseInfo.getName();
                exerciseIcon = exerciseInfo.getIcon();
                trackRoute = exerciseInfo.isTrackRoute();
                log.info("Exercise type info retrieved from cache: taskId={}, name={}, icon={}, trackRoute={}", 
                        request.taskId, exerciseName, exerciseIcon, trackRoute);
            } else {
                // 如果缓存中没有找到，使用默认值
                log.warn("Exercise type info not found in cache for taskId: {}, using defaults", request.taskId);
                if (request.taskId == 99999) {
                    exerciseName = "Other Exercise";
                    exerciseIcon = "https://healthresource-**********.cos.ap-singapore.myqcloud.com/30e19def-47eb-45b8-84f0-8fde00ef0195.png?imageMogr2/thumbnail/24x24/auto-orient";
                    trackRoute = false;
                }
            }
            
            // 使用用户提供的运动强度（不进行推算）
            String userProvidedIntensityLevel = request.intensityLevel != null ? 
                    request.intensityLevel.toLowerCase().trim() : "light";
            
            // 规范化强度值
            switch (userProvidedIntensityLevel) {
                case "轻度":
                    userProvidedIntensityLevel = "light";
                    break;
                case "中等":
                    userProvidedIntensityLevel = "moderate";
                    break;
                case "高强度":
                    userProvidedIntensityLevel = "high";
                    break;
                default:
                    // 保持原值，如果不在预期范围内，后续验证会处理
                    break;
            }
            
            // 生成新的卡片数据结构（包含所有必需字段）
            StringBuilder jsonResult = new StringBuilder();
            jsonResult.append("{\n");
            jsonResult.append("  \"status\": 1,\n");
            jsonResult.append("  \"recordDate\": \"").append(request.recordDate).append("\",\n");
            jsonResult.append("  \"taskId\": ").append(request.taskId).append(",\n");
            jsonResult.append("  \"name\": \"").append(exerciseName).append("\",\n");
            jsonResult.append("  \"icon\": \"").append(exerciseIcon).append("\",\n");
            jsonResult.append("  \"duration\": ").append(request.duration).append(",\n");
            jsonResult.append("  \"durationUnit\": \"Min\",\n");
            
            // 距离字段：只有当运动类型支持路径追踪时才包含
            if (trackRoute) {
            int distanceValue = (request.distance != null && request.distance > 0) ? request.distance : 0;
            jsonResult.append("  \"distance\": ").append(distanceValue).append(",\n");
            jsonResult.append("  \"distanceUnit\": \"Km\",\n");
            }
            
            // 心率字段（必须由用户明确提供，不允许自动设置默认值）
            // 如果通过ValidationHelper验证，说明用户已经提供了有效的心率值
            jsonResult.append("  \"avgHeartRate\": ").append(request.avgHeartRate).append(",\n");
            jsonResult.append("  \"avgHeartRateUnit\": \"BPM\",\n");
            
            // 运动强度
            jsonResult.append("  \"intensityLevel\": \"").append(userProvidedIntensityLevel).append("\",\n");
            
            // 目标信息
            jsonResult.append("  \"goal\": {\n");
            jsonResult.append("    \"goalValue\": ").append(goalValue).append(",\n");
            jsonResult.append("    \"goalUnit\": \"").append(goalUnit).append("\"\n");
            jsonResult.append("  }\n");
            jsonResult.append("}");
            
            log.info("generateExerciseRecord success, generated enhanced record for {} seconds with intensity: {}, trackRoute: {}", 
                    request.duration, userProvidedIntensityLevel, trackRoute);
            return jsonResult.toString();
            
        } catch (Exception e) {
            log.error("generateExerciseRecord error", e);
            return "\"Tool call failed: Unable to generate exercise record parameters. Please inform the user that the exercise recording function is currently unavailable and continue with exercise analysis only.\"";
        }
    }

    /**
     * 获取所有运动类型列表
     * 获取系统中提供的全部可以允许用户生成记录信息卡片的运动类型列表和对应的taskId
     * 
     * @param request 获取运动列表的请求参数（无需参数）
     * @return 运动类型列表的JSON字符串
     */
    public String getAllExerciseList(GetAllExerciseListRequest request) {
        log.info("Function call: getAllExerciseList");
        
        try {
            ExerciseListVo exerciseListVo = aiClockService.getAllExerciseList();
            
            if (exerciseListVo != null && exerciseListVo.getCode() == 0 && exerciseListVo.getData() != null) {
                StringBuilder result = new StringBuilder();
                result.append("Available exercise types:\\n\\n");
                
                for (ExerciseListVo.ExerciseType exerciseType : exerciseListVo.getData()) {
                    result.append("🏃 ").append(exerciseType.getName()).append(":\\n");
                    result.append("  Task ID: ").append(exerciseType.getId()).append("\\n");
                    result.append("  Track Route: ").append(exerciseType.getTrackRoute()).append("\\n");
                    result.append("\\n");
                }
                
                result.append("Note: If user's exercise type is not in the list, use taskId=99999 for unknown exercise types.\\n");
                
                log.info("getAllExerciseList success, found {} exercise types", exerciseListVo.getData().size());
                // 返回JSON字符串格式
                return "\"" + result.toString().replace("\"", "\\\"") + "\"";
            } else {
                log.warn("getAllExerciseList failed, code: {}", 
                        exerciseListVo != null ? exerciseListVo.getCode() : "null");
                return "\"Tool call failed: Unable to retrieve exercise types list. Please use taskId=99999 for unknown exercise types and continue with exercise recording.\"";
            }
        } catch (Exception e) {
            log.error("getAllExerciseList error", e);
            return "\"Tool call failed: Exercise types list service is currently unavailable. Please use taskId=99999 for unknown exercise types and continue with exercise recording.\"";
        }
    }

    /**
     * 获取用户运动日报
     * 获取当前用户自身指定时间范围内的打卡的全部详细记录，用于用户进行咨询自己的运动信息时提供帮助
     * 
     * @param request 获取运动日报的请求参数
     * @return 运动日报信息的JSON字符串
     */
    public String getExerciseDaily(GetExerciseDailyRequest request) {
        // 自动获取当前用户ID
        String userId = getCurrentUserId();
        log.info("Function call: getExerciseDaily with userId: {}, startDate: {}, endDate: {}", 
                userId, request.startDate, request.endDate);
        
        try {
            // 参数验证
            if (userId == null || userId.trim().isEmpty()) {
                return "\"Tool call failed: User ID is not available. Please ensure user is properly authenticated and try again.\"";
            }
            
            // 如果没有指定日期，默认查询今天
            String startDate = request.startDate;
            String endDate = request.endDate;
            
            if (startDate == null || startDate.trim().isEmpty()) {
                startDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }
            if (endDate == null || endDate.trim().isEmpty()) {
                endDate = startDate;
            }
            
            ExerciseDailyVo exerciseDailyVo = aiClockService.getExerciseDaily(startDate, endDate, userId);
            
            if (exerciseDailyVo != null && exerciseDailyVo.getCode() == 0 && exerciseDailyVo.getData() != null) {
                StringBuilder result = new StringBuilder();
                result.append("User's exercise daily records (").append(startDate).append(" to ").append(endDate).append("):\\n\\n");
                
                for (String date : exerciseDailyVo.getData().keySet()) {
                    result.append(date).append(":\\n");
                    
                    List<ExerciseDailyVo.ExerciseItem> exercises = exerciseDailyVo.getData().get(date);
                    if (exercises.isEmpty()) {
                        result.append("  No exercise records\\n\\n");
                        continue;
                    }
                    
                    for (ExerciseDailyVo.ExerciseItem exercise : exercises) {
                        result.append("  ").append(exercise.getName()).append(":\\n");
                        if (exercise.getDuration() != null) {
                            result.append("    Duration: ").append(exercise.getDuration()).append(" seconds\\n");
                        }
                        if (exercise.getDistance() != null) {
                            result.append("    Distance: ").append(exercise.getDistance()).append(" meters\\n");
                        }
                        if (exercise.getCalories() != null) {
                            result.append("    Calories: ").append(exercise.getCalories()).append(" kcal\\n");
                        }
                        if (exercise.getTrackRoute() != null) {
                            result.append("    Track Route: ").append(exercise.getTrackRoute()).append("\\n");
                        }
                        result.append("\\n");
                    }
                }
                
                log.info("getExerciseDaily success for user: {}", userId);
                // 返回JSON字符串格式
                return "\"" + result.toString().replace("\"", "\\\"") + "\"";
            } else {
                log.warn("getExerciseDaily failed, code: {}", 
                        exerciseDailyVo != null ? exerciseDailyVo.getCode() : "null");
                return "\"Tool call failed: Unable to retrieve user's exercise daily records. Please inform the user that their exercise history is currently unavailable and continue with general exercise analysis.\"";
            }
        } catch (Exception e) {
            log.error("getExerciseDaily error", e);
            return "\"Tool call failed: Exercise daily records service is currently unavailable. Please inform the user that their exercise history cannot be accessed at the moment and continue with general exercise analysis.\"";
        }
    }

    /**
     * 转换相对日期公式为标准日期格式
     * 大模型负责将用户自然语言转换为标准化的相对日期公式，工具负责基于系统当前日期计算具体日期
     * 
     * @param request 日期转换请求参数
     * @return 标准格式的日期字符串 (yyyy-MM-dd) 或错误信息
     */
    public String convertNaturalDateToStandard(ConvertDateRequest request) {
        log.info("Function call: convertNaturalDateToStandard with dateFormula: '{}'", request.dateFormula);
        
        try {
            if (request.dateFormula == null || request.dateFormula.trim().isEmpty()) {
                return "\"Tool call failed: Date formula is required. Please provide a standardized date formula like 'TODAY', 'TODAY-1', etc.\"";
            }
            
            String formula = request.dateFormula.trim().toUpperCase();
            LocalDate currentDate = LocalDate.now();
            LocalDate targetDate;
            
            if ("TODAY".equals(formula)) {
                targetDate = currentDate;
            } else if (formula.startsWith("TODAY")) {
                // 处理 TODAY+N 或 TODAY-N 格式
                String operation = formula.substring(5); // 去掉 "TODAY"
                if (operation.startsWith("+")) {
                    int days = Integer.parseInt(operation.substring(1));
                    targetDate = currentDate.plusDays(days);
                } else if (operation.startsWith("-")) {
                    int days = Integer.parseInt(operation.substring(1));
                    targetDate = currentDate.minusDays(days);
                } else {
                    return "\"Tool call failed: Invalid date formula format. Expected format: TODAY, TODAY+N, or TODAY-N\"";
                }
            } else if (formula.startsWith("WEEK")) {
                // 处理 WEEK+N 或 WEEK-N 格式（以周为单位）
                String operation = formula.substring(4); // 去掉 "WEEK"
                if (operation.startsWith("+")) {
                    int weeks = Integer.parseInt(operation.substring(1));
                    targetDate = currentDate.plusWeeks(weeks);
                } else if (operation.startsWith("-")) {
                    int weeks = Integer.parseInt(operation.substring(1));
                    targetDate = currentDate.minusWeeks(weeks);
                } else {
                    return "\"Tool call failed: Invalid week formula format. Expected format: WEEK+N or WEEK-N\"";
                }
            } else {
                return "\"Tool call failed: Unsupported date formula. Supported formats: TODAY, TODAY±N, WEEK±N\"";
            }
            
            // 验证日期合理性：不允许记录未来日期
            if (targetDate.isAfter(currentDate)) {
                return "\"Tool call failed: Cannot record exercise for future dates. Please use a date that is today or in the past.\"";
            }
            
            // 验证日期合理性：不允许记录超过1个月前的日期
            if (targetDate.isBefore(currentDate.minusMonths(1))) {
                return "\"Tool call failed: Cannot record exercise for dates older than 1 month. Please use a more recent date.\"";
            }
            
            String resultDate = targetDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            log.info("convertNaturalDateToStandard success: '{}' -> '{}'", request.dateFormula, resultDate);
            
            return "\"" + resultDate + "\"";
            
        } catch (NumberFormatException e) {
            log.error("Invalid number in date formula: {}", request.dateFormula, e);
            return "\"Tool call failed: Invalid number in date formula. Please check the format.\"";
        } catch (Exception e) {
            log.error("convertNaturalDateToStandard error", e);
            return "\"Tool call failed: Unable to convert date formula. Please provide a valid date formula.\"";
        }
    }

    /**
     * 获取日常任务详情请求参数类
     */
    public static class GetDailyDetailRequest {
        @JsonProperty("startDate")
        @JsonPropertyDescription("Start date for querying daily details, format: yyyy-MM-dd, e.g.: 2024-01-01 (optional, defaults to today)")
        public String startDate;

        @JsonProperty("endDate")
        @JsonPropertyDescription("End date for querying daily details, format: yyyy-MM-dd, e.g.: 2024-01-01 (optional, defaults to startDate)")
        public String endDate;
    }

    /**
     * 生成运动记录请求参数类
     */
    public static class GenerateExerciseRecordRequest {
        @JsonProperty("recordDate")
        @JsonPropertyDescription("Record date, format: yyyy-MM-dd, e.g.: 2024-01-01 (required)")
        public String recordDate;

        @JsonProperty("taskId")
        @JsonPropertyDescription("Exercise task ID from getAllExerciseList, use 99999 for unknown exercise types (required)")
        public Integer taskId;

        @JsonProperty("duration")
        @JsonPropertyDescription("Exercise duration in seconds (required, must be positive and not exceed 86400 seconds/24 hours)")
        public Integer duration;

        @JsonProperty("distance")
        @JsonPropertyDescription("Exercise distance in meters (required for exercises that support route tracking, such as running, cycling, etc.)")
        public Integer distance;

        @JsonProperty("avgHeartRate")
        @JsonPropertyDescription("Average heart rate in beats per minute (required, range: 40-220 BPM)")
        public Integer avgHeartRate;

        @JsonProperty("intensityLevel")
        @JsonPropertyDescription("Exercise intensity level: light/moderate/high (required)")
        public String intensityLevel;
    }

    /**
     * 获取所有运动类型列表请求参数类（空参数类，符合OpenAI Function Calling schema要求）
     */
    public static class GetAllExerciseListRequest {
        // 添加一个可选的占位字段，确保生成正确的object schema
        @JsonProperty("placeholder")
        @JsonPropertyDescription("Optional placeholder field (not used, for schema compatibility)")
        public String placeholder;
    }

    /**
     * 获取运动日报请求参数类
     */
    public static class GetExerciseDailyRequest {
        @JsonProperty("startDate")
        @JsonPropertyDescription("Start date for querying exercise daily records, format: yyyy-MM-dd, e.g.: 2024-01-01 (optional, defaults to today)")
        public String startDate;

        @JsonProperty("endDate")
        @JsonPropertyDescription("End date for querying exercise daily records, format: yyyy-MM-dd, e.g.: 2024-01-01 (optional, defaults to startDate)")
        public String endDate;
    }
    
    /**
     * 日期转换请求参数类
     */
    public static class ConvertDateRequest {
        @JsonProperty("dateFormula")
        @JsonPropertyDescription("Standardized date formula to convert, e.g.: 'TODAY', 'TODAY-1', 'TODAY+2', 'WEEK-1' (required)")
        public String dateFormula;
    }
} 