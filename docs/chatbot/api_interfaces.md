# 聊天机器人系统 Feign 接口文档

## 文档概述

本文档详细描述了聊天机器人系统中各个业务场景所使用的 Feign 接口，包括接口地址、请求参数、响应数据结构以及调用逻辑。

### 文档版本
- **版本**: v1.0
- **创建日期**: 2025-01-27
- **维护团队**: 聊天机器人开发团队

---

## 目录

1. [AI Clock 服务接口](#1-ai-clock-服务接口)
   - [1.1 营养分析相关接口](#11-营养分析相关接口)
   - [1.2 饮水追踪相关接口](#12-饮水追踪相关接口)
   - [1.3 睡眠追踪相关接口](#13-睡眠追踪相关接口)
   - [1.4 运动追踪相关接口](#14-运动追踪相关接口)
   - [1.5 日常任务详情接口](#15-日常任务详情接口)
2. [健康代理服务接口](#2-健康代理服务接口)
   - [2.1 医院指南接口](#21-医院指南接口)
   - [2.2 医院提醒接口](#22-医院提醒接口)
   - [2.3 面部扫描结果接口](#23-面部扫描结果接口)
   - [2.4 健康咨询接口](#24-健康咨询接口)
3. [HSD 服务接口](#3-hsd-服务接口)
   - [3.1 预约检查接口](#31-预约检查接口)
   - [3.2 时间段查询接口](#32-时间段查询接口)
4. [接口配置说明](#4-接口配置说明)
5. [错误处理机制](#5-错误处理机制)

---

## 1. AI Clock 服务接口

### 基础信息
- **服务名称**: AI Clock Service
- **Feign Client**: `AiClockFeignClient`
- **服务地址**: `${evyd.micro-service.hosts.routines:http://evyd-health-manage-routines}`
- **业务封装**: `AiClockService`

### 1.1 营养分析相关接口

#### 1.1.1 获取用餐时间段列表

**接口描述**: 获取系统预设的用餐时间段信息，用于营养记录时的时间段选择。

**Feign 接口定义**:
```java
@GetMapping("/internal/ai-clock/meal-timing")
public MealTimingVo getMealTiming();
```

**业务封装**:
```java
MealTimingVo getMealTiming();
```

**请求参数**: 无

**响应数据结构**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "timings": [
      {
        "id": "1",
        "name": "早餐",
        "timeRange": "06:00-10:00"
      },
      {
        "id": "2",
        "name": "午餐",
        "timeRange": "11:00-14:00"
      },
      {
        "id": "3",
        "name": "晚餐",
        "timeRange": "17:00-21:00"
      },
      {
        "id": "4",
        "name": "零食",
        "timeRange": "全天"
      }
    ]
  }
}
```

**使用场景**: 
- NUTRITION_ANALYSIS 场景中的 Function Calling 工具
- 用于获取标准用餐时间段信息，辅助大模型提取准确的餐段信息

#### 1.1.2 获取用户营养日报

**接口描述**: 获取用户指定时间范围内的营养摄入记录。

**Feign 接口定义**:
```java
@GetMapping("/internal/ai-clock/nutrition-daily")
public NutritionDailyVo getNutritionDaily(
        @RequestParam("startDate") String startDate,
        @RequestParam("endDate") String endDate,
        @RequestParam("userId") String userId);
```

**业务封装**:
```java
NutritionDailyVo getNutritionDaily(String startDate, String endDate, String userId);
```

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| startDate | String | 是 | 开始日期，格式：yyyy-MM-dd |
| endDate | String | 是 | 结束日期，格式：yyyy-MM-dd |
| userId | String | 是 | 用户ID |

**响应数据结构**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "date": "2025-01-27",
        "meals": [
          {
            "timing": "1",
            "timingName": "早餐",
            "totalCalories": 450,
            "foods": [
              {
                "foodName": "燕麦粥",
                "servingNumber": 1,
                "servingUnit": "碗",
                "calories": 300,
                "carbohydrate": 60,
                "protein": 10,
                "fat": 5
              }
            ]
          }
        ]
      }
    ]
  }
}
```

**使用场景**: 
- NUTRITION_ANALYSIS 场景中的 Function Calling 工具
- 用户咨询自己的饮食记录时提供上下文信息

#### 1.1.3 记录用户营养摄入信息

**接口描述**: 记录用户的营养摄入信息。

**Feign 接口定义**:
```java
@PostMapping("/internal/ai-clock/nutrition-clock")
public NutritionClockVo nutritionClock(@RequestBody NutritionClockQo qo);
```

**业务封装**:
```java
NutritionClockVo recordNutrition(String userId, String timing, String recordDate, 
        List<NutritionClockQo.FoodItem> foods);
```

**请求数据结构**:
```java
@Data
public class NutritionClockQo {
    /** 用户ID */
    private String userId;
    
    /** 用餐时间：1早餐，2午餐，3晚餐，4零食 */
    private String timing;
    
    /** 记录日期，格式：yyyy-MM-dd */
    private String recordDate;
    
    /** 食物列表 */
    private List<FoodItem> foods;
    
    @Data
    public static class FoodItem {
        /** 食物名称 */
        private String foodName;
        
        /** 份数 */
        private Integer servingNumber;
        
        /** 单位 */
        private String servingUnit;
        
        /** 卡路里(kcal) */
        private Integer calories;
        
        /** 碳水化合物(g) */
        private Integer carbohydrate;
        
        /** 蛋白质(g) */
        private Integer protein;
        
        /** 脂肪(g) */
        private Integer fat;
    }
}
```

**响应数据结构**:
```java
@Data
public class NutritionClockVo {
    /** 响应码 */
    private Integer code;
    
    /** 响应消息 */
    private String message;
    
    /** 响应数据 */
    private Object data;
}
```

**使用场景**: 
- NUTRITION_ANALYSIS 场景中的 Function Calling 工具
- 生成营养记录卡片参数

### 1.2 饮水追踪相关接口

#### 1.2.1 记录用户饮水信息

**接口描述**: 记录用户的饮水信息。

**Feign 接口定义**:
```java
@PostMapping("/internal/ai-clock/hydration-clock")
public HydrationClockVo hydrationClock(@RequestBody HydrationClockQo qo);
```

**业务封装**:
```java
HydrationClockVo recordHydration(String userId, String recordDate, Integer clockValue);
```

**请求数据结构**:
```java
@Data
public class HydrationClockQo {
    private String userId;
    private String recordDate;
    private Integer completedValue;
    private Integer clockValue;
}
```

**响应数据结构**:
```java
@Data
public class HydrationClockVo {
    private Integer code;
    private String message;
    private Object data;
}
```

**使用场景**: 
- HYDRATION_TRACKING 场景中的 Function Calling 工具
- 生成饮水记录卡片参数

### 1.3 睡眠追踪相关接口

#### 1.3.1 记录用户睡眠信息

**接口描述**: 记录用户的睡眠信息。

**Feign 接口定义**:
```java
@PostMapping("/internal/ai-clock/sleep-clock")
public SleepClockVo sleepClock(@RequestBody SleepClockQo qo);
```

**业务封装**:
```java
SleepClockVo recordSleep(String userId, String recordDate, Integer completedValue);
```

**请求数据结构**:
```java
@Data
public class SleepClockQo {
    private String userId;
    private String recordDate;
    private Integer completedValue;
    // 其他睡眠相关字段
}
```

**响应数据结构**:
```java
@Data
public class SleepClockVo {
    private Integer code;
    private String message;
    private Object data;
}
```

**使用场景**: 
- SLEEP_TRACKING 场景中的 Function Calling 工具
- 生成睡眠记录卡片参数

### 1.4 运动追踪相关接口

#### 1.4.1 获取用户运动日报

**接口描述**: 获取用户指定时间范围内的运动记录。

**Feign 接口定义**:
```java
@GetMapping("/internal/ai-clock/exercise-daily")
public ExerciseDailyVo getExerciseDaily(
        @RequestParam("startDate") String startDate,
        @RequestParam("endDate") String endDate,
        @RequestParam("userId") String userId);
```

**业务封装**:
```java
ExerciseDailyVo getExerciseDaily(String startDate, String endDate, String userId);
```

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| startDate | String | 是 | 开始日期，格式：yyyy-MM-dd |
| endDate | String | 是 | 结束日期，格式：yyyy-MM-dd |
| userId | String | 是 | 用户ID |

**使用场景**: 
- EXERCISE_TRACKING 场景中的 Function Calling 工具
- 用户咨询自己的运动记录时提供上下文信息

#### 1.4.2 获取所有运动类型列表

**接口描述**: 获取系统支持的所有运动类型列表。

**Feign 接口定义**:
```java
@GetMapping("/internal/ai-clock/all-exercise-list")
public ExerciseListVo getAllExerciseList();
```

**业务封装**:
```java
ExerciseListVo getAllExerciseList();
```

**请求参数**: 无

**响应数据结构**:
```java
@Data
public class ExerciseListVo {
    /** 响应码 */
    private Integer code;
    
    /** 响应消息 */
    private String message;
    
    /** 响应数据 */
    private List<ExerciseType> data;
    
    @Data
    public static class ExerciseType {
        /** 运动ID */
        private Integer id;
        
        /** 运动名称 */
        private String name;
        
        // 其他运动类型相关字段
    }
}
```

**使用场景**: 
- EXERCISE_TRACKING 场景中的 Function Calling 工具
- 获取系统支持的运动类型，用于生成运动记录卡片
- 如果用户的运动类型不存在，使用 taskId=99999 处理

#### 1.4.3 记录用户运动信息

**接口描述**: 记录用户的运动信息。

**Feign 接口定义**:
```java
@PostMapping("/internal/ai-clock/exercise-clock")
public ExerciseClockVo exerciseClock(@RequestBody ExerciseClockQo qo);
```

**业务封装**:
```java
ExerciseClockVo recordExercise(String userId, String recordDate, Integer taskId,
                               Integer duration, Integer distance, Integer avgHeartRate, String intensityLevel);
```

**请求数据结构**:
```java
@Data
public class ExerciseClockQo {
    /** 用户ID */
    private String userId;
    
    /** 记录日期 */
    private String recordDate;
    
    /** 运动任务ID */
    private Integer taskId;
    
    /** 运动时长(秒) */
    private Integer duration;
    
    /** 运动距离(米)，可选，当trackRoute=Y时需要 */
    private Integer distance;
    
    /** 平均心率(次/分钟)，可选 */
    private Integer avgHeartRate;
    
    /** 运动强度，可选，light/moderate/high */
    private String intensityLevel;
}
```

**使用场景**: 
- EXERCISE_TRACKING 场景中的 Function Calling 工具
- 生成运动记录卡片参数

### 1.5 日常任务详情接口

#### 1.5.1 获取用户日常任务详情

**接口描述**: 获取用户指定时间范围内的日常任务详情，包括饮水、睡眠、运动等各类任务的完成情况。

**Feign 接口定义**:
```java
@GetMapping("/internal/ai-clock/daily-detail")
public DailyDetailVo getDailyDetail(
        @RequestParam("startDate") String startDate,
        @RequestParam("endDate") String endDate,
        @RequestParam("userId") String userId);
```

**业务封装**:
```java
DailyDetailVo getDailyDetail(String startDate, String endDate, String userId);
```

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| startDate | String | 是 | 开始日期，格式：yyyy-MM-dd |
| endDate | String | 是 | 结束日期，格式：yyyy-MM-dd |
| userId | String | 是 | 用户ID |

**响应数据结构**:
```java
@Data
public class DailyDetailVo {
    /** 响应码 */
    private Integer code;
    
    /** 响应消息 */
    private String message;
    
    /** 响应数据，按日期分组的任务详情列表 */
    private Map<String, List<TaskCategory>> data;
    
    @Data
    public static class TaskCategory {
        /** 图标URL */
        private String icon;
        
        /** 类别标题 */
        private String title;
        
        /** 类别类型 */
        private String type;
        
        /** 是否是阶段目标，影响amazing day */
        private Boolean phaseGoal;
        
        /** 具体目标列表 */
        private List<TaskGoal> goals;
    }
    
    @Data
    public static class TaskGoal {
        /** 目标代码 */
        private String goalCode;
        
        // 其他目标相关字段
    }
}
```

**使用场景**: 
- 多个场景中的 Function Calling 工具
- HYDRATION_TRACKING: 获取饮水记录信息
- SLEEP_TRACKING: 获取睡眠记录信息
- EXERCISE_TRACKING: 获取运动记录信息
- HEALTH_ANALYTICS: 获取健康数据用于分析

---

## 2. 健康代理服务接口

### 基础信息
- **服务名称**: Health Agent Service
- **Feign Client**: `HealthAgentFeignClient`
- **服务地址**: `${evyd.micro-service.hosts.health-agent:http://smartchat-health-agent}`

### 2.1 医院指南接口

**接口描述**: 提供医院指南信息查询服务。

**Feign 接口定义**:
```java
@PostMapping("/hospital/guide")
public HospitalGuideVo hospital_guide(@RequestBody HospitalGuideQo qo);
```

**请求数据结构**:
```java
@Data
public class HospitalGuideQo {
    // 医院指南查询相关字段
}
```

**响应数据结构**:
```java
@Data
public class HospitalGuideVo {
    // 医院指南响应相关字段
}
```

**使用场景**: 
- SMART_TRIAGE 场景
- 智能分诊服务

### 2.2 医院提醒接口

**接口描述**: 提供医院提醒信息服务。

**Feign 接口定义**:
```java
@PostMapping("/hospital/reminder")
public HospitalReminderVo hospital_reminder(@RequestBody HospitalReminderQo qo);
```

**请求数据结构**:
```java
@Data
public class HospitalReminderQo {
    // 医院提醒相关字段
}
```

**响应数据结构**:
```java
@Data
public class HospitalReminderVo {
    // 医院提醒响应相关字段
}
```

**使用场景**: 
- 提醒消息服务
- 医院相关提醒推送

### 2.3 面部扫描结果接口

**接口描述**: 处理面部扫描结果数据。

**Feign 接口定义**:
```java
@PostMapping("/face/scan/result")
public InternalFaceScanResultVo face_scan_result(@RequestBody InternalFaceScanResultQo qo);
```

**请求数据结构**:
```java
@Data
public class InternalFaceScanResultQo {
    // 面部扫描结果相关字段
}
```

**响应数据结构**:
```java
@Data
public class InternalFaceScanResultVo {
    // 面部扫描结果响应相关字段
}
```

**使用场景**: 
- 面部扫描服务
- 健康检测相关功能

### 2.4 健康咨询接口

**接口描述**: 提供健康咨询服务。

**Feign Client**: `HealthConsultFeignClient`

**Feign 接口定义**:
```java
@PostMapping("/health/consult")
public HealthConsultVo healthConsult(@RequestBody HealthConsultQo qo);
```

**请求数据结构**:
```java
@Data
public class HealthConsultQo {
    String user_id;
    
    // Channel URL
    String conversation_id;
    
    // User input
    String inputs;
}
```

**响应数据结构**:
```java
@Data
public class HealthConsultVo {
    // 健康咨询响应相关字段
}
```

**使用场景**: 
- HEALTH_CONSULTATION 场景
- 健康咨询服务

---

## 3. HSD 服务接口

### 基础信息
- **服务名称**: HSD Service
- **Feign Client**: `HsdFeignClient`
- **服务地址**: `${evyd.micro-service.hosts.hsd:http://evyd-health-service-hsd-ehospital}`
- **业务封装**: `HsdService`

### 3.1 预约检查接口

**接口描述**: 检查预约状态。

**Feign 接口定义**:
```java
@GetMapping("/internal/ai-process/check-appointment")
CheckAppointmentResultVo checkAppointment(@SpringQueryMap CheckAppointmentQo qo);
```

**请求数据结构**:
```java
@Data
public class CheckAppointmentQo {
    private String userId;
    private List<Integer> slotIdList;
}
```

**响应数据结构**:
```java
@Data
public class CheckAppointmentResultVo {
    // 预约检查结果相关字段
}
```

**使用场景**: 
- APPOINTMENT_MANAGEMENT 场景
- 预约管理功能

### 3.2 时间段查询接口

**接口描述**: 查询可用的时间段。

**Feign 接口定义**:
```java
@GetMapping("/internal/ai-process/slots")
SlotResultVo getSlots(@SpringQueryMap SlotQo qo);
```

**请求数据结构**:
```java
@Data
public class SlotQo {
    // 时间段查询相关字段
}
```

**响应数据结构**:
```java
@Data
public class SlotResultVo {
    // 时间段查询结果相关字段
}
```

**使用场景**: 
- APPOINTMENT_MANAGEMENT 场景
- 预约时间段查询

---

## 4. 接口配置说明

### 4.1 Feign 客户端配置

**配置文件位置**: `application-*.yaml`

```yaml
### Feign 相关 ###
feign:
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 30000
        loggerLevel: basic
  compression:
    request:
      enabled: true
    response:
      enabled: true
  httpclient:
    enabled: false
  okhttp:
    enabled: true

# feign 客户端配置
evyd:
  micro-service:
    hosts:
      # 默认配置 -> 可单独指定 feignName
      routines: http://evyd-health-manage-routines
      health-agent: http://smartchat-health-agent
      hsd: http://evyd-health-service-hsd-ehospital
```

### 4.2 服务地址配置

| 服务名称 | 配置键 | 默认地址 |
|----------|--------|----------|
| AI Clock Service | evyd.micro-service.hosts.routines | http://evyd-health-manage-routines |
| Health Agent Service | evyd.micro-service.hosts.health-agent | http://smartchat-health-agent |
| HSD Service | evyd.micro-service.hosts.hsd | http://evyd-health-service-hsd-ehospital |

### 4.3 Feign 客户端启用

**主应用类配置**:
```java
@EnableFeignClients(basePackages={"com.evydtech.*"})
@EnableFeignCommonDecoder
public class ChatBotApplication {
    // 应用启动类
}
```

---

## 5. 错误处理机制

### 5.1 全局异常处理

**异常处理器**: `GlobalExceptionHandler`

```java
/**
 * OpenFeign 异常
 */
@ExceptionHandler(FeignException.class)
public Result<?> runtimeExceptionHandler(FeignException e) {
    return responseData(ErrorCodeEnum.OPEN_FEIGN_EXCEPTION.getCode(), e);
}
```

### 5.2 错误码定义

```java
public enum ErrorCodeEnum {
    /**
     * OpenFeign 异常
     */
    OPEN_FEIGN_EXCEPTION(50015, "Feign调用异常");
}
```

### 5.3 请求拦截器

**拦截器**: `FeignRequestInterceptor`

```java
@Component
public class FeignRequestInterceptor implements RequestInterceptor {
    @Override
    public void apply(RequestTemplate template) {
        // 添加通用请求头
        // 记录请求日志
        log.info("FEIGN_REQUEST_INFO_COMMON|{}", template);
    }
}
```

---

## 6. 使用示例

### 6.1 营养分析场景示例

```java
@Service
public class NutritionAnalysisServiceImpl {
    
    @Autowired
    private AiClockService aiClockService;
    
    public void processNutritionAnalysis(String userId) {
        // 1. 获取用餐时间段
        MealTimingVo mealTiming = aiClockService.getMealTiming();
        
        // 2. 获取用户营养日报
        String startDate = "2025-01-27";
        String endDate = "2025-01-27";
        NutritionDailyVo nutritionDaily = aiClockService.getNutritionDaily(
            startDate, endDate, userId);
        
        // 3. 记录营养信息
        List<NutritionClockQo.FoodItem> foods = Arrays.asList(
            // 构建食物列表
        );
        NutritionClockVo result = aiClockService.recordNutrition(
            userId, "1", "2025-01-27", foods);
    }
}
```

### 6.2 健康咨询场景示例

```java
@Service
public class HealthConsultationServiceImpl {
    
    @Autowired
    private HealthConsultFeignClient healthConsultFeignClient;
    
    public HealthConsultVo processHealthConsultation(String userId, String input) {
        HealthConsultQo qo = new HealthConsultQo();
        qo.setUser_id(userId);
        qo.setInputs(input);
        qo.setConversation_id("conversation_" + userId);
        
        return healthConsultFeignClient.healthConsult(qo);
    }
}
```

---

## 7. 维护说明

### 7.1 接口版本管理

- 所有接口遵循 RESTful 设计规范
- 接口版本通过 URL 路径进行管理
- 向后兼容性原则，新版本不影响旧版本

### 7.2 监控和日志

- 所有 Feign 调用都有详细的请求日志
- 通过 `FeignRequestInterceptor` 统一记录请求信息
- 异常情况通过全局异常处理器统一处理

### 7.3 性能优化

- 启用请求和响应压缩
- 使用 OkHttp 客户端提升性能
- 合理设置连接超时和读取超时时间

---

## 8. 联系方式

如有问题或建议，请联系：
- **开发团队**: 聊天机器人开发团队
- **文档维护**: 技术文档组
- **更新频率**: 随系统版本更新

---

*本文档最后更新时间: 2025-01-27*