# Health Advisor 场景工具增强说明

## 概述

本文档说明了为Health Advisor场景新增的患者数据查询工具，包括架构设计和使用方式。

## 新增工具

### 1. 患者健康指标查询工具
- **工具名称**: `query_patient_health_indicators_tool`
- **功能**: 查询患者最新的健康指标数据（血压、血糖、体重等）
- **适用场景**: 用户询问当前健康指标、生命体征等

### 2. 患者医疗记录查询工具
- **工具名称**: `query_patient_medical_records_tool`
- **功能**: 查询患者医疗记录（诊断、用药、治疗历史）
- **特色**: 内嵌SQL生成代理，能够根据用户请求智能生成SQL查询
- **适用场景**: 用户询问病史、用药记录、诊断信息等

## 架构设计亮点

### 1. 嵌套Agent架构
- 在工具内部嵌套了专用的`SQLGenerationAgent`
- 该代理专门负责根据用户查询生成医疗数据SQL语句
- 采用轻量级、无状态设计，避免与主场景ReactAgent冲突

### 2. 模块化设计
- 创建了`service/scenes/embedded_agents/`包，用于管理场景内嵌套的专用代理
- 便于其他场景也可以复用或扩展类似的agent功能
- 保持了与现有架构的兼容性

### 3. 智能SQL生成
- SQL代理使用英文医疗数据分析提示词
- 能够理解医疗表结构并生成合适的查询语句
- 包含数据表字段验证，确保生成的SQL语法正确

## 系统提示词优化

### 1. 工具选择指导
- 新增了详细的工具选择指南
- 明确了每个工具的适用场景和触发条件
- 提供了中英文示例查询

### 2. 数据解释说明
- 添加了新工具返回数据的解释指导
- 说明了如何识别和处理不同格式的医疗数据
- 强调了医疗数据的交叉引用和综合分析

## 使用示例

### 健康指标查询
```
用户: "我的血压是多少？"
系统: 调用 query_patient_health_indicators_tool("blood pressure", "blood_pressure")
返回: 患者最新血压数据，包括数值、单位、记录时间等
```

### 医疗记录查询
```
用户: "我最近有什么诊断记录？"
系统: 调用 query_patient_medical_records_tool("recent diagnoses", "diagnosis")
流程: 
1. 内嵌SQL代理分析查询意图
2. 生成相应的SQL语句
3. 执行查询并返回诊断记录
4. 格式化展示给用户
```

## 技术特性

### 1. 错误处理
- 完善的异常处理机制
- 优雅的降级策略
- 详细的日志记录

### 2. 数据安全
- 固定患者ID（生产环境需要从上下文获取）
- SQL注入防护
- 数据访问权限控制

### 3. 性能优化
- 服务实例单例模式
- 合理的数据量限制
- 高效的数据格式化

## 扩展性

### 1. 新Agent支持
- `service/scenes/embedded_agents/`包可以容纳更多专用代理
- 统一的代理接口设计
- 便于其他场景集成类似功能

### 2. 新工具添加
- 工具模块化设计，便于添加新的医疗数据查询工具
- 统一的数据格式和错误处理
- 可扩展的提示词指导系统

## 注意事项

1. **患者ID管理**: 当前使用固定ID，生产环境需要从场景上下文动态获取
2. **数据权限**: 需要确保用户只能访问自己的医疗数据
3. **SQL安全**: SQL代理已包含基础安全检查，但建议在生产环境中添加更多验证
4. **性能监控**: 建议对嵌套代理的性能进行监控，避免响应时间过长

## 总结

通过引入嵌套Agent架构和新的患者数据查询工具，Health Advisor场景现在具备了：

1. **更强的数据获取能力**: 可以查询多种类型的患者数据
2. **更智能的查询方式**: 通过AI代理生成复杂的SQL查询
3. **更好的用户体验**: 能够根据用户需求精确获取相关数据
4. **更好的架构设计**: 模块化、可扩展的架构为未来功能扩展打下基础

这一设计不仅满足了当前的需求，也为其他场景集成类似功能提供了可复用的架构模式。