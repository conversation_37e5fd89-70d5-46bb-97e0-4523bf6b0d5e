# OCR业务逻辑与接口文档

## 概述

本文档详细描述了聊天机器人平台中OCR（光学字符识别）功能的完整业务逻辑、接口规范和技术实现细节，为Python服务迁移提供完整的技术参考。

## 目录

1. [OCR业务流程](#ocr业务流程)
2. [核心业务逻辑](#核心业务逻辑)
3. [文件服务接口](#文件服务接口)
4. [OCR服务实现](#ocr服务实现)
5. [数据结构定义](#数据结构定义)
6. [配置与常量](#配置与常量)
7. [错误处理](#错误处理)
8. [Python迁移指南](#python迁移指南)

---

## OCR业务流程

### 整体流程图

```
用户发送图片消息 → 消息数据解析 → 文件ID提取 → OCR服务调用 → AI图片分析 → 结果处理 → 返回分析文本
```

### 详细业务流程

1. **消息接收与验证**
   - 接收SendBird回调消息
   - 验证消息类型为图片（bizMsgType=2）
   - 提取消息数据结构

2. **文件信息提取**
   - 解析content数组获取文件信息
   - 提取sign字段作为文件ID
   - 构建文件路径信息

3. **OCR处理**
   - 调用OCR服务进行图片分析
   - 优先使用公网URL方式
   - 失败时回退到Base64方式

4. **结果封装**
   - 构建OcrProcessResult对象
   - 包含处理后的文本和扩展数据
   - 返回给业务流程继续处理

---

## 核心业务逻辑

### ChatBusinessServiceAppImpl.processOcrIfNeeded()

**方法签名：**
```java
private OcrProcessResult processOcrIfNeeded(SendBirdCallbackMessageQo qo)
```

**核心实现逻辑：**

#### 1. 消息数据检查
```java
// 检查是否有数据
String messageData = qo.getMessage().getData();
if (StrUtil.isBlank(messageData)) {
    return null;
}

// 解析数据结构
JSONObject dataJson = JSON.parseObject(messageData);
if (dataJson == null) {
    return null;
}
```

#### 2. 图片消息验证
```java
// 检查bizMsgType是否为2（图片消息）
Integer bizMsgType = dataJson.getInteger("bizMsgType");
if (bizMsgType == null || !bizMsgType.equals(2)) {
    return null;
}
```

#### 3. 文件ID提取
```java
// 获取content数组
List<Map<String, Object>> contentList = (List<Map<String, Object>>) dataJson.get("content");
if (CollectionUtil.isEmpty(contentList)) {
    return null;
}

// 获取第一个content项的sign（文件ID）
Map<String, Object> firstContent = contentList.get(0);
String sign = (String) firstContent.get("sign");
```

#### 4. 文件ID验证与默认处理
```java
if (StrUtil.isBlank(sign)) {
    // sign为空时返回默认提示语
    String defaultText = "This input has no effect, don't do anything, just reply to me: No file ID passed correctly";
    return new OcrProcessResult(defaultText, null);
}
```

#### 5. OCR服务调用
```java
// 调用图片分析服务进行内容识别
OcrQo ocrQo = new OcrQo();
ocrQo.setFileId(sign);

OcrVo ocrResult = ocrService.recognizeText(ocrQo);
```

#### 6. 扩展数据构建
```java
// 构建extendData
Map<String, Object> extendDataMap = new HashMap<>();
extendDataMap.put("imageAnalysisPath", firstContent.get("path"));
extendDataMap.put("imageAnalysisFileId", sign);
String extendDataJson = JSON.toJSONString(extendDataMap);
```

#### 7. 结果处理
```java
if (ocrResult.getSuccess() && StrUtil.isNotBlank(ocrResult.getText())) {
    log.info("图片内容分析成功，文件ID: {}, 分析结果: {}", sign, ocrResult.getText());
    return new OcrProcessResult(ocrResult.getText(), extendDataJson);
} else {
    log.warn("图片内容分析失败，文件ID: {}, 错误信息: {}", sign, ocrResult.getErrorMessage());
    String failureText = "Image content analysis failed, please try again or provide text input";
    return new OcrProcessResult(failureText, extendDataJson);
}
```

### OcrProcessResult内部类

```java
private static class OcrProcessResult {
    private String processedText;  // 处理后的文本
    private String extendData;     // 扩展数据JSON字符串
    
    public OcrProcessResult(String processedText, String extendData) {
        this.processedText = processedText;
        this.extendData = extendData;
    }
    
    // getter方法
    public String getProcessedText() { return processedText; }
    public String getExtendData() { return extendData; }
}
```

---

## 文件服务接口

### 接口概览

| 接口名称 | HTTP方法 | 路径 | 功能描述 |
|---------|---------|------|---------|
| 获取上传信息 | POST | `/file/info/upload` | 获取文件上传签名信息 |
| 获取预览信息 | POST | `/file/info/preview` | 获取文件预览URL |
| 文件下载 | GET | `/file/download` | 下载文件信息 |
| 文件上传 | POST | `/file/action/upload` | 上传文件 |

### 1. 获取文件预览信息接口

**接口路径：** `POST /file/info/preview`

**功能说明：** 根据文件ID获取文件的公网访问URL，OCR服务使用此接口获取图片URL

**请求参数：**
```json
{
    "bizUserId": "用户业务ID",
    "ids": ["文件ID1", "文件ID2"]
}
```

**响应格式：**
```json
{
    "code": 0,
    "message": "success",
    "data": [
        {
            "id": "文件ID",
            "path": "https://example.com/file/preview/url"
        }
    ]
}
```

**Java实现：**
```java
@PostMapping("/file/info/preview")
public Result<?> getPreviewInfo(@RequestBody FileQO qo) {
    return Result.success(fileService.getPreviewInfo(qo));
}
```

### 2. 文件下载接口

**接口路径：** `GET /file/download?fileId={fileId}`

**功能说明：** 获取文件下载信息和元数据

**请求参数：**
- `fileId`: 文件ID（必填）

**响应格式：**
```json
{
    "code": 0,
    "message": "success", 
    "data": {
        "fileName": "文件名",
        "contentType": "文件类型",
        "size": 文件大小,
        "url": "下载URL"
    }
}
```

### 3. 获取上传信息接口

**接口路径：** `POST /file/info/upload`

**功能说明：** 获取文件上传的签名信息

**请求参数：**
```json
{
    "bizUserId": "用户业务ID",
    "fileName": "文件名",
    "contentType": "文件类型",
    "length": 文件大小
}
```

**响应格式：**
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "id": "文件ID",
        "path": "上传路径"
    }
}
```

### 4. 文件上传接口

**接口路径：** `POST /file/action/upload`

**功能说明：** 直接上传文件

**请求参数：** FormData格式
- `file`: 文件对象
- `bizUserId`: 用户业务ID

**响应格式：**
```json
{
    "code": 0,
    "message": "success",
    "data": null
}
```

---

## OCR服务实现

### OcrService接口

```java
@Service
public interface OcrService {
    /**
     * 图片内容分析识别（包括文本、物体、场景等）
     *
     * @param qo OcrQo
     * @return OcrVo
     */
    OcrVo recognizeText(OcrQo qo);
}
```

### OcrServiceImpl核心实现

#### 主要处理流程

1. **双重处理策略**
   - 优先使用公网URL方式（不下载图片）
   - 失败时回退到Base64方式

2. **图片URL获取**
   ```java
   private String getImageUrl(OcrQo qo) {
       // 1. 直接使用imagePath
       if (StrUtil.isNotBlank(qo.getImagePath())) {
           return qo.getImagePath();
       }
       
       // 2. 通过FileService获取文件路径
       if (StrUtil.isNotBlank(qo.getFileId())) {
           FileQO fileQO = new FileQO();
           fileQO.setIds(Collections.singletonList(qo.getFileId()));
           
           List<FileVO> fileVOList = fileService.getPreviewInfo(fileQO);
           if (!CollectionUtils.isEmpty(fileVOList)) {
               return fileVOList.get(0).getPath();
           }
       }
       return null;
   }
   ```

3. **AI模型调用**
   - 使用GPT-4.1-mini多模态模型
   - 支持URL和Base64两种方式
   - 专门的健康领域优化提示词

#### AI配置参数

```java
// 模型配置
aiChatGptQo.setModel("gpt-4.1-mini");           // 多模态模型
aiChatGptQo.setMaxTokens(200);                  // 最大token数
aiChatGptQo.setTemperature(0.1);                // 降低创造性，提高准确性
```

---

## 数据结构定义

### OcrQo - OCR请求对象

```java
@Data
public class OcrQo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 文件ID
     */
    private String fileId;

    /**
     * 图片URL路径
     */
    private String imagePath;

    /**
     * 图片数据（Base64编码或字节数组）
     */
    private byte[] imageData;
}
```

### OcrVo - OCR响应对象

```java
@Data
public class OcrVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 分析是否成功
     */
    private Boolean success;

    /**
     * 分析出的图片内容描述
     */
    private String text;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 置信度（可选）
     */
    private Double confidence;
}
```

### FileQO - 文件请求对象

```java
@Data
public class FileQO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * BizUserId
     */
    @NotEmpty
    private String bizUserId;

    /**
     * FileName
     */
    @NotEmpty
    private String fileName;

    /**
     * Length
     */
    @NotEmpty
    private Long length;

    /**
     * ContentType
     */
    @NotEmpty
    private String contentType;

    /**
     * Ids - 文件ID列表
     */
    private List<String> ids;

    private MultipartFile file;
}
```

### FileVO - 文件响应对象

```java
@Data
public class FileVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * Id - 文件ID
     */
    private String id;

    /**
     * Path - 文件路径/URL
     */
    private String path;
}
```

### SendBirdCallbackMessageQo - 消息回调对象

**消息数据结构示例：**
```json
{
    "message": {
        "data": "{\"bizMsgType\":2,\"content\":[{\"sign\":\"file123\",\"path\":\"/uploads/image.jpg\"}]}"
    }
}
```

**关键字段说明：**
- `bizMsgType`: 消息类型，2表示图片消息
- `content`: 内容数组，包含文件信息
- `sign`: 文件ID，用于后续文件操作
- `path`: 文件路径信息

---

## 配置与常量

### AI平台配置

```yaml
chatbot:
  ai-platform:
    openai:
      api-key: ${OPEN_AI_API_KEY}
      chat-model-name: ${OPEN_AI_MODEL:gpt-4o}
      completion-model-name: ${OPEN_AI_MODEL_COMPLETION:text-davinci-003}
      api-timeout: ${OPEN_AI_TIMEOUT:30}
      max-token: ${OPEN_AI_MAX_TOKEN:12000}
      trace-log-length: ${OPEN_AI_TRACE_LOG_LENGTH:1500}
      organization: ${OPEN_AI_ORGANIZATION}
```

### OSS文件服务配置

```yaml
chatbot:
  oss:
    enabled: true
    app-id: hermes
    app-secret: hermes
    business: hermes
```

### OCR业务常量

#### 消息类型常量
```java
public static final Integer IMAGE_MESSAGE_TYPE = 2;  // 图片消息类型
```

#### 默认提示语常量
```java
// 文件ID为空时的默认提示
public static final String NO_FILE_ID_TEXT =
    "This input has no effect, don't do anything, just reply to me: No file ID passed correctly";

// OCR失败时的提示
public static final String OCR_FAILED_TEXT =
    "Image content analysis failed, please try again or provide text input";

// OCR异常时的提示
public static final String OCR_ERROR_TEXT =
    "Image content analysis processing error, please try again or provide text input";
```

#### AI模型配置常量
```java
public static final String OCR_MODEL = "gpt-4.1-mini";     // OCR使用的AI模型
public static final Integer OCR_MAX_TOKENS = 200;          // 最大token数
public static final Double OCR_TEMPERATURE = 0.1;          // 温度参数
public static final Double DEFAULT_CONFIDENCE = 0.95;      // 默认置信度
```

### 健康领域优化提示词

OCR服务使用专门针对健康平台优化的系统提示词，重点关注以下领域：

#### 营养与食物分析（最高优先级）
- 识别所有食物、饮料和餐食，包含具体名称和数量
- 估算份量和服务大小，包含测量单位（如"1杯米饭"、"2片面包"、"200ml水"）
- 注明烹饪方法（烤、炸、蒸、生、烘焙）
- 描述用餐环境和时间指示器（早餐盘、午餐环境、晚餐桌、零食时间）
- 按营养组分类（蛋白质、碳水化合物、蔬菜、水果、乳制品、脂肪）
- 包含消费指示器："已吃"、"已消费"、"已完成"、"吃了一半"、"剩余"
- 提及食物准备阶段："已烹饪"、"已准备"、"准备食用"、"正在消费"

#### 运动与体力活动
- 识别运动器材、健身房环境、体育设施、锻炼环境
- 描述身体动作、锻炼类型、运动强度水平
- 注明持续时间指示器（计时器、锻炼计划、健身应用屏幕）
- 提及健身追踪设备、智能手表、心率监测器
- 包含活动完成提示："完成锻炼"、"运动后"、"训练期间"
- 描述运动环境："晨跑"、"健身房训练"、"家庭锻炼"、"户外活动"

#### 水分与饮料追踪
- 识别所有类型的饮料和饮用容器，包含具体容量
- 估算液体数量和容器大小（250ml玻璃杯、500ml瓶子、1L水壶）
- 注明水分相关物品（水瓶、玻璃杯、杯子、吸管）
- 包含消费状态："空瓶"、"半满玻璃杯"、"喝完"、"重新装满"
- 提及水分环境："运动后饮品"、"晨水"、"日常水分补充"

#### 睡眠与休息环境
- 描述睡眠相关环境（卧室、床、枕头、睡眠环境）
- 注明时间指示器，暗示睡眠模式（时钟、日出/日落、卧室照明）
- 识别睡眠追踪设备、应用或睡眠质量指示器
- 包含睡眠环境："就寝准备"、"晨起"、"睡眠追踪屏幕"
- 提及睡眠质量提示："安静环境"、"睡眠干扰"、"舒适环境"

---

## 错误处理

### 异常处理策略

#### 1. 消息数据异常
```java
try {
    // OCR处理逻辑
} catch (Exception e) {
    log.error("图片内容分析处理异常", e);
    String errorText = "Image content analysis processing error, please try again or provide text input";
    return new OcrProcessResult(errorText, null);
}
```

#### 2. 文件服务异常
- **文件不存在**：返回404错误
- **文件访问权限**：返回403错误
- **文件服务超时**：返回500错误
- **网络连接异常**：自动重试机制

#### 3. AI服务异常
- **API调用失败**：回退到Base64方式
- **模型响应超时**：返回默认错误提示
- **Token限制超出**：调整maxTokens参数
- **API配额不足**：记录错误日志并返回友好提示

### 错误码定义

| 错误码 | 错误信息 | 处理方式 |
|-------|---------|---------|
| `BIZ_GET_FILE_INFO_PARAMS_ERROR` | 文件信息参数错误 | 参数验证失败 |
| `BIZ_GET_UPLOAD_FAILED_COMMON_JAR` | 文件上传失败 | OSS服务异常 |
| `BIZ_GET_PREVIEW_FAILED_COMMON_JAR` | 文件预览失败 | 文件访问异常 |

### 日志记录规范

#### 成功日志
```java
log.info("图片内容分析成功，文件ID: {}, 分析结果: {}", sign, ocrResult.getText());
log.info("使用公网URL方式进行图片分析: {}", imageUrl);
log.info("FILE_DOWNLOAD_SUCCESS|fileId:{}", fileId);
```

#### 警告日志
```java
log.warn("图片内容分析失败，文件ID: {}, 错误信息: {}", sign, ocrResult.getErrorMessage());
log.warn("获取图片URL失败", e);
```

#### 错误日志
```java
log.error("图片内容分析异常", e);
log.error("调用AI进行图片内容分析失败", e);
log.error("FILE_DOWNLOAD_FAILED|fileId:{}|error:{}", fileId, e.getMessage(), e);
```

---

## Python迁移指南

### 1. 核心业务逻辑迁移

#### Python实现框架
```python
class OCRProcessor:
    def __init__(self, file_service, ai_service):
        self.file_service = file_service
        self.ai_service = ai_service

    def process_ocr_if_needed(self, message_data):
        """
        处理OCR请求的主要方法
        对应Java中的processOcrIfNeeded方法
        """
        try:
            # 1. 消息数据检查
            if not message_data or not message_data.get('message', {}).get('data'):
                return None

            # 2. 解析数据结构
            data_json = json.loads(message_data['message']['data'])
            if not data_json:
                return None

            # 3. 验证图片消息类型
            biz_msg_type = data_json.get('bizMsgType')
            if biz_msg_type != 2:  # 2表示图片消息
                return None

            # 4. 提取文件信息
            content_list = data_json.get('content', [])
            if not content_list:
                return None

            first_content = content_list[0]
            file_id = first_content.get('sign')

            # 5. 文件ID验证
            if not file_id:
                return OCRProcessResult(
                    processed_text="This input has no effect, don't do anything, just reply to me: No file ID passed correctly",
                    extend_data=None
                )

            # 6. 调用OCR服务
            ocr_result = self.recognize_text(file_id)

            # 7. 构建扩展数据
            extend_data = {
                "imageAnalysisPath": first_content.get('path'),
                "imageAnalysisFileId": file_id
            }

            # 8. 处理结果
            if ocr_result.success and ocr_result.text:
                logger.info(f"图片内容分析成功，文件ID: {file_id}, 分析结果: {ocr_result.text}")
                return OCRProcessResult(
                    processed_text=ocr_result.text,
                    extend_data=json.dumps(extend_data)
                )
            else:
                logger.warning(f"图片内容分析失败，文件ID: {file_id}, 错误信息: {ocr_result.error_message}")
                return OCRProcessResult(
                    processed_text="Image content analysis failed, please try again or provide text input",
                    extend_data=json.dumps(extend_data)
                )

        except Exception as e:
            logger.error(f"图片内容分析处理异常: {str(e)}")
            return OCRProcessResult(
                processed_text="Image content analysis processing error, please try again or provide text input",
                extend_data=None
            )
```

#### 数据类定义
```python
from dataclasses import dataclass
from typing import Optional, List

@dataclass
class OCRRequest:
    file_id: Optional[str] = None
    image_path: Optional[str] = None
    image_data: Optional[bytes] = None

@dataclass
class OCRResponse:
    success: bool
    text: Optional[str] = None
    error_message: Optional[str] = None
    confidence: Optional[float] = None

@dataclass
class OCRProcessResult:
    processed_text: str
    extend_data: Optional[str] = None

@dataclass
class FileRequest:
    biz_user_id: str
    file_name: Optional[str] = None
    content_type: Optional[str] = None
    length: Optional[int] = None
    ids: Optional[List[str]] = None

@dataclass
class FileResponse:
    id: str
    path: str
```

### 2. 文件服务接口调用

#### HTTP客户端配置
```python
import requests
from typing import List, Optional

class FileServiceClient:
    def __init__(self, base_url: str, timeout: int = 30):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()

    def get_preview_info(self, file_ids: List[str], biz_user_id: str) -> List[FileResponse]:
        """
        获取文件预览信息
        对应Java接口: POST /file/info/preview
        """
        url = f"{self.base_url}/file/info/preview"
        payload = {
            "bizUserId": biz_user_id,
            "ids": file_ids
        }

        try:
            response = self.session.post(url, json=payload, timeout=self.timeout)
            response.raise_for_status()

            result = response.json()
            if result.get('code') == 0:
                return [FileResponse(id=item['id'], path=item['path'])
                       for item in result.get('data', [])]
            else:
                raise Exception(f"API错误: {result.get('message')}")

        except requests.RequestException as e:
            logger.error(f"获取文件预览信息失败: {str(e)}")
            raise

    def download_file(self, file_id: str) -> dict:
        """
        下载文件信息
        对应Java接口: GET /file/download
        """
        url = f"{self.base_url}/file/download"
        params = {"fileId": file_id}

        try:
            response = self.session.get(url, params=params, timeout=self.timeout)
            response.raise_for_status()

            result = response.json()
            if result.get('code') == 0:
                return result.get('data', {})
            else:
                raise Exception(f"API错误: {result.get('message')}")

        except requests.RequestException as e:
            logger.error(f"下载文件失败: {str(e)}")
            raise
```

### 3. AI服务集成

#### OpenAI客户端配置
```python
import openai
from typing import List, Dict

class AIServiceClient:
    def __init__(self, api_key: str, model: str = "gpt-4.1-mini"):
        self.client = openai.OpenAI(api_key=api_key)
        self.model = model

    def analyze_image_by_url(self, image_url: str) -> str:
        """
        通过URL分析图片
        对应Java中的callAiForOcrByUrl方法
        """
        try:
            messages = [
                {
                    "role": "system",
                    "content": self._build_system_prompt()
                },
                {
                    "role": "user",
                    "content": f"Analyze this image and provide a comprehensive description following the guidelines: {image_url}"
                }
            ]

            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=200,
                temperature=0.1
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            logger.error(f"AI图片分析失败: {str(e)}")
            raise

    def _build_system_prompt(self) -> str:
        """
        构建系统提示词
        对应Java中的buildOptimizedSystemPrompt方法
        """
        return """You are a specialized health-focused image analysis assistant for a comprehensive health platform.
Your task is to analyze images and provide structured descriptions that help with health-related intent recognition and scenario routing.

## Analysis Priority Framework:

**NUTRITION & FOOD ANALYSIS (Highest Priority)**
- Identify ALL food items, beverages, and meals with specific names and quantities
- Estimate portions and serving sizes with measurement units (e.g., '1 cup rice', '2 slices bread', '200ml water')
- Note cooking methods (grilled, fried, steamed, raw, baked)
- Describe meal context and timing indicators (breakfast plate, lunch setting, dinner table, snack time)
- Categorize by nutritional groups (proteins, carbohydrates, vegetables, fruits, dairy, fats)
- Include consumption indicators: 'eaten', 'consumed', 'finished', 'half-eaten', 'leftover'
- Mention food preparation stages: 'cooked', 'prepared', 'ready to eat', 'being consumed'

**EXERCISE & PHYSICAL ACTIVITY**
- Identify exercise equipment, gym settings, sports facilities, workout environments
- Describe physical movements, workout types, exercise intensity levels
- Note duration indicators (timers, workout schedules, fitness app screens)
- Mention fitness tracking devices, smartwatches, heart rate monitors
- Include activity completion cues: 'finished workout', 'post-exercise', 'during training'
- Describe exercise context: 'morning run', 'gym session', 'home workout', 'outdoor activity'

**HYDRATION & BEVERAGE TRACKING**
- Identify all types of beverages and drinking containers with specific volumes
- Estimate liquid quantities and container sizes (250ml glass, 500ml bottle, 1L jug)
- Note hydration-related items (water bottles, glasses, cups, straws)
- Include consumption status: 'empty bottle', 'half-full glass', 'finished drinking', 'refilled'
- Mention hydration context: 'post-workout drink', 'morning water', 'daily hydration'

**SLEEP & REST ENVIRONMENT**
- Describe sleep-related settings (bedrooms, beds, pillows, sleep environment)
- Note time indicators suggesting sleep patterns (clocks, sunrise/sunset, bedroom lighting)
- Identify sleep tracking devices, apps, or sleep quality indicators
- Include sleep context: 'bedtime preparation', 'morning wake-up', 'sleep tracking screen'
- Mention sleep quality cues: 'restful environment', 'sleep disruption', 'comfortable setting'

Focus on health-relevant details that would help determine user intent for nutrition analysis, exercise tracking, health consultation, hydration monitoring, sleep tracking, or medical appointment management.
Provide specific, measurable information that supports both intent recognition and potential data card generation."""
```

### 4. 配置管理

#### 配置文件示例
```python
# config.py
import os
from dataclasses import dataclass

@dataclass
class OCRConfig:
    # AI服务配置
    openai_api_key: str = os.getenv('OPENAI_API_KEY')
    openai_model: str = os.getenv('OPENAI_MODEL', 'gpt-4.1-mini')
    openai_max_tokens: int = int(os.getenv('OPENAI_MAX_TOKENS', '200'))
    openai_temperature: float = float(os.getenv('OPENAI_TEMPERATURE', '0.1'))

    # 文件服务配置
    file_service_base_url: str = os.getenv('FILE_SERVICE_BASE_URL')
    file_service_timeout: int = int(os.getenv('FILE_SERVICE_TIMEOUT', '30'))

    # 业务常量
    image_message_type: int = 2
    default_confidence: float = 0.95

    # 错误提示语
    no_file_id_text: str = "This input has no effect, don't do anything, just reply to me: No file ID passed correctly"
    ocr_failed_text: str = "Image content analysis failed, please try again or provide text input"
    ocr_error_text: str = "Image content analysis processing error, please try again or provide text input"

# 使用配置
config = OCRConfig()
```

### 5. 完整集成示例

```python
# main.py
import logging
from ocr_processor import OCRProcessor
from file_service_client import FileServiceClient
from ai_service_client import AIServiceClient
from config import OCRConfig

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    # 初始化配置
    config = OCRConfig()

    # 初始化服务客户端
    file_service = FileServiceClient(
        base_url=config.file_service_base_url,
        timeout=config.file_service_timeout
    )

    ai_service = AIServiceClient(
        api_key=config.openai_api_key,
        model=config.openai_model
    )

    # 初始化OCR处理器
    ocr_processor = OCRProcessor(file_service, ai_service)

    # 示例消息数据
    message_data = {
        "message": {
            "data": '{"bizMsgType":2,"content":[{"sign":"file123","path":"/uploads/image.jpg"}]}'
        }
    }

    # 处理OCR请求
    result = ocr_processor.process_ocr_if_needed(message_data)

    if result:
        print(f"OCR处理结果: {result.processed_text}")
        print(f"扩展数据: {result.extend_data}")
    else:
        print("非图片消息，无需OCR处理")

if __name__ == "__main__":
    main()
```

---

## 总结

本文档提供了完整的OCR业务逻辑和接口规范，包括：

1. **完整的业务流程** - 从消息接收到结果返回的全流程
2. **详细的接口文档** - 所有必需的文件服务接口规范
3. **核心实现逻辑** - Java代码的详细分析和Python迁移指南
4. **配置和常量** - 所有相关的配置参数和业务常量
5. **错误处理机制** - 完善的异常处理和日志记录规范

通过本文档，Python开发团队可以：
- 完全理解OCR功能的业务逻辑
- 正确调用现有的文件服务接口
- 快速实现Python版本的OCR服务
- 保持与Java版本的功能一致性

**注意事项：**
- 确保Python服务能够正确调用chatbot服务的文件接口
- 保持AI模型配置和提示词的一致性
- 实现相同的错误处理和日志记录机制
- 测试各种边界情况和异常场景

---
