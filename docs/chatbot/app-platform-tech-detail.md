# App 平台详细技术文档 - Python 重构参考

## 1. 概述

本文档专门针对 `chatbot-app-web` 模块的技术实现进行详细分析，为 Python 重构提供完整的参考资料。文档涵盖所有已上线场景的业务逻辑、工具配置、代码实现细节等关键信息。

### 1.1 App 平台架构概览

```
chatbot-app-web/
├── controller/
│   └── OpenController.java          # 回调接口入口
├── business/service/impl/
│   └── ChatBusinessServiceAppImpl.java  # 核心业务处理
├── engine/service/impl/
│   └── EngineAppServiceImpl.java    # AI 引擎服务
├── scene/
│   ├── enums/SceneAppEnum.java      # 场景枚举定义
│   ├── service/impl/                # 场景实现类
│   ├── function/                    # Function Tools
│   └── util/IntentionAppTool.java   # 意图识别工具
└── reminder/service/impl/
    └── ReminderMsgServiceImpl.java  # 提醒消息服务
```

## 2. 已上线场景详细分析

### 2.1 场景枚举配置 (SceneAppEnum)

**关键配置信息**:
```java
// 已上线的核心场景
OTHER_SCENE(OTHER_INTENTION_DICT_TEMPLATE, "Default Chat", ChatDefaultSceneServiceImpl.class, false, null),
NUTRITION_ANALYSIS(NUTRITION_ANALYSIS_INTENTION_DICT_TEMPLATE, "Nutrition analysis", NutritionAnalysisServiceImpl.class, false, null),
HEALTH_ADVISOR(HEALTH_ADVISOR_INTENTION_DICT_TEMPLATE, "Health advisor", HealthConsultationServiceImpl.class, false, null),
EXERCISE_TRACKING(EXERCISE_TRACKING_INTENTION_DICT_TEMPLATE, "Exercise tracking", ExerciseTrackingServiceImpl.class, false, null),
HYDRATION_TRACKING(HYDRATION_TRACKING_INTENTION_DICT_TEMPLATE, "Hydration tracking", HydrationTrackingServiceImpl.class, false, null),
SLEEP_TRACKING(SLEEP_TRACKING_INTENTION_DICT_TEMPLATE, "Sleep tracking", SleepTrackingServiceImpl.class, false, null),
HEALTH_ANALYTICS(HEALTH_ANALYTICS_INTENTION_DICT_TEMPLATE, "Health analytics", HealthAnalyticsServiceImpl.class, false, null),
APPOINTMENT_MANAGEMENT(APPOINTMENT_MANAGEMENT_INTENTION_DICT_TEMPLATE, "Appointment management", AppointmentManagementServiceImpl.class, false, null)
```

**枚举字段说明**:
- `sceneBizCode`: MongoDB 中的提示词模板枚举
- `sceneBizDesc`: 场景描述
- `sceneBeanClazz`: 处理器实现类
- `filterFlag`: 是否在意图识别中过滤（false=参与意图识别，true=直接场景）
- `directlySceneCode`: 直接场景代码（用于特定触发条件）

### 2.2 意图识别机制 (IntentionAppTool)

**核心处理逻辑**:
```java
public static SceneAppEnum processor(IntentionAppQo intentionAppQo) {
    // 1. 构建 AI 请求参数
    AiChatGptQo aiChatGptQo = new AiChatGptQo();
    List<MsgDto> msgDtoList = intentionAppQo.getMsgDtoList();
    
    // 2. 添加用户输入到消息列表
    msgDtoList = MsgTool.addPrompts(msgDtoList, intentionAppQo.getPrompts(), Integer.MAX_VALUE, MsgOwnerEnum.USER);
    
    // 3. 获取需要过滤的场景
    String filterScene = SpringUtil.getProperty("FILTER_SCENE");
    
    // 4. 构建意图分析的场景提示语列表
    List<MongoPromptsTemplateEnum> scenePromptsList = Arrays.stream(SceneAppEnum.values())
            .filter(ele -> !ele.getFilterFlag())  // 过滤掉直接场景
            .map(SceneAppEnum::getSceneBizCode)
            .collect(Collectors.toList());
    
    // 5. 批量获取提示词模板
    Map<MongoPromptsTemplateEnum, String> promptsTemplateMap = PromptsTemplateTool.getPromptsTemplate(scenePromptsList);
    
    // 6. 构建意图分析的完整提示词
    String intentionPrompts = buildIntentionPrompts(promptsTemplateMap, filterScene);
    
    // 7. 调用 GPT 进行意图识别
    AiInterface<AiChatGptQo, AiChatGptVo> aiInterface = intentionAppQo.getAiInterface();
    AiChatGptVo aiChatGptVo = aiInterface.simpleChat(aiChatGptQo);
    
    // 8. 解析 GPT 响应，提取场景枚举
    String aiGc = StrUtil.removeSuffix(aiChatGptVo.getAiGc(), StrUtil.DOT);
    SceneAppEnum sceneAppEnumResult = extractScenePro(aiGc);
    
    // 9. 记录 LLM 日志
    BizLogUtil.createLLMLog(intentionAppQo.getConversationDto().getUserBizId(), 
                           intentionAppQo.getConversationDto().getBizId(), 
                           LLMLogBizEventEnum.ENGIN_INTENT.getCode(), 
                           LLMLogBizEventEnum.ENGIN_INTENT.getDesc(), 
                           aiGc, sceneAppEnumResult.name());
    
    return sceneAppEnumResult;
}
```

**意图识别提示词构建**:
```java
private static String buildIntentionPrompts(Map<MongoPromptsTemplateEnum, String> promptsTemplateMap, String filterScene) {
    StringBuilder intentionPrompts = new StringBuilder();
    intentionPrompts.append("Based on the user's input and conversation history, identify the most appropriate scenario from the following options:\n\n");
    
    // 添加每个场景的描述
    for (Map.Entry<MongoPromptsTemplateEnum, String> entry : promptsTemplateMap.entrySet()) {
        intentionPrompts.append("- ").append(entry.getKey().getType()).append(": ").append(entry.getValue()).append("\n");
    }
    
    intentionPrompts.append("\nPlease respond with only the scenario name, no additional explanation.");
    
    // 如果有过滤场景，添加过滤说明
    if (StrUtil.isNotBlank(filterScene)) {
        intentionPrompts.append("\nNote: Exclude these scenarios: ").append(filterScene);
    }
    
    return intentionPrompts.toString();
}
```

## 3. 基础服务类详解 (BaseAbstractSceneService)

### 3.1 核心方法实现

**buildDefault 方法** - 构建默认 GPT 请求:
```java
protected AiChatGptQo buildDefault(SceneHandlerQo handlerQO) {
    String defaultSystemPrompts = MsgTool.getDefaultSystemPrompts();
    
    // 动态加入安全提示语
    String sensitiveTopicTemplate = PromptsTemplateTool.getPromptsTemplate(MongoPromptsTemplateEnum.SENSITIVE_TOPIC_PROMPT);
    String finalSysPrompt = defaultSystemPrompts + ".\n\n" + sensitiveTopicTemplate;

    // 1. 构建 system 提示语
    return this.buildSystemPrompts(handlerQO, finalSysPrompt, 0)
            // 2. 构建 user 提示语
            .buildUserPrompts(handlerQO, handlerQO.getPrompts())
            // 3. 构建 GPT 请求参数
            .build(handlerQO);
}
```

**buildSystemPrompts 方法** - 构建系统提示词:
```java
protected BaseAbstractSceneService buildSystemPrompts(SceneHandlerQo handlerQO, String systemPrompts, List<String> customSystemPromptsList, Integer index) {
    // 1. 检查会话中是否有需要注入的 system prompt
    if (handlerQO.getConversationDto() != null
            && handlerQO.getConversationDto().getExtendData() != null
            && handlerQO.getConversationDto().getExtendData().isNeedInject()) {
        
        // 处理会话级别的自定义提示词注入
        String customSystemPrompts = handlerQO.getConversationDto().getExtendData().getCustomSystemPrompts();
        if (StrUtil.isNotBlank(customSystemPrompts)) {
            // 解析 JSON 格式的自定义提示词
            try {
                JSONArray jsonArray = JSON.parseArray(customSystemPrompts);
                List<String> finalCustomSystemPrompts = jsonArray.toJavaList(String.class);
                if (CollUtil.isNotEmpty(customSystemPromptsList)) {
                    customSystemPromptsList.addAll(finalCustomSystemPrompts);
                } else {
                    customSystemPromptsList = ListUtil.toLinkedList(finalCustomSystemPrompts);
                }
            } catch (Exception e) {
                // 如果不是 JSON 格式，直接作为字符串处理
                if (CollUtil.isNotEmpty(customSystemPromptsList)) {
                    customSystemPromptsList.add(customSystemPrompts);
                } else {
                    customSystemPromptsList = ListUtil.toLinkedList(customSystemPrompts);
                }
            }
        }
    }
    
    // 2. 构建用户信息字符串
    String userInfoString = buildUserInfoString(handlerQO);
    
    // 3. 拼接自定义系统提示词
    String customSystemPromptsListString = null;
    if (CollUtil.isNotEmpty(customSystemPromptsList)) {
        customSystemPromptsListString = StrUtil.join("\n", customSystemPromptsList);
    }
    
    // 4. 最终拼接成功的系统提示语
    String finalSystemPrompts = Stream.of(systemPrompts, customSystemPromptsListString, userInfoString)
            .map(StrUtil::nullToEmpty)
            .collect(Collectors.joining("\n"));
    
    // 5. 添加到消息列表
    handlerQO.setMsgDtoList(MsgTool.addPrompts(handlerQO.getMsgDtoList(), finalSystemPrompts, index, MsgOwnerEnum.SYSTEM));
    
    return this;
}
```

**buildUserPrompts 方法** - 构建用户提示词:
```java
protected BaseAbstractSceneService buildUserPrompts(SceneHandlerQo handlerQO, String userPrompts) {
    // 用户行为那边在传参数的时候，该 userPrompts 会为 null
    // 所以在这里判断一下，如果用户的语句为空，则不构建用户的 prompts
    if (StringUtils.hasLength(userPrompts)) {
        handlerQO.setMsgDtoList(MsgTool.addPrompts(handlerQO.getMsgDtoList(), userPrompts, Integer.MAX_VALUE, MsgOwnerEnum.USER));
    }
    return this;
}
```

**build 方法** - 构建最终的 AI 请求对象:
```java
protected AiChatGptQo build(SceneHandlerQo handlerQO) {
    // 自己封装的请求参数
    AiChatGptQo aiChatGptQo = new AiChatGptQo();
    aiChatGptQo.setMessageDetailList(MsgTool.msgDTOList2MessageDetailList(MsgTool.controlMaxToken(handlerQO.getMsgDtoList())));
    aiChatGptQo.setTemperature(0.5);
    aiChatGptQo.setFrequencyPenalty(0.0);
    aiChatGptQo.setPresencePenalty(0.0);
    aiChatGptQo.setConversationDto(handlerQO.getConversationDto());
    aiChatGptQo.setLlmLogDto(handlerQO.getLlmLogDto());
    return aiChatGptQo;
}
```

### 3.2 消息处理方法

**beforeHandle 方法** - 前置处理:
```java
protected MessageVo beforeHandle(SceneHandlerQo handlerQO) {
    return createMessagePlaceholder(handlerQO);
}
```

**createMessagePlaceholder 方法** - 创建消息占位符:
```java
protected MessageVo createMessagePlaceholder(SceneHandlerQo handlerQO) {
    String sessionId = null;
    ImMessageCustomDataDto.BizSceneDto bizSceneDto = ImMessageUtil.extractCustomData4Scene(handlerQO.getDoChatHandlerBaseDto().getMessage().getData());
    if (ObjectUtil.isNotNull(bizSceneDto)) {
        sessionId = bizSceneDto.getSessionId();
    }

    MessageQo messageQo = MessageQo.builder()
            .bizId(IdUtil.fastSimpleUUID())
            .sessionId(sessionId)
            .conversationBizId(handlerQO.getConversationDto().getBizId())
            .userBizId(handlerQO.getUserInfo().getBizId())
            .messageType(ImMessageTypeEnum.TEXT.getCode())
            .messageFromType(MessageFromTypeEnum.BOT.getCode())
            .message("")
            .data("")
            .createdAt(System.currentTimeMillis())
            .updatedAt(System.currentTimeMillis())
            .dataCheckStatus(DataCheckStatusEnum.ENABLE.getCode())
            .build();

    return this.messageService.create(messageQo);
}
```

**sendImMessage 方法** - 发送 IM 消息:
```java
protected ImBotMessageVo sendImMessage(ImBotMessageQo qo) {
    return this.imService.bizSendBotMessage(qo);
}
```

**sendImMessageStream 方法** - 发送流式 IM 消息:
```java
protected ImBotMessageVo sendImMessageStream(ImBotMessageQo qo) {
    return this.imService.bizSendBotMessageStream(qo);
}
```

## 4. 核心场景实现详解

### 4.1 默认聊天场景 (ChatDefaultSceneServiceImpl)

**实现逻辑**:
```java
@Service
@Slf4j
public class ChatDefaultSceneServiceImpl extends BaseAbstractSceneService {
    @Override
    public AiChatGptVo handle(AiInterface<AiChatGptQo, AiChatGptVo> aiInterface, SceneHandlerQo handlerQO) {
        MessageVo messageVo = this.beforeHandle(handlerQO);
        return this.doHandle(aiInterface, handlerQO, messageVo);
    }

    private AiChatGptVo doHandle(final AiInterface<AiChatGptQo, AiChatGptVo> aiInterface, final SceneHandlerQo handlerQO, final MessageVo messageVo) {
        // Call GPT
        AiChatGptQo aiChatGptQo = buildDefault(handlerQO);
        aiChatGptQo.setMessageQo(MessageQo.builder().bizId(messageVo.getBizId()).sessionId(this.getSessionIdByHandlerQO(handlerQO)).build());

        // 不立刻发送IM消息等具体场景处理完再根据情况发送
        AiChatGptVo aiChatGptVo = aiInterface.streamChat(aiChatGptQo, new AiCallbackService() {
            @Override
            public void onHandle(StreamChatDto streamChatDto) {
                log.debug("CHAT_DEFAULT_STREAM_CALLBACK_ON_HANDLE|TIME|{}|THREAD_NAME|{}", DateUtil.now(), Thread.currentThread().getName());
            }

            @Override
            public void onSuccess(StreamChatDto streamChatDto) {
                log.debug("CHAT_DEFAULT_STREAM_CALLBACK_ON_SUCCESS|TIME|{}|THREAD_NAME|{}", DateUtil.now(), Thread.currentThread().getName());
            }

            @Override
            public void onError(Object errorInfo) {
                log.error("CHAT_DEFAULT_STREAM_CALLBACK_ON_ERROR|ERROR_INFO|{}", errorInfo);
            }
        });

        return aiChatGptVo;
    }
}
```

**关键特点**:
- 使用 `buildDefault()` 构建标准的 GPT 请求
- 使用 `streamChat()` 进行流式对话
- 实现了完整的回调机制处理流式响应

### 4.2 营养分析场景 (NutritionAnalysisServiceImpl)

**核心实现**:
```java
@Service
@Slf4j
public class NutritionAnalysisServiceImpl extends BaseAbstractSceneService {
    @Autowired
    private NutritionAnalysisFunctionTools nutritionAnalysisFunctionTools;

    @Override
    public AiChatGptVo handle(AiInterface<AiChatGptQo, AiChatGptVo> aiInterface, SceneHandlerQo handlerQo) {
        log.info("NUTRITION_ANALYSIS_SCENE_START|{}", handlerQo.getPrompts());

        try {
            // 1. 获取系统提示词
            String originalPrompts = PromptsTemplateTool.getPromptsTemplate(MongoPromptsTemplateEnum.NUTRITION_ANALYSIS_SYSTEM_PROMPTS);
            String enhancedSystemPrompts = buildEnhancedSystemPrompts(handlerQo, originalPrompts);

            // 2. 构建 AI 请求
            AiChatGptQo aiChatGptQo = this.buildSystemPrompts(handlerQo, enhancedSystemPrompts)
                    .buildUserPrompts(handlerQo, handlerQo.getPrompts())
                    .build(handlerQo);

            // 3. 设置 Function Calling
            setupFunctionCalling(aiChatGptQo, handlerQo);

            // 4. 使用工具链路调用
            AiChatGptVo result = aiInterface.handleWithToolChainingStream(aiChatGptQo, new AiCallbackService() {
                @Override
                public void onSuccess(StreamChatDto streamChatDto) {
                    // 处理营养记录卡片
                    processNutritionRecordCard(result, aiChatGptQo);
                    sendCardToImIfExists(result, handlerQo);
                }
            });

            // 5. 清理用户上下文
            nutritionAnalysisFunctionTools.clearCurrentUserId();

            return result;

        } catch (Exception e) {
            log.error("NUTRITION_ANALYSIS_SCENE_ERROR", e);
            return handleFallback(aiInterface, handlerQo);
        }
    }

    /**
     * 构建增强的系统提示词
     */
    private String buildEnhancedSystemPrompts(SceneHandlerQo handlerQo, String originalPrompts) {
        StringBuilder enhancedPrompts = new StringBuilder();
        enhancedPrompts.append(originalPrompts).append("\n\n");

        // 添加当前时间信息
        enhancedPrompts.append("Current system time: ").append(DateUtil.now()).append("\n");

        // 添加用户信息
        if (handlerQo.getUserInfo() != null) {
            enhancedPrompts.append("User ID: ").append(handlerQo.getUserInfo().getThirdUserId()).append("\n");
        }

        // 添加营养分析特定指导
        enhancedPrompts.append("\nNutrition Analysis Guidelines:\n");
        enhancedPrompts.append("1. Always call getUserNutritionDaily first for historical data\n");
        enhancedPrompts.append("2. Use generateNutritionRecord for meal logging requests\n");
        enhancedPrompts.append("3. Provide detailed nutritional analysis with specific values\n");
        enhancedPrompts.append("4. Include health recommendations based on data\n");

        return enhancedPrompts.toString();
    }

    /**
     * 设置 Function Calling
     */
    private void setupFunctionCalling(AiChatGptQo aiChatGptQo, SceneHandlerQo handlerQo) {
        try {
            String currentUserId = handlerQo.getUserInfo() != null ? handlerQo.getUserInfo().getThirdUserId() : null;
            nutritionAnalysisFunctionTools.setCurrentUserId(currentUserId);

            // 创建 Function 列表
            List<ChatFunction> chatFunctionList = Arrays.asList(
                    ChatFunction.builder()
                            .name("getMealTimingInfo")
                            .description("Get meal timing codes reference. Call ONLY when unclear about timing parameter values for generateNutritionRecord. Returns timing codes: 1=Breakfast, 2=Lunch, 3=Dinner, 4=Snack")
                            .executor(NutritionAnalysisFunctionTools.GetMealTimingInfoRequest.class,
                                    request -> nutritionAnalysisFunctionTools.getMealTimingInfo(request))
                            .build(),
                    ChatFunction.builder()
                            .name("getUserNutritionDaily")
                            .description("MANDATORY: Get user's daily nutrition intake data and analysis. Call for ANY nutrition query: 'what did I eat today', 'my nutrition intake', 'daily calories', 'nutrition summary', 'food history', '今天吃了什么', '我的营养摄入', '每日热量', '营养总结'. NEVER refuse with 'cannot access data' - ALWAYS call this tool first for nutrition queries.")
                            .executor(NutritionAnalysisFunctionTools.GetUserNutritionDailyRequest.class,
                                    request -> nutritionAnalysisFunctionTools.getUserNutritionDaily(request))
                            .build(),
                    ChatFunction.builder()
                            .name("generateNutritionRecord")
                            .description("Generate nutrition record card for meal logging. Call when user wants to log/record meals: 'I ate breakfast', 'log my lunch', 'record dinner', 'add snack', '记录早餐', '添加午餐', '登记晚餐'. Required: foods array with foodName and calories, timing (1=Breakfast, 2=Lunch, 3=Dinner, 4=Snack), recordDate (YYYY-MM-DD format).")
                            .executor(NutritionAnalysisFunctionTools.GenerateNutritionRecordRequest.class,
                                    request -> nutritionAnalysisFunctionTools.generateNutritionRecord(request))
                            .build(),
                    ChatFunction.builder()
                            .name("convertNaturalDateToStandard")
                            .description("Convert natural language date expressions to standard YYYY-MM-DD format. Call when user provides relative dates like 'yesterday', 'last week', 'three days ago', '昨天', '上周', '前天'. Input should be standardized date formula.")
                            .executor(NutritionAnalysisFunctionTools.ConvertDateRequest.class,
                                    request -> nutritionAnalysisFunctionTools.convertNaturalDateToStandard(request))
                            .build()
            );

            // 创建 Function Executor
            FunctionExecutor functionExecutor = new FunctionExecutor(chatFunctionList);
            aiChatGptQo.setFunctionExecutor(functionExecutor);
            aiChatGptQo.setFunctionCall(ChatCompletionRequest.ChatCompletionRequestFunctionCall.of("auto"));

            log.info("NUTRITION_ANALYSIS_FUNCTION_CALLING_SETUP_SUCCESS|USER_ID: {}|FUNCTIONS: {}",
                    currentUserId, chatFunctionList.size());

        } catch (Exception e) {
            log.error("NUTRITION_ANALYSIS_FUNCTION_CALLING_SETUP_ERROR", e);
            throw new RuntimeException("Failed to setup function calling", e);
        }
    }

    /**
     * 处理营养记录卡片生成
     */
    private AiChatGptVo processNutritionRecordCard(AiChatGptVo originalResult, AiChatGptQo aiChatGptQo) {
        try {
            log.info("NUTRITION_PROCESS_CARD_START|ORIGINAL_DATA_TYPE:{}", originalResult.getDataType());

            // 检查是否调用了 generateNutritionRecord 工具
            String nutritionRecordData = extractNutritionRecordData(aiChatGptQo);

            if (nutritionRecordData != null) {
                log.info("NUTRITION_PROCESS_CARD_FOUND_DATA|DATA_LENGTH:{}|DATA_PREVIEW:{}",
                        nutritionRecordData.length(),
                        nutritionRecordData.length() > 200 ? nutritionRecordData.substring(0, 200) + "..." : nutritionRecordData);

                // 构建完整的卡片结构，包含外层固定字段和内层数据
                String completeCardData = buildCompleteNutritionCard(nutritionRecordData);

                // 设置为卡片消息
                originalResult.setData(completeCardData);
                originalResult.setDataType(BizMessageTypeEnum.CARD.getCode());

                log.info("NUTRITION_PROCESS_CARD_SUCCESS|CARD_DATA_LENGTH:{}", completeCardData.length());
            } else {
                log.info("NUTRITION_PROCESS_CARD_NO_DATA_FOUND|SKIPPING_CARD_GENERATION");
            }

            return originalResult;

        } catch (Exception e) {
            log.error("NUTRITION_PROCESS_CARD_ERROR", e);
            return originalResult; // 返回原始结果，不影响主流程
        }
    }

    /**
     * 发送卡片到 IM
     */
    private void sendCardToImIfExists(AiChatGptVo result, SceneHandlerQo handlerQo) {
        try {
            // 检查是否有卡片数据
            if (result.getData() != null && BizMessageTypeEnum.CARD.getCode().equals(result.getDataType())) {
                log.info("NUTRITION_SEND_CARD_DETECTED|BUILDING_IM_MESSAGE");

                // 构建 IM 卡片消息
                ImBotMessageQo imBotMessageQo = ImBotMessageQo.of(
                    handlerQo.getDoChatHandlerBaseDto(),
                    null,
                    result.getData(),
                    BizMessageTypeEnum.CARD.getCode(),
                    ImMessageStatusEnum.FINISHED.getCode()
                );

                // 推送卡片消息到 IM
                this.sendImMessage(imBotMessageQo);

                // 重置 data 避免二次发送卡片
                result.setData(null);
                log.info("NUTRITION_SEND_CARD_SUCCESS");
            } else {
                log.info("NUTRITION_SEND_CARD_NO_CARD_DATA");
            }

        } catch (Exception e) {
            log.error("NUTRITION_SEND_CARD_ERROR", e);
            // 推送失败不影响主流程，只记录错误日志
        }
    }

    /**
     * 降级处理
     */
    private AiChatGptVo handleFallback(AiInterface<AiChatGptQo, AiChatGptVo> aiInterface, SceneHandlerQo handlerQo) {
        try {
            // 获取原始提示词用于 fallback
            String originalPrompts = PromptsTemplateTool.getPromptsTemplate(MongoPromptsTemplateEnum.NUTRITION_ANALYSIS_SYSTEM_PROMPTS);
            String fallbackSystemPrompts = buildEnhancedSystemPrompts(handlerQo, originalPrompts);

            // 构建简单的 AI 请求（不使用 function calling）
            AiChatGptQo fallbackQo = this.buildSystemPrompts(handlerQo, fallbackSystemPrompts)
                .buildUserPrompts(handlerQo, handlerQo.getPrompts())
                .build(handlerQo);

            // 使用简单聊天
            AiChatGptVo result = aiInterface.simpleChat(fallbackQo);
            log.info("NUTRITION_ANALYSIS_FALLBACK_SUCCESS|{}", result.getAiGc());
            return result;

        } catch (Exception fallbackError) {
            log.error("Fallback also failed", fallbackError);

            // 最终降级：返回错误信息
            AiChatGptVo errorResult = new AiChatGptVo();
            errorResult.setAiGc("Sorry, the nutrition analysis service is temporarily unavailable. Please try again later.");
            return errorResult;
        }
    }
}
```

### 4.3 健康咨询场景 (HealthConsultationServiceImpl - HEALTH_ADVISOR 意图识别场景)

**核心实现**:
```java
@Service
@Slf4j
public class HealthConsultationServiceImpl extends BaseAbstractSceneService {
    @Autowired
    private HealthConsultationFunctionTools healthConsultationFunctionTools;

    @Autowired
    private HealthAgentFeignClient healthConsultFeignClient;

    @Override
    public AiChatGptVo handle(AiInterface<AiChatGptQo, AiChatGptVo> aiInterface, SceneHandlerQo handlerQo) {
        log.info("HEALTH_CONSULTATION_SCENE_START|{}", handlerQo.getPrompts());

        try {
            // 构建 AI 请求
            AiChatGptQo aiChatGptQo = this.buildSystemPrompts(handlerQo, buildHealthConsultationSystemPrompts(handlerQo))
                    .buildUserPrompts(handlerQo, handlerQo.getPrompts())
                    .build(handlerQo);

            // 设置 function calling
            setupFunctionCalling(aiChatGptQo, handlerQo);

            log.debug("HEALTH_CONSULTATION_FUNCTION_CALLING_SETUP|USER: {}|HAS_FUNCTION_EXECUTOR: {}|FUNCTIONS_COUNT: {}",
                    handlerQo.getUserInfo() != null ? handlerQo.getUserInfo().getBizId() : "UNKNOWN",
                    aiChatGptQo.getFunctionExecutor() != null,
                    aiChatGptQo.getFunctionExecutor() != null && aiChatGptQo.getFunctionExecutor().getFunctions() != null ?
                        aiChatGptQo.getFunctionExecutor().getFunctions().size() : 0);

            // 使用工具链路调用
            AiChatGptVo result = aiInterface.handleWithToolChainingStream(aiChatGptQo, null);

            // 清理用户上下文
            healthConsultationFunctionTools.clearCurrentUserId();

            log.info("HEALTH_CONSULTATION_SCENE_END|{}", result.getAiGc());
            return result;

        } catch (Exception e) {
            log.error("HEALTH_CONSULTATION_SCENE_ERROR", e);
            return handleFallback(aiInterface, handlerQo);
        }
    }

    /**
     * 构建健康咨询系统提示词
     */
    private String buildHealthConsultationSystemPrompts(SceneHandlerQo handlerQo) {
        StringBuilder systemPrompts = new StringBuilder();

        // 1. 获取基础系统提示词
        String basePrompts = PromptsTemplateTool.getPromptsTemplate(MongoPromptsTemplateEnum.HEALTH_CONSULTATION_SYSTEM_PROMPTS);
        systemPrompts.append(basePrompts).append("\n\n");

        // 2. 添加 Function Calling 指导
        String functionCallingPrompts = PromptsTemplateTool.getPromptsTemplate(MongoPromptsTemplateEnum.HEALTH_CONSULTATION_FUNCTION_CALLING_PROMPTS);
        systemPrompts.append(functionCallingPrompts).append("\n\n");

        // 3. 添加用户案例数据
        String userCaseData = PromptsTemplateTool.getPromptsTemplate(MongoPromptsTemplateEnum.HEALTH_CONSULTATION_USER_CASE_DATA);
        systemPrompts.append("User Case Data Examples:\n").append(userCaseData).append("\n\n");

        // 4. 添加当前时间和用户信息
        systemPrompts.append("Current system time: ").append(DateUtil.now()).append("\n");
        if (handlerQo.getUserInfo() != null) {
            systemPrompts.append("Current user ID: ").append(handlerQo.getUserInfo().getThirdUserId()).append("\n");
        }

        // 5. 添加健康咨询特定指导
        systemPrompts.append("\nHealth Consultation Guidelines:\n");
        systemPrompts.append("1. ALWAYS call getDailyDetail first for comprehensive health data\n");
        systemPrompts.append("2. Provide evidence-based health advice\n");
        systemPrompts.append("3. Recommend professional medical consultation for serious symptoms\n");
        systemPrompts.append("4. Maintain a caring and professional tone\n");
        systemPrompts.append("5. Include specific health metrics and trends in analysis\n");

        return systemPrompts.toString();
    }

    /**
     * 设置 Function Calling
     */
    private void setupFunctionCalling(AiChatGptQo aiChatGptQo, SceneHandlerQo handlerQo) {
        try {
            String currentUserId = handlerQo.getUserInfo() != null ? handlerQo.getUserInfo().getThirdUserId() : null;
            healthConsultationFunctionTools.setCurrentUserId(currentUserId);

            List<ChatFunction> chatFunctionList = Arrays.asList(
                ChatFunction.builder()
                    .name("getDailyDetail")
                    .description("MANDATORY: Get user's comprehensive health data including exercise, nutrition, sleep, and hydration with historical trends. Call for ANY health consultation request: 'analyze my health', 'health status', 'recent health data', 'health trends', 'overall wellness', '分析我的健康', '健康状况', '最近健康数据', '健康趋势', '整体健康'. NEVER refuse with 'cannot access data' - ALWAYS call this tool first for comprehensive health analysis.")
                    .executor(HealthConsultationFunctionTools.GetDailyDetailRequest.class,
                            request -> healthConsultationFunctionTools.getDailyDetail(request))
                    .build()
            );

            FunctionExecutor functionExecutor = new FunctionExecutor(chatFunctionList);
            aiChatGptQo.setFunctionExecutor(functionExecutor);
            aiChatGptQo.setFunctionCall(ChatCompletionRequest.ChatCompletionRequestFunctionCall.of("auto"));

            log.info("HEALTH_CONSULTATION_FUNCTION_CALLING_SETUP_SUCCESS|USER_ID: {}|FUNCTIONS: {}",
                    currentUserId, chatFunctionList.size());

        } catch (Exception e) {
            log.error("HEALTH_CONSULTATION_FUNCTION_CALLING_SETUP_ERROR", e);
            throw new RuntimeException("Failed to setup function calling", e);
        }
    }

    /**
     * 降级处理 - 调用外部健康咨询服务
     */
    private AiChatGptVo handleFallback(AiInterface<AiChatGptQo, AiChatGptVo> aiInterface, SceneHandlerQo handlerQo) {
        try {
            log.info("HEALTH_CONSULTATION_FALLBACK_TO_EXTERNAL_SERVICE");

            // 构建外部服务请求
            HealthConsultQo healthConsultQo = new HealthConsultQo();
            healthConsultQo.setUser_id(handlerQo.getUserInfo().getThirdUserId());
            healthConsultQo.setConversation_id(handlerQo.getConversationDto().getUniqueSign());
            healthConsultQo.setContent(handlerQo.getPrompts());

            // 构建自定义数据
            ImMessageCustomDataDto imMessageCustomDataDto = new ImMessageCustomDataDto();
            imMessageCustomDataDto.setType(ImMessageCustomDataTypeEnum.SCENE.getCode());
            imMessageCustomDataDto.setSceneCode(DirectlySceneConstant.CODE_APP_HEALTH_CONSULTATION_ASSISTANT);

            AiChatGptVo aiChatGptVo = new AiChatGptVo();
            String strCustomData = JSON.toJSONString(imMessageCustomDataDto, JSONWriter.Feature.WriteNulls);
            aiChatGptVo.setData(strCustomData);

            // 调用外部健康咨询服务
            HealthConsultVo healthConsultVo = healthConsultFeignClient.healthConsult(healthConsultQo);
            if (healthConsultVo != null && healthConsultVo.getData() != null) {
                aiChatGptVo.setAiGc(healthConsultVo.getData().getAnswer());
                log.info("Get answer from Health consult agent: {}", healthConsultVo.getData().getAnswer());
            } else {
                log.warn("Failed to get answer from Health consult agent for inputs: {}", handlerQo.getPrompts());
                aiChatGptVo.setAiGc("I'm here to help with your health questions. Could you please provide more specific details about your health concerns?");
            }

            return aiChatGptVo;

        } catch (Exception fallbackError) {
            log.error("Fallback also failed", fallbackError);

            // 最终降级：返回错误信息
            AiChatGptVo errorResult = new AiChatGptVo();
            errorResult.setAiGc("Sorry, the health consultation service is temporarily unavailable. Please try again later.");
            return errorResult;
        }
    }
}
```

### 4.4 运动追踪场景 (ExerciseTrackingServiceImpl)

**核心实现**:
```java
@Service
@Slf4j
public class ExerciseTrackingServiceImpl extends BaseAbstractSceneService {
    @Autowired
    private ExerciseTrackingFunctionTools exerciseTrackingFunctionTools;

    // 多卡片数据存储（临时存储，用于批量发送）
    private static final Map<String, List<String>> MULTIPLE_CARD_DATA_STORAGE = new ConcurrentHashMap<>();

    @Override
    public AiChatGptVo handle(AiInterface<AiChatGptQo, AiChatGptVo> aiInterface, SceneHandlerQo handlerQo) {
        log.info("EXERCISE_TRACKING_SCENE_START|{}", handlerQo.getPrompts());

        // 生成请求 ID 用于数据关联
        String requestId = IdUtil.fastSimpleUUID();

        try {
            // 获取系统提示词
            String originalPrompts = PromptsTemplateTool.getPromptsTemplate(MongoPromptsTemplateEnum.EXERCISE_TRACKING_SYSTEM_PROMPTS);
            String enhancedSystemPrompts = buildEnhancedSystemPrompts(handlerQo, originalPrompts);

            // 构建 AI 请求
            AiChatGptQo aiChatGptQo = this.buildSystemPrompts(handlerQo, enhancedSystemPrompts)
                    .buildUserPrompts(handlerQo, handlerQo.getPrompts())
                    .build(handlerQo);

            // 配置 Function Calling
            setupFunctionCalling(aiChatGptQo, handlerQo);

            // 创建 finalResult 对象，用于在回调中访问
            AiChatGptVo finalResult = new AiChatGptVo();
            finalResult.setId(requestId); // 设置请求 ID 用于数据关联

            // 使用优化后的流式工具链路调用（V6版本：工具非流式+最终文本流式）
            AiChatGptVo result = aiInterface.handleWithToolChainingStream(aiChatGptQo, new AiCallbackService() {

                @Override
                public void onHandle(StreamChatDto streamChatDto) {
                    // 流式处理中的回调
                }

                @Override
                public void onSuccess(StreamChatDto streamChatDto) {
                    // 发送卡片到 IM
                    sendCardToImIfExists(finalResult, handlerQo);
                }

                @Override
                public void onError(Object errorInfo) {
                    // 错误处理
                }
            });

            // 清理用户上下文
            exerciseTrackingFunctionTools.clearCurrentUserId();

            log.info("EXERCISE_TRACKING_SCENE_END|{}", result.getAiGc());
            return result;

        } catch (Exception e) {
            log.error("EXERCISE_TRACKING_SCENE_ERROR", e);
            return handleFallback(aiInterface, handlerQo);
        }
    }

    /**
     * 设置 Function Calling
     */
    private void setupFunctionCalling(AiChatGptQo aiChatGptQo, SceneHandlerQo handlerQo) {
        try {
            String currentUserId = handlerQo.getUserInfo() != null ? handlerQo.getUserInfo().getThirdUserId() : null;
            exerciseTrackingFunctionTools.setCurrentUserId(currentUserId);

            // 创建 Function 列表
            List<ChatFunction> chatFunctionList = Arrays.asList(
                    ChatFunction.builder()
                            .name("getDailyDetail")
                            .description("MANDATORY: Get user's daily exercise progress, goals, and historical exercise data. Call for ANY query about past exercise OR exercise performance consultation: 'did I exercise today', 'my exercise progress', 'exercise history', 'how was my workout', 'am I walking fast enough', '今天运动了吗', '我的运动进度', '我是不是走得有点快', '运动表现怎么样'. NEVER refuse with 'cannot access data' - ALWAYS call this tool first for historical exercise queries and performance analysis.")
                            .executor(ExerciseTrackingFunctionTools.GetDailyDetailRequest.class,
                                    request -> exerciseTrackingFunctionTools.getDailyDetail(request))
                            .build(),
                    ChatFunction.builder()
                            .name("getExerciseDaily")
                            .description("Get detailed daily exercise records for specific date ranges. Call when user asks for specific exercise data: 'show my exercise on June 1st', 'exercise details for last week', 'workout summary for specific dates', '显示6月1日的运动', '上周的运动详情', '特定日期的锻炼总结'.")
                            .executor(ExerciseTrackingFunctionTools.GetExerciseDailyRequest.class,
                                    request -> exerciseTrackingFunctionTools.getExerciseDaily(request))
                            .build(),
                    ChatFunction.builder()
                            .name("getAllExerciseList")
                            .description("Get complete list of available exercise types with their IDs. Call when user asks about exercise types: 'what exercises can I do', 'show exercise list', 'available workouts', '有什么运动', '运动类型列表', '可用的锻炼'.")
                            .executor(ExerciseTrackingFunctionTools.GetAllExerciseListRequest.class,
                                    request -> exerciseTrackingFunctionTools.getAllExerciseList(request))
                            .build(),
                    ChatFunction.builder()
                            .name("generateExerciseRecord")
                            .description("Generate exercise record card for workout logging. Call when user wants to log/record exercise: 'I did 30 minutes running', 'log my workout', 'record exercise', 'add training session', '记录30分钟跑步', '登记我的锻炼', '添加训练'. Required: taskId (exercise type ID), duration (seconds), optional: distance, avgHeartRate, intensityLevel.")
                            .executor(ExerciseTrackingFunctionTools.GenerateExerciseRecordRequest.class,
                                    request -> exerciseTrackingFunctionTools.generateExerciseRecord(request))
                            .build()
            );

            // 创建 Function Executor
            FunctionExecutor functionExecutor = new FunctionExecutor(chatFunctionList);
            aiChatGptQo.setFunctionExecutor(functionExecutor);
            aiChatGptQo.setFunctionCall(ChatCompletionRequest.ChatCompletionRequestFunctionCall.of("auto"));

            log.info("EXERCISE_TRACKING_FUNCTION_CALLING_SETUP_SUCCESS|USER_ID: {}|FUNCTIONS: {}",
                    currentUserId, chatFunctionList.size());

        } catch (Exception e) {
            log.error("EXERCISE_TRACKING_FUNCTION_CALLING_SETUP_ERROR", e);
            throw new RuntimeException("Failed to setup function calling", e);
        }
    }

    /**
     * 发送卡片到 IM（支持多卡片处理）
     */
    private void sendCardToImIfExists(AiChatGptVo result, SceneHandlerQo handlerQo) {
        try {
            String requestId = result.getId();

            // 检查是否有多卡片数据
            if (MULTIPLE_CARD_DATA_STORAGE.containsKey(requestId)) {
                List<String> cardDataList = MULTIPLE_CARD_DATA_STORAGE.get(requestId);
                if (cardDataList.size() > 1) {
                    log.info("MULTIPLE_EXERCISE_CARDS_DETECTED|COUNT:{}|PROCESSING_BATCH_PUSH", cardDataList.size());

                    sendMultipleCardsDirectly(handlerQo, requestId);
                    // 清缓存
                    MULTIPLE_CARD_DATA_STORAGE.remove(requestId);
                    log.info("CLEANED_UP_MULTIPLE_CARD_DATA_STORAGE|REQUEST_ID:{}", requestId);
                } else {
                    log.info("SINGLE_EXERCISE_CARD_DETECTED|PROCESSING_SINGLE_CARD_PUSH");
                    // 单张卡片，使用原有逻辑
                    String cardData = cardDataList.get(0);
                    sendSingleCardDirectly(handlerQo, cardData);
                    MULTIPLE_CARD_DATA_STORAGE.remove(requestId);
                }
            } else {
                log.info("EXERCISE_SEND_CARD_NO_CARD_DATA|REQUEST_ID:{}", requestId);
            }

        } catch (Exception e) {
            log.error("EXERCISE_SEND_CARD_ERROR", e);
        }
    }
}
```

### 4.5 水分追踪场景 (HydrationTrackingServiceImpl)

**核心实现**:
```java
@Service
@Slf4j
public class HydrationTrackingServiceImpl extends BaseAbstractSceneService {
    @Autowired
    private HydrationTrackingFunctionTools hydrationTrackingFunctionTools;

    @Override
    public AiChatGptVo handle(AiInterface<AiChatGptQo, AiChatGptVo> aiInterface, SceneHandlerQo handlerQo) {
        log.info("HYDRATION_TRACKING_SCENE_START|{}", handlerQo.getPrompts());

        try {
            // 获取系统提示词
            String originalPrompts = PromptsTemplateTool.getPromptsTemplate(MongoPromptsTemplateEnum.HYDRATION_TRACKING_SYSTEM_PROMPTS);
            String enhancedSystemPrompts = buildEnhancedSystemPrompts(handlerQo, originalPrompts);

            // 构建 AI 请求
            AiChatGptQo aiChatGptQo = this.buildSystemPrompts(handlerQo, enhancedSystemPrompts)
                    .buildUserPrompts(handlerQo, handlerQo.getPrompts())
                    .build(handlerQo);

            // 配置 Function Calling
            setupFunctionCalling(aiChatGptQo, handlerQo);

            // 使用流式工具链路调用
            AiChatGptVo finalResult = new AiChatGptVo();
            AiChatGptVo result = aiInterface.handleWithToolChainingStream(aiChatGptQo, new AiCallbackService() {
                @Override
                public void onSuccess(StreamChatDto streamChatDto) {
                    sendCardToImIfExists(finalResult, handlerQo);
                }
            });

            // 处理水分记录卡片
            processHydrationRecordCard(result, aiChatGptQo);
            finalResult.setData(result.getData());
            finalResult.setDataType(result.getDataType());
            result.setData(null);

            // 清空 aiGc 文本内容，避免 afterHandle 重复发送 IM 文本消息
            result.setAiGc(null);
            return result;

        } catch (Exception e) {
            log.error("HYDRATION_TRACKING_SCENE_ERROR", e);
            return handleFallback(aiInterface, handlerQo);
        }
    }

    /**
     * 设置 Function Calling
     */
    private void setupFunctionCalling(AiChatGptQo aiChatGptQo, SceneHandlerQo handlerQo) {
        try {
            String currentUserId = handlerQo.getUserInfo() != null ? handlerQo.getUserInfo().getThirdUserId() : null;
            hydrationTrackingFunctionTools.setCurrentUserId(currentUserId);

            List<ChatFunction> chatFunctionList = Arrays.asList(
                ChatFunction.builder()
                    .name("getDailyDetail")
                    .description("MANDATORY: Get user's daily hydration progress, goals, and historical water intake data. Call for ANY query about past hydration: 'how much water did I drink today', 'my hydration progress', 'water intake history', 'recent drinking patterns', 'hydration status', 'daily water consumption', 'am I drinking enough', 'water intake summary', '今天喝了多少水', '我的饮水进度', '最近喝水情况', '饮水记录', '喝水够吗', '水分摄入量'. NEVER refuse with 'cannot access data' - ALWAYS call this tool first for historical hydration queries.")
                    .executor(HydrationTrackingFunctionTools.GetDailyDetailRequest.class,
                            request -> hydrationTrackingFunctionTools.getDailyDetail(request))
                    .build(),
                ChatFunction.builder()
                    .name("generateHydrationRecord")
                    .description("Generate hydration record card for water intake logging. Call when user wants to log/record water consumption: 'I drank 500ml water', 'log my water intake', 'record drinking', 'add hydration', '记录喝水500毫升', '添加饮水记录', '登记水分摄入'. Required: amount (ml), recordDate (YYYY-MM-DD format), optional: drinkType.")
                    .executor(HydrationTrackingFunctionTools.GenerateHydrationRecordRequest.class,
                            request -> hydrationTrackingFunctionTools.generateHydrationRecord(request))
                    .build()
            );

            FunctionExecutor functionExecutor = new FunctionExecutor(chatFunctionList);
            aiChatGptQo.setFunctionExecutor(functionExecutor);
            aiChatGptQo.setFunctionCall(ChatCompletionRequest.ChatCompletionRequestFunctionCall.of("auto"));

        } catch (Exception e) {
            log.error("HYDRATION_TRACKING_FUNCTION_CALLING_SETUP_ERROR", e);
            throw new RuntimeException("Failed to setup function calling", e);
        }
    }
}
```

### 4.6 睡眠追踪场景 (SleepTrackingServiceImpl)

**核心实现**:
```java
@Service
@Slf4j
public class SleepTrackingServiceImpl extends BaseAbstractSceneService {
    @Autowired
    private SleepTrackingFunctionTools sleepTrackingFunctionTools;

    @Override
    public AiChatGptVo handle(AiInterface<AiChatGptQo, AiChatGptVo> aiInterface, SceneHandlerQo handlerQo) {
        log.info("SLEEP_TRACKING_SCENE_START|{}", handlerQo.getPrompts());

        try {
            // 获取系统提示词
            String originalPrompts = PromptsTemplateTool.getPromptsTemplate(MongoPromptsTemplateEnum.SLEEP_TRACKING_SYSTEM_PROMPTS);
            String enhancedSystemPrompts = buildEnhancedSystemPrompts(handlerQo, originalPrompts);

            // 构建 AI 请求
            AiChatGptQo aiChatGptQo = this.buildSystemPrompts(handlerQo, enhancedSystemPrompts)
                    .buildUserPrompts(handlerQo, handlerQo.getPrompts())
                    .build(handlerQo);

            // 配置 Function Calling
            setupFunctionCalling(aiChatGptQo, handlerQo);

            // 使用流式工具链路调用
            AiChatGptVo result = aiInterface.handleWithToolChainingStream(aiChatGptQo, null);

            // 清理用户上下文
            sleepTrackingFunctionTools.clearCurrentUserId();

            return result;

        } catch (Exception e) {
            log.error("SLEEP_TRACKING_SCENE_ERROR", e);
            return handleFallback(aiInterface, handlerQo);
        }
    }

    /**
     * 设置 Function Calling
     */
    private void setupFunctionCalling(AiChatGptQo aiChatGptQo, SceneHandlerQo handlerQo) {
        try {
            String currentUserId = handlerQo.getUserInfo() != null ? handlerQo.getUserInfo().getThirdUserId() : null;
            sleepTrackingFunctionTools.setCurrentUserId(currentUserId);

            List<ChatFunction> chatFunctionList = Arrays.asList(
                ChatFunction.builder()
                    .name("getDailyDetail")
                    .description("MANDATORY: Get user's daily sleep progress, goals, and historical sleep data. Call for ANY query about past sleep: 'how is my sleep', 'recent sleep patterns', 'sleep history', '最近睡眠怎么样', '昨天睡了多久'. NEVER refuse with 'cannot access data' - ALWAYS call this tool first for historical sleep queries.")
                    .executor(SleepTrackingFunctionTools.GetDailyDetailRequest.class,
                            request -> sleepTrackingFunctionTools.getDailyDetail(request))
                    .build(),
                ChatFunction.builder()
                    .name("generateSleepRecord")
                    .description("Generate sleep record card for sleep logging. Call when user wants to log/record sleep: 'I slept 8 hours', 'log my sleep', 'record sleep time', 'add sleep data', '记录睡眠8小时', '添加睡眠记录', '登记睡觉时间'. Required: sleepDuration (minutes), bedTime, wakeTime, recordDate (YYYY-MM-DD format), optional: sleepQuality.")
                    .executor(SleepTrackingFunctionTools.GenerateSleepRecordRequest.class,
                            request -> sleepTrackingFunctionTools.generateSleepRecord(request))
                    .build()
            );

            FunctionExecutor functionExecutor = new FunctionExecutor(chatFunctionList);
            aiChatGptQo.setFunctionExecutor(functionExecutor);
            aiChatGptQo.setFunctionCall(ChatCompletionRequest.ChatCompletionRequestFunctionCall.of("auto"));

        } catch (Exception e) {
            log.error("SLEEP_TRACKING_FUNCTION_CALLING_SETUP_ERROR", e);
            throw new RuntimeException("Failed to setup function calling", e);
        }
    }
}
```

### 4.7 健康分析场景 (HealthAnalyticsServiceImpl)

**核心实现**:
```java
@Service
@Slf4j
public class HealthAnalyticsServiceImpl extends BaseAbstractSceneService {
    @Autowired
    private HealthAnalyticsFunctionTools healthAnalyticsFunctionTools;

    @Override
    public AiChatGptVo handle(AiInterface<AiChatGptQo, AiChatGptVo> aiInterface, SceneHandlerQo handlerQo) {
        log.info("HEALTH_ANALYTICS_SCENE_START|{}", handlerQo.getPrompts());

        try {
            // 获取系统提示词
            String originalPrompts = PromptsTemplateTool.getPromptsTemplate(MongoPromptsTemplateEnum.HEALTH_ANALYTICS_SYSTEM_PROMPTS);
            String enhancedSystemPrompts = buildEnhancedSystemPrompts(handlerQo, originalPrompts);

            // 构建 AI 请求
            AiChatGptQo aiChatGptQo = this.buildSystemPrompts(handlerQo, enhancedSystemPrompts)
                    .buildUserPrompts(handlerQo, handlerQo.getPrompts())
                    .build(handlerQo);

            // 设置 Function Calling 工具
            setupFunctionCalling(aiChatGptQo, handlerQo);

            // 使用流式回调处理
            AiChatGptVo result = aiInterface.handleWithToolChainingStream(aiChatGptQo, null);

            // 清理用户上下文
            healthAnalyticsFunctionTools.clearCurrentUserId();

            return result;

        } catch (Exception e) {
            log.error("HEALTH_ANALYTICS_SCENE_ERROR", e);
            return handleFallback(aiInterface, handlerQo);
        }
    }

    /**
     * 设置 Function Calling
     */
    private void setupFunctionCalling(AiChatGptQo aiChatGptQo, SceneHandlerQo handlerQo) {
        try {
            String currentUserId = handlerQo.getUserInfo() != null ? handlerQo.getUserInfo().getThirdUserId() : null;
            healthAnalyticsFunctionTools.setCurrentUserId(currentUserId);

            List<ChatFunction> chatFunctionList = Arrays.asList(
                ChatFunction.builder()
                    .name("getComprehensiveHealthData")
                    .description("MANDATORY: Get user's comprehensive health analytics including exercise, nutrition, sleep, and hydration trends with statistical analysis. Call for ANY health analytics query: 'analyze my health trends', 'health report', 'overall health status', 'health statistics', 'wellness summary', '分析我的健康趋势', '健康报告', '整体健康状况', '健康统计', '健康总结'. NEVER refuse with 'cannot access data' - ALWAYS call this tool first for comprehensive health analytics.")
                    .executor(HealthAnalyticsFunctionTools.GetComprehensiveHealthDataRequest.class,
                            request -> healthAnalyticsFunctionTools.getComprehensiveHealthData(request))
                    .build(),
                ChatFunction.builder()
                    .name("getDetailedExerciseHistory")
                    .description("MANDATORY: Get detailed exercise history with specific workout data, duration, distance, calories, and statistics. Call for ANY detailed exercise analysis: 'exercise details', 'workout performance', 'fitness analysis', 'exercise trends', '运动详情', '健身分析', '运动表现'. Use this when you need specific exercise insights beyond basic overview.")
                    .executor(HealthAnalyticsFunctionTools.GetDetailedExerciseHistoryRequest.class,
                            request -> healthAnalyticsFunctionTools.getDetailedExerciseHistory(request))
                    .build(),
                ChatFunction.builder()
                    .name("getDetailedNutritionHistory")
                    .description("MANDATORY: Get detailed nutrition history with meal-by-meal breakdown, macronutrient distribution, and dietary patterns. Call for ANY detailed nutrition analysis: 'nutrition details', 'diet analysis', 'eating patterns', 'macro breakdown', '营养详情', '饮食分析', '营养摄入'. Use this when you need specific nutrition insights beyond basic overview.")
                    .executor(HealthAnalyticsFunctionTools.GetDetailedNutritionHistoryRequest.class,
                            request -> healthAnalyticsFunctionTools.getDetailedNutritionHistory(request))
                    .build()
            );

            FunctionExecutor functionExecutor = new FunctionExecutor(chatFunctionList);
            aiChatGptQo.setFunctionExecutor(functionExecutor);
            aiChatGptQo.setFunctionCall(ChatCompletionRequest.ChatCompletionRequestFunctionCall.of("auto"));

            log.info("HEALTH_ANALYTICS_FUNCTION_CALLING_SETUP_SUCCESS|USER_ID: {}|FUNCTIONS: {}",
                    currentUserId, chatFunctionList.size());

        } catch (Exception e) {
            log.error("HEALTH_ANALYTICS_FUNCTION_CALLING_SETUP_ERROR", e);
            throw new RuntimeException("Failed to setup function calling", e);
        }
    }

    /**
     * 构建增强版系统提示词
     */
    private String buildEnhancedSystemPrompts(SceneHandlerQo handlerQo, String originalPrompts) {
        // 获取用户性别信息用于BMR估算
        String userGenderInfo = getUserGenderInfo(handlerQo);

        // 生成精确到时分秒的当前时间信息
        LocalDateTime now = LocalDateTime.now();
        String currentDateTime = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String currentDate = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String currentTime = now.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        String currentDayOfWeek = now.getDayOfWeek().toString();

        // 构建系统时间信息
        StringBuilder systemTimeInfo = new StringBuilder();
        systemTimeInfo.append("**SYSTEM TIME INFORMATION:**\n")
                .append("Current Date and Time: ").append(currentDateTime).append("\n")
                .append("Current Date: ").append(currentDate).append("\n")
                .append("Current Time: ").append(currentTime).append("\n")
                .append("Day of Week: ").append(currentDayOfWeek).append("\n")
                .append("User Gender Info: ").append(userGenderInfo).append("\n\n");

        // 添加敏感话题处理
        String sensitiveTopicTemplate = PromptsTemplateTool.getPromptsTemplate(MongoPromptsTemplateEnum.SENSITIVE_TOPIC_PROMPT);
        String sensitiveTopicInfo = "**SAFETY GUIDELINES:**\n" + sensitiveTopicTemplate + "\n\n";

        // 如果MongoDB中有完整的提示词，直接使用
        if (StrUtil.isNotBlank(originalPrompts)) {
            return systemTimeInfo.toString() + sensitiveTopicInfo + originalPrompts;
        }

        // 如果没有配置，使用基础提示词构建
        StringBuilder fallbackPrompts = new StringBuilder();
        fallbackPrompts.append("You are a comprehensive health analytics specialist with expertise in data interpretation, trend analysis, and personalized health insights.\n\n")
                .append("## Core Capabilities:\n")
                .append("1. **ANALYSIS MODE**: Analyze historical health data and provide insights\n")
                .append("2. **PLANNING MODE**: Generate future health plans and recommendations\n\n")
                .append("## Function Calling Protocol:\n")
                .append("- **getComprehensiveHealthData**: Get overview of all health dimensions\n")
                .append("- **getDetailedExerciseHistory**: Get specific exercise data and trends\n")
                .append("- **getDetailedNutritionHistory**: Get detailed nutrition patterns\n\n")
                .append("## Key Guidelines:\n")
                .append("- Always call appropriate tools for historical analysis\n")
                .append("- Provide data-driven insights with specific metrics\n")
                .append("- Include trend analysis and correlations\n")
                .append("- Offer actionable recommendations based on patterns\n")
                .append("- Use encouraging and supportive language\n\n");

        return systemTimeInfo.toString() + sensitiveTopicInfo + fallbackPrompts.toString();
    }

    /**
     * 获取用户性别信息用于BMR估算
     */
    private String getUserGenderInfo(SceneHandlerQo handlerQo) {
        if (handlerQo.getUserInfo() != null && handlerQo.getUserInfo().getGender() != null) {
            Integer genderCode = handlerQo.getUserInfo().getGender();
            if (genderCode == 1) {
                return "Male (BMR estimate: 1500-1800 kcal/day)";
            } else if (genderCode == 2) {
                return "Female (BMR estimate: 1200-1500 kcal/day)";
            }
        }
        return "Unknown gender (BMR estimate: 1350-1650 kcal/day)";
    }

    /**
     * 降级处理
     */
    private AiChatGptVo handleFallback(AiInterface<AiChatGptQo, AiChatGptVo> aiInterface, SceneHandlerQo handlerQo) {
        log.warn("HEALTH_ANALYTICS_FALLBACK_START");

        try {
            // 获取原始提示词用于fallback
            String originalPrompts = PromptsTemplateTool.getPromptsTemplate(MongoPromptsTemplateEnum.HEALTH_ANALYTICS_SYSTEM_PROMPTS);
            String fallbackSystemPrompts = buildEnhancedSystemPrompts(handlerQo, originalPrompts);

            // 构建简单的AI请求（不使用function calling）
            AiChatGptQo fallbackQo = this.buildSystemPrompts(handlerQo, fallbackSystemPrompts)
                .buildUserPrompts(handlerQo, handlerQo.getPrompts())
                .build(handlerQo);

            // 使用简单聊天
            AiChatGptVo result = aiInterface.simpleChat(fallbackQo);

            log.info("HEALTH_ANALYTICS_FALLBACK_SUCCESS|{}", result.getAiGc());
            return result;

        } catch (Exception fallbackError) {
            log.error("HEALTH_ANALYTICS_FALLBACK_ERROR", fallbackError);

            // 最终降级：返回错误信息
            AiChatGptVo errorResult = new AiChatGptVo();
            errorResult.setAiGc("Sorry, the health analytics service is temporarily unavailable. Please try again later or try asking about general health topics.");
            return errorResult;
        }
    }
}
```

### 4.8 预约管理场景 (AppointmentManagementServiceImpl)

**核心实现**:
```java
@Service
@Slf4j
public class AppointmentManagementServiceImpl extends BaseAbstractSceneService {
    @Autowired
    private AppointmentManagementFunctionTools appointmentManagementFunctionTools;

    @Autowired
    private HsdService hsdService;

    @Override
    public AiChatGptVo handle(AiInterface<AiChatGptQo, AiChatGptVo> aiInterface, SceneHandlerQo handlerQo) {
        log.info("APPOINTMENT_MANAGEMENT_SCENE_START|CONVERSATION_ID: {}, USER_ID: {}, INPUT: {}",
                handlerQo.getConversationDto().getUniqueSign(),
                handlerQo.getUserInfo().getBizId(),
                handlerQo.getPrompts());

        MessageVo messageVo = this.beforeHandle(handlerQo);
        return this.doHandle(aiInterface, handlerQo, messageVo);
    }

    private AiChatGptVo doHandle(final AiInterface<AiChatGptQo, AiChatGptVo> aiInterface, final SceneHandlerQo handlerQo, final MessageVo messageVo) {
        try {
            // 获取系统提示词
            String originalPrompts = PromptsTemplateTool.getPromptsTemplate(MongoPromptsTemplateEnum.APPOINTMENT_MANAGEMENT_SYSTEM_PROMPTS);
            String enhancedSystemPrompts = buildEnhancedSystemPrompts(handlerQo, originalPrompts);

            // 构建 AI 请求
            AiChatGptQo aiChatGptQo = this.buildSystemPrompts(handlerQo, enhancedSystemPrompts)
                    .buildUserPrompts(handlerQo, handlerQo.getPrompts())
                    .build(handlerQo);

            // 设置 Function Calling
            setupFunctionCalling(aiChatGptQo, handlerQo);

            // 使用工具链路调用
            AiChatGptVo result = aiInterface.handleWithToolChainingStream(aiChatGptQo, null);

            // 清理用户上下文
            appointmentManagementFunctionTools.clearCurrentUserId();

            return result;

        } catch (Exception e) {
            log.error("APPOINTMENT_MANAGEMENT_SCENE_ERROR", e);
            return handleFallback(aiInterface, handlerQo);
        }
    }

    /**
     * 设置 Function Calling
     */
    private void setupFunctionCalling(AiChatGptQo aiChatGptQo, SceneHandlerQo handlerQo) {
        try {
            String currentUserId = handlerQo.getUserInfo() != null ? handlerQo.getUserInfo().getThirdUserId() : null;
            appointmentManagementFunctionTools.setCurrentUserId(currentUserId);

            List<ChatFunction> chatFunctionList = Arrays.asList(
                ChatFunction.builder()
                    .name("getAvailableSlots")
                    .description("Get available appointment time slots for booking. Call when user wants to schedule appointments: 'book appointment', 'schedule doctor visit', 'available times', 'when can I see doctor', '预约医生', '安排就诊时间', '什么时候有空'.")
                    .executor(AppointmentManagementFunctionTools.GetAvailableSlotsRequest.class,
                            request -> appointmentManagementFunctionTools.getAvailableSlots(request))
                    .build(),
                ChatFunction.builder()
                    .name("bookAppointment")
                    .description("Book a medical appointment. Call when user confirms appointment booking: 'book this slot', 'confirm appointment', 'schedule for this time', '确认预约', '预定这个时间', '安排这个时段'.")
                    .executor(AppointmentManagementFunctionTools.BookAppointmentRequest.class,
                            request -> appointmentManagementFunctionTools.bookAppointment(request))
                    .build(),
                ChatFunction.builder()
                    .name("getMyAppointments")
                    .description("Get user's existing appointments. Call when user asks about their appointments: 'my appointments', 'upcoming visits', 'scheduled appointments', '我的预约', '即将到来的就诊', '已安排的预约'.")
                    .executor(AppointmentManagementFunctionTools.GetMyAppointmentsRequest.class,
                            request -> appointmentManagementFunctionTools.getMyAppointments(request))
                    .build()
            );

            FunctionExecutor functionExecutor = new FunctionExecutor(chatFunctionList);
            aiChatGptQo.setFunctionExecutor(functionExecutor);
            aiChatGptQo.setFunctionCall(ChatCompletionRequest.ChatCompletionRequestFunctionCall.of("auto"));

        } catch (Exception e) {
            log.error("APPOINTMENT_MANAGEMENT_FUNCTION_CALLING_SETUP_ERROR", e);
            throw new RuntimeException("Failed to setup function calling", e);
        }
    }
}
```



## 5. 完整 Function Tools 实现详解

### 5.1 营养分析工具 (NutritionAnalysisFunctionTools)

**完整类结构**:
```java
@Component
@Slf4j
public class NutritionAnalysisFunctionTools extends FunctionToolsBase {
    @Autowired
    private AiClockService aiClockService;

    // 请求数据类定义
    public static class GetUserNutritionDailyRequest {
        @JsonProperty("startDate")
        public String startDate;

        @JsonProperty("endDate")
        public String endDate;
    }

    public static class GetMealTimingInfoRequest {
        // 空请求，仅用于获取时间段信息
    }

    public static class GenerateNutritionRecordRequest {
        @JsonProperty("foods")
        public List<FoodItemRequest> foods;

        @JsonProperty("timing")
        public Integer timing; // 1=早餐, 2=午餐, 3=晚餐, 4=零食

        @JsonProperty("recordDate")
        public String recordDate; // YYYY-MM-DD 格式
    }

    public static class FoodItemRequest {
        @JsonProperty("foodName")
        public String foodName;

        @JsonProperty("calories")
        public Integer calories;

        @JsonProperty("protein")
        public Double protein;

        @JsonProperty("carbs")
        public Double carbs;

        @JsonProperty("fat")
        public Double fat;
    }
}
```

### 5.2 运动追踪工具 (ExerciseTrackingFunctionTools)

**完整类结构**:
```java
@Component
@Slf4j
public class ExerciseTrackingFunctionTools extends FunctionToolsBase {
    @Autowired
    private AiClockService aiClockService;

    // 请求数据类定义
    public static class GetDailyDetailRequest {
        @JsonProperty("startDate")
        public String startDate;

        @JsonProperty("endDate")
        public String endDate;
    }

    public static class GetExerciseDailyRequest {
        @JsonProperty("startDate")
        public String startDate;

        @JsonProperty("endDate")
        public String endDate;
    }

    public static class GetAllExerciseListRequest {
        // 空请求，获取所有运动类型列表
    }

    public static class GenerateExerciseRecordRequest {
        @JsonProperty("taskId")
        public Integer taskId; // 运动类型ID

        @JsonProperty("duration")
        public Integer duration; // 运动时长（秒）

        @JsonProperty("recordDate")
        public String recordDate; // YYYY-MM-DD 格式

        @JsonProperty("distance")
        public Double distance; // 距离（公里）

        @JsonProperty("avgHeartRate")
        public Integer avgHeartRate; // 平均心率

        @JsonProperty("intensityLevel")
        public String intensityLevel; // 强度级别：Low/Medium/High
    }
}
```

### 5.3 健康咨询工具 (HealthConsultationFunctionTools)

**完整类结构**:
```java
@Component
@Slf4j
public class HealthConsultationFunctionTools extends FunctionToolsBase {
    @Autowired
    private AiClockService aiClockService;

    // 请求数据类定义
    public static class GetDailyDetailRequest {
        @JsonProperty("startDate")
        public String startDate;

        @JsonProperty("endDate")
        public String endDate;
    }

    /**
     * 获取用户综合健康数据
     */
    public String getDailyDetail(GetDailyDetailRequest request) {
        String userId = getCurrentUserId();
        log.info("Function call: getDailyDetail with userId: {}, startDate: {}, endDate: {}",
                userId, request.startDate, request.endDate);

        try {
            // 参数验证
            if (userId == null || userId.trim().isEmpty()) {
                return "\"Tool call failed: User ID is not available. Please ensure user is properly authenticated and try again.\"";
            }

            // 智能日期处理 - 使用14天范围确保数据完整性
            LocalDate currentDate = LocalDate.now();
            LocalDate startDate = currentDate.minusDays(13);
            LocalDate endDate = currentDate;

            String finalStartDateStr = startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String finalEndDateStr = endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            // 调用健康数据服务
            DailyDetailVo dailyDetailVo = aiClockService.getDailyDetail(finalStartDateStr, finalEndDateStr, userId);

            if (dailyDetailVo != null && dailyDetailVo.getCode() == 0 && dailyDetailVo.getData() != null) {
                StringBuilder result = new StringBuilder();
                result.append("User's comprehensive health data (").append(finalStartDateStr).append(" to ").append(finalEndDateStr).append("):\\n\\n");

                // 统计总体数据
                int totalExerciseDays = 0;
                int totalDuration = 0;
                double totalDistance = 0.0;
                int totalCalories = 0;
                double avgSleepHours = 0.0;
                int sleepDaysCount = 0;

                for (DailyDetailVo.DailyDetailData dailyData : dailyDetailVo.getData()) {
                    result.append("=== ").append(dailyData.getRecordDate()).append(" ===\\n");

                    // 运动数据
                    result.append("Exercise Goal: ").append(dailyData.getGoalValue()).append(" ").append(dailyData.getGoalUnit()).append("\\n");
                    result.append("Exercise Completed: ").append(dailyData.getCompletedValue()).append(" ").append(dailyData.getGoalUnit()).append("\\n");

                    if (dailyData.getExerciseRecords() != null && !dailyData.getExerciseRecords().isEmpty()) {
                        totalExerciseDays++;
                        result.append("Exercise Activities:\\n");
                        for (DailyDetailVo.ExerciseRecord record : dailyData.getExerciseRecords()) {
                            result.append("  - ").append(record.getTaskName()).append(": ");
                            result.append(record.getDuration()).append(" min");
                            if (record.getDistance() != null && record.getDistance() > 0) {
                                result.append(", ").append(record.getDistance()).append(" km");
                                totalDistance += record.getDistance();
                            }
                            if (record.getAvgHeartRate() != null && record.getAvgHeartRate() > 0) {
                                result.append(", HR: ").append(record.getAvgHeartRate()).append(" bpm");
                            }
                            result.append("\\n");
                            totalDuration += record.getDuration();
                        }
                    }

                    // 营养数据（如果有）
                    if (dailyData.getNutritionData() != null) {
                        result.append("Nutrition: ").append(dailyData.getNutritionData().getTotalCalories()).append(" kcal");
                        result.append(" (P:").append(dailyData.getNutritionData().getProtein()).append("g");
                        result.append(" C:").append(dailyData.getNutritionData().getCarbs()).append("g");
                        result.append(" F:").append(dailyData.getNutritionData().getFat()).append("g)\\n");
                        totalCalories += dailyData.getNutritionData().getTotalCalories();
                    }

                    // 睡眠数据（如果有）
                    if (dailyData.getSleepData() != null) {
                        double sleepHours = dailyData.getSleepData().getDuration() / 60.0;
                        result.append("Sleep: ").append(String.format("%.1f", sleepHours)).append(" hours");
                        result.append(" (Quality: ").append(dailyData.getSleepData().getQuality()).append(")\\n");
                        avgSleepHours += sleepHours;
                        sleepDaysCount++;
                    }

                    // 水分摄入（如果有）
                    if (dailyData.getHydrationData() != null) {
                        result.append("Hydration: ").append(dailyData.getHydrationData().getTotalIntake()).append(" ml\\n");
                    }

                    result.append("\\n");
                }

                // 添加综合分析总结
                result.append("=== COMPREHENSIVE HEALTH ANALYSIS ===\\n");
                result.append("Exercise Summary:\\n");
                result.append("- Active Days: ").append(totalExerciseDays).append(" out of 14 days\\n");
                result.append("- Total Exercise Time: ").append(totalDuration).append(" minutes\\n");
                result.append("- Total Distance: ").append(String.format("%.2f", totalDistance)).append(" km\\n");
                result.append("- Average Daily Exercise: ").append(String.format("%.1f", (double) totalDuration / 14)).append(" minutes\\n\\n");

                if (sleepDaysCount > 0) {
                    result.append("Sleep Summary:\\n");
                    result.append("- Average Sleep: ").append(String.format("%.1f", avgSleepHours / sleepDaysCount)).append(" hours per night\\n");
                    result.append("- Sleep Consistency: ").append(sleepDaysCount).append(" days tracked\\n\\n");
                }

                if (totalCalories > 0) {
                    result.append("Nutrition Summary:\\n");
                    result.append("- Average Daily Calories: ").append(totalCalories / 14).append(" kcal\\n\\n");
                }

                // 健康建议
                result.append("Health Recommendations:\\n");
                if (totalExerciseDays < 5) {
                    result.append("- Increase exercise frequency to at least 5 days per week\\n");
                }
                if (avgSleepHours / Math.max(sleepDaysCount, 1) < 7) {
                    result.append("- Aim for 7-9 hours of sleep per night\\n");
                }
                if (totalDuration / 14 < 30) {
                    result.append("- Target at least 30 minutes of daily physical activity\\n");
                }

                log.info("getDailyDetail success for user: {}, comprehensive data retrieved for {} days", userId, dailyDetailVo.getData().size());
                return "\"" + result.toString().replace("\"", "\\\"") + "\"";
            } else {
                log.warn("getDailyDetail failed, code: {}",
                        dailyDetailVo != null ? dailyDetailVo.getCode() : "null");
                return "\"Tool call failed: Unable to retrieve user's health data. Please inform the user that their health history is currently unavailable and continue with general health consultation.\"";
            }

        } catch (Exception e) {
            log.error("getDailyDetail error", e);
            return "\"Tool call failed: Health data service is currently unavailable. Please inform the user and continue with general health advice.\"";
        }
    }
}
```

### 5.4 健康分析工具 (HealthAnalyticsFunctionTools)

**完整类结构**:
```java
@Component
@Slf4j
public class HealthAnalyticsFunctionTools extends FunctionToolsBase {
    @Autowired
    private AiClockService aiClockService;

    // 请求数据类定义
    public static class GetComprehensiveHealthDataRequest {
        @JsonProperty("startDate")
        public String startDate;

        @JsonProperty("endDate")
        public String endDate;
    }

    public static class GetDetailedExerciseAnalysisRequest {
        @JsonProperty("startDate")
        public String startDate;

        @JsonProperty("endDate")
        public String endDate;
    }

    public static class GetDetailedNutritionAnalysisRequest {
        @JsonProperty("startDate")
        public String startDate;

        @JsonProperty("endDate")
        public String endDate;
    }

    /**
     * 获取综合健康数据分析
     */
    public String getComprehensiveHealthData(GetComprehensiveHealthDataRequest request) {
        String userId = getCurrentUserId();
        log.info("Function call: getComprehensiveHealthData with userId: {}, startDate: {}, endDate: {}",
                userId, request.startDate, request.endDate);

        try {
            if (userId == null || userId.trim().isEmpty()) {
                return "\"Tool call failed: User ID is not available. Please ensure user is properly authenticated and try again.\"";
            }

            // 验证和调整日期范围 - 使用14天范围确保数据完整性
            String[] validatedDates = validateAndAdjustDateRange(request.startDate, request.endDate);
            String finalStartDate = validatedDates[0];
            String finalEndDate = validatedDates[1];

            // 获取综合健康数据
            DailyDetailVo dailyDetailVo = aiClockService.getDailyDetail(finalStartDate, finalEndDate, userId);

            if (dailyDetailVo != null && dailyDetailVo.getCode() == 0 && dailyDetailVo.getData() != null) {
                StringBuilder result = new StringBuilder();
                result.append("Comprehensive Health Data Analysis (").append(finalStartDate).append(" to ").append(finalEndDate).append("):\\n\\n");

                // 统计分析数据
                HealthAnalyticsData analytics = new HealthAnalyticsData();

                for (DailyDetailVo.DailyDetailData dailyData : dailyDetailVo.getData()) {
                    // 运动数据分析
                    if (dailyData.getExerciseRecords() != null && !dailyData.getExerciseRecords().isEmpty()) {
                        analytics.totalExerciseDays++;
                        for (DailyDetailVo.ExerciseRecord record : dailyData.getExerciseRecords()) {
                            analytics.totalExerciseMinutes += record.getDuration();
                            if (record.getDistance() != null) {
                                analytics.totalDistance += record.getDistance();
                            }
                            if (record.getAvgHeartRate() != null) {
                                analytics.heartRateSum += record.getAvgHeartRate();
                                analytics.heartRateCount++;
                            }
                        }
                    }

                    // 营养数据分析
                    if (dailyData.getNutritionData() != null) {
                        analytics.totalCalories += dailyData.getNutritionData().getTotalCalories();
                        analytics.totalProtein += dailyData.getNutritionData().getProtein();
                        analytics.totalCarbs += dailyData.getNutritionData().getCarbs();
                        analytics.totalFat += dailyData.getNutritionData().getFat();
                        analytics.nutritionDays++;
                    }

                    // 睡眠数据分析
                    if (dailyData.getSleepData() != null) {
                        analytics.totalSleepHours += dailyData.getSleepData().getDuration() / 60.0;
                        analytics.sleepDays++;
                        if ("Good".equals(dailyData.getSleepData().getQuality()) ||
                            "Excellent".equals(dailyData.getSleepData().getQuality())) {
                            analytics.goodSleepDays++;
                        }
                    }

                    // 水分数据分析
                    if (dailyData.getHydrationData() != null) {
                        analytics.totalHydration += dailyData.getHydrationData().getTotalIntake();
                        analytics.hydrationDays++;
                        if (dailyData.getHydrationData().getTotalIntake() >= 2000) {
                            analytics.hydrationGoalDays++;
                        }
                    }
                }

                // 生成综合分析报告
                result.append("=== COMPREHENSIVE HEALTH ANALYTICS ===\\n\\n");

                // 运动分析
                result.append("🏃 EXERCISE ANALYTICS:\\n");
                result.append("- Active Days: ").append(analytics.totalExerciseDays).append("/14 days (").append(String.format("%.1f", analytics.totalExerciseDays/14.0*100)).append("%)\\n");
                result.append("- Total Exercise Time: ").append(analytics.totalExerciseMinutes).append(" minutes\\n");
                result.append("- Average Daily Exercise: ").append(String.format("%.1f", analytics.totalExerciseMinutes/14.0)).append(" minutes\\n");
                result.append("- Total Distance: ").append(String.format("%.2f", analytics.totalDistance)).append(" km\\n");
                if (analytics.heartRateCount > 0) {
                    result.append("- Average Heart Rate: ").append(String.format("%.0f", analytics.heartRateSum/analytics.heartRateCount)).append(" bpm\\n");
                }
                result.append("\\n");

                // 营养分析
                if (analytics.nutritionDays > 0) {
                    result.append("🍎 NUTRITION ANALYTICS:\\n");
                    result.append("- Tracking Days: ").append(analytics.nutritionDays).append("/14 days\\n");
                    result.append("- Average Daily Calories: ").append(String.format("%.0f", analytics.totalCalories/analytics.nutritionDays)).append(" kcal\\n");
                    result.append("- Average Macros: P:").append(String.format("%.1f", analytics.totalProtein/analytics.nutritionDays)).append("g ");
                    result.append("C:").append(String.format("%.1f", analytics.totalCarbs/analytics.nutritionDays)).append("g ");
                    result.append("F:").append(String.format("%.1f", analytics.totalFat/analytics.nutritionDays)).append("g\\n");
                    result.append("\\n");
                }

                // 睡眠分析
                if (analytics.sleepDays > 0) {
                    result.append("😴 SLEEP ANALYTICS:\\n");
                    result.append("- Sleep Tracking Days: ").append(analytics.sleepDays).append("/14 days\\n");
                    result.append("- Average Sleep Duration: ").append(String.format("%.1f", analytics.totalSleepHours/analytics.sleepDays)).append(" hours\\n");
                    result.append("- Good Sleep Rate: ").append(String.format("%.1f", analytics.goodSleepDays/(double)analytics.sleepDays*100)).append("%\\n");
                    result.append("\\n");
                }

                // 水分分析
                if (analytics.hydrationDays > 0) {
                    result.append("💧 HYDRATION ANALYTICS:\\n");
                    result.append("- Hydration Tracking Days: ").append(analytics.hydrationDays).append("/14 days\\n");
                    result.append("- Average Daily Intake: ").append(String.format("%.0f", analytics.totalHydration/analytics.hydrationDays)).append(" ml\\n");
                    result.append("- Goal Achievement Rate: ").append(String.format("%.1f", analytics.hydrationGoalDays/(double)analytics.hydrationDays*100)).append("%\\n");
                    result.append("\\n");
                }

                // 健康评分和建议
                result.append("=== HEALTH SCORE & RECOMMENDATIONS ===\\n");
                int healthScore = calculateHealthScore(analytics);
                result.append("Overall Health Score: ").append(healthScore).append("/100\\n\\n");

                result.append("Key Recommendations:\\n");
                if (analytics.totalExerciseDays < 5) {
                    result.append("- 🎯 Increase exercise frequency to 5+ days per week\\n");
                }
                if (analytics.totalExerciseMinutes/14.0 < 30) {
                    result.append("- 🎯 Aim for 30+ minutes of daily physical activity\\n");
                }
                if (analytics.sleepDays > 0 && analytics.totalSleepHours/analytics.sleepDays < 7) {
                    result.append("- 🎯 Prioritize 7-9 hours of quality sleep nightly\\n");
                }
                if (analytics.hydrationDays > 0 && analytics.totalHydration/analytics.hydrationDays < 2000) {
                    result.append("- 🎯 Increase daily water intake to 2000+ ml\\n");
                }

                log.info("getComprehensiveHealthData success for user: {}, comprehensive analysis completed", userId);
                return "\"" + result.toString().replace("\"", "\\\"") + "\"";
            } else {
                log.warn("getComprehensiveHealthData failed, code: {}",
                        dailyDetailVo != null ? dailyDetailVo.getCode() : "null");
                return "\"Tool call failed: Unable to retrieve comprehensive health data. Please inform the user that their health analytics are currently unavailable and continue with general health consultation.\"";
            }

        } catch (Exception e) {
            log.error("getComprehensiveHealthData error", e);
            return "\"Tool call failed: Health analytics service is currently unavailable. Please inform the user and continue with general health advice.\"";
        }
    }

    /**
     * 计算健康评分
     */
    private int calculateHealthScore(HealthAnalyticsData analytics) {
        int score = 0;

        // 运动评分 (30分)
        double exerciseRate = analytics.totalExerciseDays / 14.0;
        double avgExercise = analytics.totalExerciseMinutes / 14.0;
        if (exerciseRate >= 0.5 && avgExercise >= 30) score += 30;
        else if (exerciseRate >= 0.3 && avgExercise >= 20) score += 20;
        else if (exerciseRate >= 0.2) score += 10;

        // 睡眠评分 (25分)
        if (analytics.sleepDays > 0) {
            double avgSleep = analytics.totalSleepHours / analytics.sleepDays;
            double goodSleepRate = analytics.goodSleepDays / (double) analytics.sleepDays;
            if (avgSleep >= 7 && avgSleep <= 9 && goodSleepRate >= 0.7) score += 25;
            else if (avgSleep >= 6 && goodSleepRate >= 0.5) score += 15;
            else if (avgSleep >= 5) score += 5;
        }

        // 营养评分 (25分)
        if (analytics.nutritionDays > 0) {
            double avgCalories = analytics.totalCalories / analytics.nutritionDays;
            if (avgCalories >= 1500 && avgCalories <= 2500) score += 25;
            else if (avgCalories >= 1200 && avgCalories <= 3000) score += 15;
            else score += 5;
        }

        // 水分评分 (20分)
        if (analytics.hydrationDays > 0) {
            double hydrationRate = analytics.hydrationGoalDays / (double) analytics.hydrationDays;
            if (hydrationRate >= 0.8) score += 20;
            else if (hydrationRate >= 0.5) score += 10;
            else if (hydrationRate >= 0.3) score += 5;
        }

        return Math.min(score, 100);
    }

    /**
     * 验证和调整日期范围
     */
    private String[] validateAndAdjustDateRange(String startDate, String endDate) {
        LocalDate currentDate = LocalDate.now();
        LocalDate start = currentDate.minusDays(13); // 14天前
        LocalDate end = currentDate; // 今天

        return new String[]{
            start.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
            end.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
        };
    }

    /**
     * 健康分析数据结构
     */
    private static class HealthAnalyticsData {
        int totalExerciseDays = 0;
        int totalExerciseMinutes = 0;
        double totalDistance = 0.0;
        int heartRateSum = 0;
        int heartRateCount = 0;

        int nutritionDays = 0;
        double totalCalories = 0.0;
        double totalProtein = 0.0;
        double totalCarbs = 0.0;
        double totalFat = 0.0;

        int sleepDays = 0;
        double totalSleepHours = 0.0;
        int goodSleepDays = 0;

        int hydrationDays = 0;
        double totalHydration = 0.0;
        int hydrationGoalDays = 0;
    }
}
```

### 5.5 预约管理工具 (AppointmentManagementFunctionTools)

**完整类结构**:
```java
@Component
@Slf4j
public class AppointmentManagementFunctionTools extends FunctionToolsBase {
    @Autowired
    private HsdService hsdService;

    // 请求数据类定义
    public static class GetAvailableSlotsRequest {
        @JsonProperty("apptFromTimestamp")
        @JsonPropertyDescription("预约开始时间戳，格式：yyyy-MM-dd HH:mm:ss")
        public String apptFromTimestamp;

        @JsonProperty("apptToTimestamp")
        @JsonPropertyDescription("预约结束时间戳，格式：yyyy-MM-dd HH:mm:ss")
        public String apptToTimestamp;
    }

    public static class BookAppointmentRequest {
        @JsonProperty("scheduleId")
        @JsonPropertyDescription("预约时段ID")
        public Integer scheduleId;

        @JsonProperty("appointmentDate")
        @JsonPropertyDescription("预约日期，格式：yyyy-MM-dd")
        public String appointmentDate;

        @JsonProperty("appointmentTime")
        @JsonPropertyDescription("预约时间，格式：HH:mm")
        public String appointmentTime;
    }

    public static class GetMyAppointmentsRequest {
        @JsonProperty("startDate")
        @JsonPropertyDescription("查询开始日期，格式：yyyy-MM-dd")
        public String startDate;

        @JsonProperty("endDate")
        @JsonPropertyDescription("查询结束日期，格式：yyyy-MM-dd")
        public String endDate;
    }

    /**
     * 获取可用预约时段
     */
    public String getAvailableSlots(GetAvailableSlotsRequest request) {
        String userId = getCurrentUserId();
        log.info("Function call: getAvailableSlots with userId: {}, fromTime: {}, toTime: {}",
                userId, request.apptFromTimestamp, request.apptToTimestamp);

        try {
            if (userId == null || userId.trim().isEmpty()) {
                return "\"Tool call failed: User ID is not available. Please ensure user is properly authenticated and try again.\"";
            }

            // 时间参数处理
            String apptFromTimestamp = request.apptFromTimestamp;
            String apptToTimestamp = request.apptToTimestamp;

            if (apptFromTimestamp == null || apptFromTimestamp.trim().isEmpty()) {
                apptFromTimestamp = DateUtil.format(DateUtil.date(), "yyyy-MM-dd HH:mm:ss");
            }
            if (apptToTimestamp == null || apptToTimestamp.trim().isEmpty()) {
                apptToTimestamp = DateUtil.format(DateUtil.offsetDay(DateUtil.date(), 7), "yyyy-MM-dd HH:mm:ss");
            }

            // 构建请求参数
            SlotQo slotQo = new SlotQo();
            slotQo.setUserId(userId);
            slotQo.setApptFromTime(apptFromTimestamp);
            slotQo.setApptToTime(apptToTimestamp);

            // 调用 HSD 服务获取时段
            SlotResultVo slotResultVo = hsdService.getSlots(slotQo);

            if (slotResultVo != null && slotResultVo.getCode() == 0 && slotResultVo.getData() != null) {
                StringBuilder result = new StringBuilder();
                result.append("Available appointment slots (").append(apptFromTimestamp).append(" to ").append(apptToTimestamp).append("):\\n\\n");

                List<SlotWrapperVo> slots = slotResultVo.getData();
                if (slots.isEmpty()) {
                    result.append("No available appointment slots found for the specified time range.\\n");
                    result.append("Please try a different date range or contact the clinic directly.\\n");
                } else {
                    for (SlotWrapperVo slot : slots) {
                        result.append("📅 Date: ").append(slot.getAppointmentDate()).append("\\n");
                        result.append("🕐 Time: ").append(slot.getAppointmentTime()).append("\\n");
                        result.append("👨‍⚕️ Doctor: ").append(slot.getDoctorName()).append("\\n");
                        result.append("🏥 Department: ").append(slot.getDepartmentName()).append("\\n");
                        result.append("📍 Location: ").append(slot.getClinicLocation()).append("\\n");
                        result.append("💰 Fee: $").append(slot.getConsultationFee()).append("\\n");
                        result.append("🆔 Slot ID: ").append(slot.getScheduleId()).append("\\n");
                        result.append("\\n");
                    }
                }

                log.info("getAvailableSlots success for user: {}, found {} slots", userId, slots.size());
                return "\"" + result.toString().replace("\"", "\\\"") + "\"";
            } else {
                log.warn("getAvailableSlots failed, code: {}",
                        slotResultVo != null ? slotResultVo.getCode() : "null");
                return "\"Tool call failed: Unable to retrieve available appointment slots. Please inform the user that appointment booking is currently unavailable and suggest contacting the clinic directly.\"";
            }

        } catch (Exception e) {
            log.error("getAvailableSlots error", e);
            return "\"Tool call failed: Appointment service is currently unavailable. Please inform the user and suggest contacting the clinic directly.\"";
        }
    }

    /**
     * 预约确认
     */
    public String bookAppointment(BookAppointmentRequest request) {
        String userId = getCurrentUserId();
        log.info("Function call: bookAppointment with userId: {}, scheduleId: {}, date: {}, time: {}",
                userId, request.scheduleId, request.appointmentDate, request.appointmentTime);

        try {
            if (userId == null || userId.trim().isEmpty()) {
                return "\"Tool call failed: User ID is not available. Please ensure user is properly authenticated and try again.\"";
            }

            // 参数验证
            if (request.scheduleId == null || request.scheduleId <= 0) {
                return "\"Tool call failed: Valid schedule ID is required for booking appointment.\"";
            }

            // 构建预约请求
            CheckAppointmentQo checkAppointmentQo = new CheckAppointmentQo();
            checkAppointmentQo.setUserId(userId);
            checkAppointmentQo.setSlotIdList(Arrays.asList(request.scheduleId));

            // 调用 HSD 服务进行预约
            CheckAppointmentResultVo checkResult = hsdService.checkAppointment(checkAppointmentQo);

            if (checkResult != null && checkResult.getCode() == 0 && checkResult.getData() != null) {
                Map<String, String> resultData = checkResult.getData();
                String appointmentId = resultData.get(String.valueOf(request.scheduleId));

                if (appointmentId != null && !appointmentId.trim().isEmpty()) {
                    StringBuilder result = new StringBuilder();
                    result.append("✅ Appointment successfully booked!\\n\\n");
                    result.append("📋 Appointment Details:\\n");
                    result.append("🆔 Appointment ID: ").append(appointmentId).append("\\n");
                    result.append("📅 Date: ").append(request.appointmentDate).append("\\n");
                    result.append("🕐 Time: ").append(request.appointmentTime).append("\\n");
                    result.append("🔢 Schedule ID: ").append(request.scheduleId).append("\\n\\n");
                    result.append("📝 Important Notes:\\n");
                    result.append("- Please arrive 15 minutes before your appointment time\\n");
                    result.append("- Bring a valid ID and insurance card\\n");
                    result.append("- Contact the clinic if you need to reschedule\\n");
                    result.append("- You will receive a confirmation message shortly\\n");

                    log.info("bookAppointment success for user: {}, appointmentId: {}", userId, appointmentId);
                    return "\"" + result.toString().replace("\"", "\\\"") + "\"";
                } else {
                    log.warn("bookAppointment failed, no appointment ID returned");
                    return "\"Tool call failed: Appointment booking failed. The selected time slot may no longer be available. Please try selecting a different time slot.\"";
                }
            } else {
                log.warn("bookAppointment failed, code: {}",
                        checkResult != null ? checkResult.getCode() : "null");
                return "\"Tool call failed: Unable to complete appointment booking. Please inform the user that booking is currently unavailable and suggest contacting the clinic directly.\"";
            }

        } catch (Exception e) {
            log.error("bookAppointment error", e);
            return "\"Tool call failed: Appointment booking service is currently unavailable. Please inform the user and suggest contacting the clinic directly.\"";
        }
    }

    /**
     * 获取用户预约记录
     */
    public String getMyAppointments(GetMyAppointmentsRequest request) {
        String userId = getCurrentUserId();
        log.info("Function call: getMyAppointments with userId: {}, startDate: {}, endDate: {}",
                userId, request.startDate, request.endDate);

        try {
            if (userId == null || userId.trim().isEmpty()) {
                return "\"Tool call failed: User ID is not available. Please ensure user is properly authenticated and try again.\"";
            }

            // 日期参数处理
            String startDate = request.startDate;
            String endDate = request.endDate;

            if (startDate == null || startDate.trim().isEmpty()) {
                startDate = DateUtil.format(DateUtil.date(), "yyyy-MM-dd");
            }
            if (endDate == null || endDate.trim().isEmpty()) {
                endDate = DateUtil.format(DateUtil.offsetDay(DateUtil.date(), 30), "yyyy-MM-dd");
            }

            // 构建查询请求
            MyAppointmentsQo appointmentsQo = new MyAppointmentsQo();
            appointmentsQo.setUserId(userId);
            appointmentsQo.setStartDate(startDate);
            appointmentsQo.setEndDate(endDate);

            // 调用 HSD 服务获取预约记录
            MyAppointmentsVo appointmentsVo = hsdService.getMyAppointments(appointmentsQo);

            if (appointmentsVo != null && appointmentsVo.getCode() == 0 && appointmentsVo.getData() != null) {
                StringBuilder result = new StringBuilder();
                result.append("Your appointments (").append(startDate).append(" to ").append(endDate).append("):\\n\\n");

                List<AppointmentInfo> appointments = appointmentsVo.getData();
                if (appointments.isEmpty()) {
                    result.append("No appointments found for the specified date range.\\n");
                    result.append("You can book new appointments using the appointment booking feature.\\n");
                } else {
                    for (AppointmentInfo appointment : appointments) {
                        result.append("📋 Appointment #").append(appointment.getAppointmentId()).append("\\n");
                        result.append("📅 Date: ").append(appointment.getAppointmentDate()).append("\\n");
                        result.append("🕐 Time: ").append(appointment.getAppointmentTime()).append("\\n");
                        result.append("👨‍⚕️ Doctor: ").append(appointment.getDoctorName()).append("\\n");
                        result.append("🏥 Department: ").append(appointment.getDepartmentName()).append("\\n");
                        result.append("📍 Location: ").append(appointment.getClinicLocation()).append("\\n");
                        result.append("📊 Status: ").append(appointment.getStatus()).append("\\n");
                        result.append("\\n");
                    }
                }

                log.info("getMyAppointments success for user: {}, found {} appointments", userId, appointments.size());
                return "\"" + result.toString().replace("\"", "\\\"") + "\"";
            } else {
                log.warn("getMyAppointments failed, code: {}",
                        appointmentsVo != null ? appointmentsVo.getCode() : "null");
                return "\"Tool call failed: Unable to retrieve appointment history. Please inform the user that appointment history is currently unavailable.\"";
            }

        } catch (Exception e) {
            log.error("getMyAppointments error", e);
            return "\"Tool call failed: Appointment history service is currently unavailable. Please inform the user and suggest contacting the clinic directly.\"";
        }
    }
}
```

### 5.6 水分追踪工具 (HydrationTrackingFunctionTools)

**完整类结构**:
```java
@Component
@Slf4j
public class HydrationTrackingFunctionTools extends FunctionToolsBase {
    @Autowired
    private AiClockService aiClockService;

    // 请求数据类定义
    public static class GetDailyDetailRequest {
        @JsonProperty("startDate")
        public String startDate;

        @JsonProperty("endDate")
        public String endDate;
    }

    public static class RecordHydrationRequest {
        @JsonProperty("recordDate")
        @JsonPropertyDescription("Record date, format: yyyy-MM-dd, e.g.: 2024-01-01 (required)")
        public String recordDate;

        @JsonProperty("completedValue")
        @JsonPropertyDescription("Water intake in glasses (required, must be positive and not exceed 20 glasses)")
        public Integer completedValue;
    }

    public static class ConvertDateRequest {
        @JsonProperty("dateFormula")
        @JsonPropertyDescription("Standardized date formula to convert, e.g.: 'TODAY', 'TODAY-1', 'TODAY+2', 'WEEK-1' (required)")
        public String dateFormula;
    }

    /**
     * 获取用户饮水数据
     */
    public String getDailyDetail(GetDailyDetailRequest request) {
        String userId = getCurrentUserId();
        log.info("Function call: getDailyDetail with userId: {}, startDate: {}, endDate: {}",
                userId, request.startDate, request.endDate);

        try {
            // 参数验证
            if (userId == null || userId.trim().isEmpty()) {
                return "\"Tool call failed: User ID is not available. Please ensure user is properly authenticated and try again.\"";
            }

            // 如果没有指定日期，默认查询今天
            String startDate = request.startDate;
            String endDate = request.endDate;

            if (startDate == null || startDate.trim().isEmpty()) {
                startDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }
            if (endDate == null || endDate.trim().isEmpty()) {
                endDate = startDate;
            }

            DailyDetailVo dailyDetailVo = aiClockService.getDailyDetail(startDate, endDate, userId);

            if (dailyDetailVo != null && dailyDetailVo.getCode() == 0 && dailyDetailVo.getData() != null) {
                StringBuilder result = new StringBuilder();
                result.append("User's hydration data (").append(startDate).append(" to ").append(endDate).append("):\\n\\n");

                for (String date : dailyDetailVo.getData().keySet()) {
                    result.append("Date: ").append(date).append("\\n");
                    List<DailyDetailVo.TaskCategory> categories = dailyDetailVo.getData().get(date);

                    boolean foundHydrationData = false;
                    for (DailyDetailVo.TaskCategory category : categories) {
                        if (category.getGoals() != null && !category.getGoals().isEmpty()) {
                            for (DailyDetailVo.TaskGoal goal : category.getGoals()) {
                                // 过滤饮水相关的目标
                                if ("drinkWater".equals(goal.getGoalCode()) ||
                                    (goal.getTitle() != null && goal.getTitle().toLowerCase().contains("water")) ||
                                    (goal.getTitle() != null && goal.getTitle().toLowerCase().contains("hydration")) ||
                                    (goal.getTitle() != null && goal.getTitle().toLowerCase().contains("饮水"))) {

                                    foundHydrationData = true;
                                    result.append("Hydration Goal: ").append(goal.getTitle()).append("\\n");
                                    result.append("Target: ").append(goal.getGoal() != null ? goal.getGoal().getGoalValue() : "N/A");
                                    result.append(" ").append(goal.getGoal() != null ? goal.getGoal().getGoalUnit() : "glasses").append("\\n");
                                    result.append("Completed: ").append(goal.getCompletedValue()).append(" glasses\\n");

                                    // 计算完成率
                                    if (goal.getGoal() != null && goal.getGoal().getGoalValue() > 0) {
                                        double completionRate = (double) goal.getCompletedValue() / goal.getGoal().getGoalValue() * 100;
                                        result.append("Completion Rate: ").append(String.format("%.1f", completionRate)).append("%\\n");
                                    }
                                    result.append("\\n");
                                }
                            }
                        }
                    }

                    if (!foundHydrationData) {
                        result.append("No hydration data found for this day\\n\\n");
                    }
                }

                log.info("getDailyDetail success for user: {}", userId);
                return "\"" + result.toString().replace("\"", "\\\"") + "\"";
            } else {
                log.warn("getDailyDetail failed, code: {}",
                        dailyDetailVo != null ? dailyDetailVo.getCode() : "null");
                return "\"Tool call failed: Unable to retrieve user's daily task details. Please inform the user that their task data is currently unavailable and continue with general hydration guidance.\"";
            }
        } catch (Exception e) {
            log.error("getDailyDetail error", e);
            return "\"Tool call failed: Daily task service is currently unavailable. Please inform the user that their task data cannot be accessed at the moment and continue with general hydration guidance.\"";
        }
    }

    /**
     * 记录饮水数据
     */
    public String recordHydration(RecordHydrationRequest request) {
        log.info("Function call: recordHydration with recordDate: {}, completedValue: {}",
                request.recordDate, request.completedValue);

        try {
            // 使用统一校验助手进行参数校验
            ValidationHelper.ValidationResult validation = ValidationHelper.validateHydrationRecord(
                request.recordDate, request.completedValue
            );

            if (!validation.isValid()) {
                // 如果有智能询问提示，优先返回友好的中文提示
                if (validation.getSmartQuestionPrompt() != null) {
                    log.info("recordHydration validation failed, returning smart question prompt");
                    return "\"" + ValidationHelper.escapeJsonString(validation.getSmartQuestionPrompt()) + "\"";
                }
                // 否则返回标准错误信息
                return "\"" + validation.getErrorMessage() + "\"";
            }

            // 获取用户当前的饮水数据和目标信息
            String userId = getCurrentUserId();
            int currentCompletedValue = 0;
            int goalValue = 8; // 默认目标
            String goalUnit = "Glasses";

            if (userId != null && !userId.trim().isEmpty()) {
                try {
                    DailyDetailVo dailyDetailVo = aiClockService.getDailyDetail(request.recordDate, request.recordDate, userId);
                    if (dailyDetailVo != null && dailyDetailVo.getCode() == 0 && dailyDetailVo.getData() != null) {
                        for (String date : dailyDetailVo.getData().keySet()) {
                            List<DailyDetailVo.TaskCategory> categories = dailyDetailVo.getData().get(date);
                            for (DailyDetailVo.TaskCategory category : categories) {
                                if (category.getGoals() != null) {
                                    for (DailyDetailVo.TaskGoal goal : category.getGoals()) {
                                        if ("drinkWater".equals(goal.getGoalCode())) {
                                            currentCompletedValue = goal.getCompletedValue();
                                            goalValue = goal.getGoal().getGoalValue();
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("Failed to get current hydration data, using defaults", e);
                }
            }

            // 生成饮水记录卡片数据
            StringBuilder jsonResult = new StringBuilder();
            jsonResult.append("{\\n");
            jsonResult.append("  \\\"status\\\": 1,\\n");
            jsonResult.append("  \\\"recordDate\\\": \\\"").append(request.recordDate).append("\\\",\\n");
            jsonResult.append("  \\\"completedValue\\\": ").append(request.completedValue).append(",\\n");
            jsonResult.append("  \\\"completedUnit\\\": \\\"Glasses\\\",\\n");
            jsonResult.append("  \\\"goalValue\\\": ").append(goalValue).append(",\\n");
            jsonResult.append("  \\\"goalUnit\\\": \\\"").append(goalUnit).append("\\\"\\n");
            jsonResult.append("}");

            log.info("recordHydration success, generated enhanced record for {} glasses on {}",
                    request.completedValue, request.recordDate);
            return jsonResult.toString();

        } catch (Exception e) {
            log.error("recordHydration error", e);
            return "\"Tool call failed: Unable to generate hydration record parameters. Please inform the user that the water intake recording function is currently unavailable and continue with hydration guidance only.\"";
        }
    }

    /**
     * 转换相对日期公式为标准日期格式
     */
    public String convertNaturalDateToStandard(ConvertDateRequest request) {
        log.info("Function call: convertNaturalDateToStandard with dateFormula: '{}'", request.dateFormula);

        try {
            if (request.dateFormula == null || request.dateFormula.trim().isEmpty()) {
                return "\"Tool call failed: Date formula is required. Please provide the standardized date formula and try again.\"";
            }

            String formula = request.dateFormula.trim().toLowerCase();
            LocalDate currentDate = LocalDate.now();
            LocalDate targetDate = null;

            // 解析相对日期公式
            if ("today".equals(formula)) {
                targetDate = currentDate;
            } else if (formula.startsWith("today")) {
                String[] parts = formula.split("(?=[+-])");
                if (parts.length == 2) {
                    int offset = Integer.parseInt(parts[1]);
                    targetDate = currentDate.plusDays(offset);
                }
            } else if (formula.startsWith("week")) {
                String[] parts = formula.split("(?=[+-])");
                if (parts.length == 2) {
                    int offset = Integer.parseInt(parts[1]);
                    targetDate = currentDate.plusWeeks(offset);
                }
            }

            if (targetDate == null) {
                return "\"Tool call failed: Unsupported date formula. Please use formats like 'TODAY', 'TODAY-1', 'TODAY+2', 'WEEK-1'.\"";
            }

            // 验证日期合理性：不允许记录未来日期
            if (targetDate.isAfter(currentDate)) {
                return "\"Tool call failed: Cannot record hydration for future dates. Please use a date that is today or in the past.\"";
            }

            // 验证日期合理性：不允许记录超过30天前的日期
            if (targetDate.isBefore(currentDate.minusDays(30))) {
                return "\"Tool call failed: Cannot record hydration for dates older than 30 days. Please use a more recent date.\"";
            }

            String resultDate = targetDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            log.info("convertNaturalDateToStandard success: '{}' -> '{}'", request.dateFormula, resultDate);

            return "\"" + resultDate + "\"";

        } catch (Exception e) {
            log.error("convertNaturalDateToStandard error", e);
            return "\"Tool call failed: Unable to convert date formula. Please provide a valid date formula like 'TODAY', 'TODAY-1', etc.\"";
        }
    }
}
```

### 5.7 睡眠追踪工具 (SleepTrackingFunctionTools)

**完整类结构**:
```java
@Component
@Slf4j
public class SleepTrackingFunctionTools extends FunctionToolsBase {
    @Autowired
    private AiClockService aiClockService;

    // 请求数据类定义
    public static class GetDailyDetailRequest {
        @JsonProperty("startDate")
        public String startDate;

        @JsonProperty("endDate")
        public String endDate;
    }

    public static class GenerateSleepRecordRequest {
        @JsonProperty("recordDate")
        @JsonPropertyDescription("Record date, format: yyyy-MM-dd, e.g.: 2024-01-01 (required)")
        public String recordDate;

        @JsonProperty("completedValue")
        @JsonPropertyDescription("Sleep duration in minutes (required, must be positive and not exceed 1440 minutes/24 hours)")
        public Integer completedValue;
    }

    public static class ConvertDateRequest {
        @JsonProperty("dateFormula")
        @JsonPropertyDescription("Standardized date formula to convert, e.g.: 'TODAY', 'TODAY-1', 'TODAY+2', 'WEEK-1' (required)")
        public String dateFormula;
    }

    /**
     * 获取用户睡眠数据
     */
    public String getDailyDetail(GetDailyDetailRequest request) {
        String userId = getCurrentUserId();
        log.info("Function call: getDailyDetail with userId: {}, startDate: {}, endDate: {}",
                userId, request.startDate, request.endDate);

        try {
            // 参数验证
            if (userId == null || userId.trim().isEmpty()) {
                return "\"Tool call failed: User ID is not available. Please ensure user is properly authenticated and try again.\"";
            }

            // 如果没有指定日期，默认查询今天
            String startDate = request.startDate;
            String endDate = request.endDate;

            if (startDate == null || startDate.trim().isEmpty()) {
                startDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }
            if (endDate == null || endDate.trim().isEmpty()) {
                endDate = startDate;
            }

            DailyDetailVo dailyDetailVo = aiClockService.getDailyDetail(startDate, endDate, userId);

            if (dailyDetailVo != null && dailyDetailVo.getCode() == 0 && dailyDetailVo.getData() != null) {
                StringBuilder result = new StringBuilder();
                result.append("User's sleep data (").append(startDate).append(" to ").append(endDate).append("):\\n\\n");

                for (String date : dailyDetailVo.getData().keySet()) {
                    result.append("Date: ").append(date).append("\\n");
                    List<DailyDetailVo.TaskCategory> categories = dailyDetailVo.getData().get(date);

                    boolean foundSleepData = false;
                    for (DailyDetailVo.TaskCategory category : categories) {
                        if (category.getGoals() != null && !category.getGoals().isEmpty()) {
                            for (DailyDetailVo.TaskGoal goal : category.getGoals()) {
                                // 过滤睡眠相关的目标
                                if ("sleep".equals(goal.getGoalCode()) ||
                                    (goal.getTitle() != null && goal.getTitle().toLowerCase().contains("sleep")) ||
                                    (goal.getTitle() != null && goal.getTitle().toLowerCase().contains("睡眠"))) {

                                    foundSleepData = true;
                                    result.append("Sleep Goal: ").append(goal.getTitle()).append("\\n");
                                    result.append("Target: ").append(goal.getGoal() != null ? goal.getGoal().getGoalValue() : "N/A");
                                    result.append(" ").append(goal.getGoal() != null ? goal.getGoal().getGoalUnit() : "minutes").append("\\n");
                                    result.append("Completed: ").append(goal.getCompletedValue()).append(" minutes\\n");

                                    // 计算完成率
                                    if (goal.getGoal() != null && goal.getGoal().getGoalValue() > 0) {
                                        double completionRate = (double) goal.getCompletedValue() / goal.getGoal().getGoalValue() * 100;
                                        result.append("Completion Rate: ").append(String.format("%.1f", completionRate)).append("%\\n");
                                    }
                                    result.append("\\n");
                                }
                            }
                        }
                    }

                    if (!foundSleepData) {
                        result.append("No sleep data found for this day\\n\\n");
                    }
                }

                log.info("getDailyDetail success for user: {}", userId);
                return "\"" + result.toString().replace("\"", "\\\"") + "\"";
            } else {
                log.warn("getDailyDetail failed, code: {}",
                        dailyDetailVo != null ? dailyDetailVo.getCode() : "null");
                return "\"Tool call failed: Unable to retrieve user's daily task details. Please inform the user that their sleep history is currently unavailable and continue with general sleep analysis.\"";
            }
        } catch (Exception e) {
            log.error("getDailyDetail error", e);
            return "\"Tool call failed: Daily task details service is currently unavailable. Please inform the user that their sleep history cannot be accessed at the moment and continue with general sleep analysis.\"";
        }
    }

    /**
     * 生成睡眠记录
     */
    public String generateSleepRecord(GenerateSleepRecordRequest request) {
        log.info("Function call: generateSleepRecord with recordDate: {}, completedValue: {}",
                request.recordDate, request.completedValue);

        try {
            // 使用统一校验助手进行参数校验
            ValidationHelper.ValidationResult validation = ValidationHelper.validateSleepRecord(
                request.recordDate, request.completedValue
            );

            if (!validation.isValid()) {
                // 如果有智能询问提示，优先返回友好的中文提示
                if (validation.getSmartQuestionPrompt() != null) {
                    log.info("generateSleepRecord validation failed, returning smart question prompt");
                    return "\"" + ValidationHelper.escapeJsonString(validation.getSmartQuestionPrompt()) + "\"";
                }
                // 否则返回标准错误信息
                return "\"" + validation.getErrorMessage() + "\"";
            }

            // 生成扩展的睡眠记录卡片数据结构
            StringBuilder jsonResult = new StringBuilder();
            jsonResult.append("{\\n");
            jsonResult.append("  \\\"status\\\": 1,\\n");
            jsonResult.append("  \\\"recordDate\\\": \\\"").append(request.recordDate).append("\\\",\\n");
            jsonResult.append("  \\\"completedValue\\\": ").append(request.completedValue).append(",\\n");
            jsonResult.append("  \\\"completedUnit\\\": \\\"MIN\\\"\\n");
            jsonResult.append("}");

            log.info("generateSleepRecord success, generated record for {} minutes", request.completedValue);
            return jsonResult.toString();

        } catch (Exception e) {
            log.error("generateSleepRecord error", e);
            return "\"Tool call failed: Unable to generate sleep record parameters. Please inform the user that the sleep recording function is currently unavailable and continue with sleep analysis only.\"";
        }
    }

    /**
     * 转换相对日期公式为标准日期格式
     */
    public String convertNaturalDateToStandard(ConvertDateRequest request) {
        log.info("Function call: convertNaturalDateToStandard with dateFormula: '{}'", request.dateFormula);

        try {
            if (request.dateFormula == null || request.dateFormula.trim().isEmpty()) {
                return "\"Tool call failed: Date formula is required. Please provide the standardized date formula and try again.\"";
            }

            String formula = request.dateFormula.trim().toLowerCase();
            LocalDate currentDate = LocalDate.now();
            LocalDate targetDate = null;

            // 解析相对日期公式
            if ("today".equals(formula)) {
                targetDate = currentDate;
            } else if (formula.startsWith("today")) {
                String[] parts = formula.split("(?=[+-])");
                if (parts.length == 2) {
                    int offset = Integer.parseInt(parts[1]);
                    targetDate = currentDate.plusDays(offset);
                }
            } else if (formula.startsWith("week")) {
                String[] parts = formula.split("(?=[+-])");
                if (parts.length == 2) {
                    int offset = Integer.parseInt(parts[1]);
                    targetDate = currentDate.plusWeeks(offset);
                }
            }

            if (targetDate == null) {
                return "\"Tool call failed: Unsupported date formula. Please use formats like 'TODAY', 'TODAY-1', 'TODAY+2', 'WEEK-1'.\"";
            }

            // 验证日期合理性：不允许记录未来日期
            if (targetDate.isAfter(currentDate)) {
                return "\"Tool call failed: Cannot record sleep for future dates. Please use a date that is today or in the past.\"";
            }

            // 验证日期合理性：不允许记录超过1周前的日期
            if (targetDate.isBefore(currentDate.minusWeeks(1))) {
                return "\"Tool call failed: Cannot record sleep for dates older than 1 week. Please use a more recent date.\"";
            }

            String resultDate = targetDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            log.info("convertNaturalDateToStandard success: '{}' -> '{}'", request.dateFormula, resultDate);

            return "\"" + resultDate + "\"";

        } catch (Exception e) {
            log.error("convertNaturalDateToStandard error", e);
            return "\"Tool call failed: Unable to convert date formula. Please provide a valid date formula like 'TODAY', 'TODAY-1', etc.\"";
        }
    }
}
```

## 6. 动态提示词拼接机制详解

### 6.1 buildEnhancedSystemPrompts 方法实现

**营养分析场景的提示词拼接**:
```java
private String buildEnhancedSystemPrompts(SceneHandlerQo handlerQo, String originalPrompts) {
    StringBuilder enhancedPrompts = new StringBuilder();

    // 1. 基础系统提示词
    enhancedPrompts.append(originalPrompts).append("\n\n");

    // 2. 添加当前时间信息
    enhancedPrompts.append("Current system time: ").append(DateUtil.now()).append("\n");

    // 3. 添加用户信息
    if (handlerQo.getUserInfo() != null) {
        enhancedPrompts.append("User ID: ").append(handlerQo.getUserInfo().getThirdUserId()).append("\n");
    }

    // 4. 添加营养分析特定指导
    enhancedPrompts.append("\nNutrition Analysis Guidelines:\n");
    enhancedPrompts.append("1. Always call getUserNutritionDaily first for historical data\n");
    enhancedPrompts.append("2. Use generateNutritionRecord for meal logging requests\n");
    enhancedPrompts.append("3. Provide detailed nutritional analysis with specific values\n");
    enhancedPrompts.append("4. Include health recommendations based on data\n");

    return enhancedPrompts.toString();
}
```

**健康分析场景的复杂提示词拼接**:
```java
private String buildEnhancedSystemPrompts(SceneHandlerQo handlerQo, String originalPrompts) {
    // 1. 获取用户性别信息用于BMR估算
    String userGenderInfo = getUserGenderInfo(handlerQo);

    // 2. 生成精确到时分秒的当前时间信息
    LocalDateTime now = LocalDateTime.now();
    String currentDateTime = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    String currentDate = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    String currentTime = now.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
    String currentDayOfWeek = now.getDayOfWeek().toString();
    String currentYear = String.valueOf(now.getYear());

    // 3. 构建系统时间信息
    String systemTimeInfo = String.format(
        "=== SYSTEM TIME INFORMATION ===\n" +
        "Current DateTime: %s\n" +
        "Current Date: %s\n" +
        "Current Time: %s\n" +
        "Day of Week: %s\n" +
        "Year: %s\n\n",
        currentDateTime, currentDate, currentTime, currentDayOfWeek, currentYear
    );

    // 4. 获取敏感话题处理提示词
    String sensitiveTopicTemplate = PromptsTemplateTool.getPromptsTemplate(MongoPromptsTemplateEnum.SENSITIVE_TOPIC_PROMPT);
    String sensitiveTopicInfo = "=== SAFETY GUIDELINES ===\n" + sensitiveTopicTemplate + "\n\n";

    // 5. Markdown格式规则
    String markdownFormatRules = "=== RESPONSE FORMAT RULES ===\n" +
        "1. Use proper Markdown formatting for better readability\n" +
        "2. Use bullet points (•) for lists\n" +
        "3. Use **bold** for important information\n" +
        "4. Use headers (##) for section organization\n" +
        "5. Use tables when presenting structured data\n\n";

    // 6. 如果MongoDB中有完整的提示词，直接使用并添加增强信息
    if (StrUtil.isNotBlank(originalPrompts)) {
        return systemTimeInfo + sensitiveTopicInfo + markdownFormatRules + originalPrompts;
    }

    // 7. 如果没有配置，则构建基础提示词
    StringBuilder fallbackPrompts = new StringBuilder();
    fallbackPrompts.append(systemTimeInfo);
    fallbackPrompts.append(sensitiveTopicInfo);
    fallbackPrompts.append(markdownFormatRules);
    fallbackPrompts.append("You are a professional health analytics specialist...\n");

    return fallbackPrompts.toString();
}
```

**健康咨询场景的多模板拼接**:
```java
private String buildHealthConsultationSystemPrompts(SceneHandlerQo handlerQo) {
    StringBuilder systemPrompts = new StringBuilder();

    // 1. 获取基础系统提示词
    String basePrompts = PromptsTemplateTool.getPromptsTemplate(MongoPromptsTemplateEnum.HEALTH_CONSULTATION_SYSTEM_PROMPTS);
    systemPrompts.append(basePrompts).append("\n\n");

    // 2. 添加 Function Calling 指导
    String functionCallingPrompts = PromptsTemplateTool.getPromptsTemplate(MongoPromptsTemplateEnum.HEALTH_CONSULTATION_FUNCTION_CALLING_PROMPTS);
    systemPrompts.append(functionCallingPrompts).append("\n\n");

    // 3. 添加用户案例数据
    String userCaseData = PromptsTemplateTool.getPromptsTemplate(MongoPromptsTemplateEnum.HEALTH_CONSULTATION_USER_CASE_DATA);
    systemPrompts.append("User Case Data Examples:\n").append(userCaseData).append("\n\n");

    // 4. 添加敏感话题处理
    String sensitiveTopicTemplate = PromptsTemplateTool.getPromptsTemplate(MongoPromptsTemplateEnum.SENSITIVE_TOPIC_PROMPT);
    systemPrompts.append("Safety Guidelines:\n").append(sensitiveTopicTemplate).append("\n\n");

    // 5. 添加当前时间和用户信息
    systemPrompts.append("Current system time: ").append(DateUtil.now()).append("\n");
    if (handlerQo.getUserInfo() != null) {
        systemPrompts.append("Current user ID: ").append(handlerQo.getUserInfo().getThirdUserId()).append("\n");
    }

    // 6. 添加健康咨询特定指导
    systemPrompts.append("\nHealth Consultation Guidelines:\n");
    systemPrompts.append("1. ALWAYS call getDailyDetail first for comprehensive health data\n");
    systemPrompts.append("2. Provide evidence-based health advice\n");
    systemPrompts.append("3. Recommend professional medical consultation for serious symptoms\n");
    systemPrompts.append("4. Maintain a caring and professional tone\n");
    systemPrompts.append("5. Include specific health metrics and trends in analysis\n");

    return systemPrompts.toString();
}
```

### 6.2 预约管理场景的特殊提示词处理

**多阶段提示词构建**:
```java
// 第一阶段：获取号源列表
String slotListPrompt = PromptsTemplateTool.getPromptsTemplate(MongoPromptsTemplateEnum.APP_SCENE_APT_SYS);

// 第二阶段：推荐提示词
String recommendPrompt = PromptsUtil.mappingPrompts(
    PromptsTemplateTool.getPromptsTemplate(MongoPromptsTemplateEnum.APP_SCENE_APT_RECOMMEND),
    Map.of("currentTime", DateUtil.now(), "slotListStr", slotListStr)
);

// 第三阶段：格式化提示词
String formatSlotPrompt = PromptsUtil.mappingPrompts(
    PromptsTemplateTool.getPromptsTemplate(MongoPromptsTemplateEnum.APP_SCENE_APT_SLOT_FORMAT),
    Map.of("aiGcResult", aiGcResult)
);
```

## 7. 外部服务详细说明

### 7.1 AI Clock 服务 (AiClockService)

**服务接口定义**:
```java
public interface AiClockService {
    // 获取综合健康数据
    DailyDetailVo getDailyDetail(String startDate, String endDate, String userId);

    // 获取营养数据
    NutritionDailyVo getNutritionDaily(String startDate, String endDate, String userId);

    // 获取运动数据
    ExerciseDailyVo getExerciseDaily(String startDate, String endDate, String userId);

    // 获取睡眠数据
    SleepDailyVo getSleepDaily(String startDate, String endDate, String userId);

    // 获取水分数据
    HydrationDailyVo getHydrationDaily(String startDate, String endDate, String userId);

    // 获取运动类型列表
    ExerciseListVo getAllExerciseList();
}
```

**服务实现和环境适配**:
```java
@Service
public class AiClockServiceImpl implements AiClockService {
    @Autowired
    private AiClockFeignClient aiClockFeignClient;

    @Override
    public DailyDetailVo getDailyDetail(String startDate, String endDate, String userId) {
        if (SpringBizUtil.isLocal()) {
            // 本地环境使用 Mock 数据
            return AiClockMockDataUtil.generateDailyDetailMockData();
        } else {
            // 生产环境调用真实接口
            return aiClockFeignClient.getDailyDetail(startDate, endDate, userId);
        }
    }

    @Override
    public NutritionDailyVo getNutritionDaily(String startDate, String endDate, String userId) {
        if (SpringBizUtil.isLocal()) {
            return AiClockMockDataUtil.generateNutritionMockData();
        } else {
            return aiClockFeignClient.getNutritionDaily(startDate, endDate, userId);
        }
    }

    // 其他方法类似实现...
}
```

**Feign 客户端配置**:
```java
@FeignClient(name = "ai-clock", url = "${ai.clock.service.url}")
public interface AiClockFeignClient {
    @GetMapping("/api/health/daily-detail")
    DailyDetailVo getDailyDetail(@RequestParam("startDate") String startDate,
                                @RequestParam("endDate") String endDate,
                                @RequestParam("userId") String userId);

    @GetMapping("/api/nutrition/daily")
    NutritionDailyVo getNutritionDaily(@RequestParam("startDate") String startDate,
                                      @RequestParam("endDate") String endDate,
                                      @RequestParam("userId") String userId);

    @GetMapping("/api/exercise/daily")
    ExerciseDailyVo getExerciseDaily(@RequestParam("startDate") String startDate,
                                    @RequestParam("endDate") String endDate,
                                    @RequestParam("userId") String userId);

    @GetMapping("/api/sleep/daily")
    SleepDailyVo getSleepDaily(@RequestParam("startDate") String startDate,
                              @RequestParam("endDate") String endDate,
                              @RequestParam("userId") String userId);

    @GetMapping("/api/hydration/daily")
    HydrationDailyVo getHydrationDaily(@RequestParam("startDate") String startDate,
                                      @RequestParam("endDate") String endDate,
                                      @RequestParam("userId") String userId);

    @GetMapping("/api/exercise/list")
    ExerciseListVo getAllExerciseList();
}
```

### 7.2 健康咨询服务 (HealthAgentFeignClient)

**服务接口定义**:
```java
@FeignClient(name = "health-agent", url = "${health.agent.url}")
public interface HealthAgentFeignClient {
    @PostMapping("/health_consult")
    HealthConsultVo healthConsult(@RequestBody HealthConsultQo qo);

    @PostMapping("/hospital_reminder")
    HospitalReminderVo hospital_reminder(@RequestBody HospitalReminderQo qo);
}
```

**请求和响应数据结构**:
```java
// 健康咨询请求
public class HealthConsultQo {
    private String user_id;           // 用户ID
    private String conversation_id;   // 会话ID
    private String content;          // 咨询内容
}

// 健康咨询响应
public class HealthConsultVo {
    private Integer code;
    private String message;
    private HealthConsultData data;

    public static class HealthConsultData {
        private String answer;        // AI 回答
        private String confidence;    // 置信度
        private List<String> suggestions; // 建议列表
    }
}
```

**降级处理机制**:
```java
private AiChatGptVo handleFallback(AiInterface<AiChatGptQo, AiChatGptVo> aiInterface, SceneHandlerQo handlerQo) {
    try {
        log.info("HEALTH_CONSULTATION_FALLBACK_TO_EXTERNAL_SERVICE");

        // 构建外部服务请求
        HealthConsultQo healthConsultQo = new HealthConsultQo();
        healthConsultQo.setUser_id(handlerQo.getUserInfo().getThirdUserId());
        healthConsultQo.setConversation_id(handlerQo.getConversationDto().getUniqueSign());
        healthConsultQo.setContent(handlerQo.getPrompts());

        // 构建自定义数据
        ImMessageCustomDataDto imMessageCustomDataDto = new ImMessageCustomDataDto();
        imMessageCustomDataDto.setType(ImMessageCustomDataTypeEnum.SCENE.getCode());
        imMessageCustomDataDto.setSceneCode(DirectlySceneConstant.CODE_APP_HEALTH_CONSULTATION_ASSISTANT);

        AiChatGptVo aiChatGptVo = new AiChatGptVo();
        String strCustomData = JSON.toJSONString(imMessageCustomDataDto, JSONWriter.Feature.WriteNulls);
        aiChatGptVo.setData(strCustomData);

        // 调用外部健康咨询服务
        HealthConsultVo healthConsultVo = healthConsultFeignClient.healthConsult(healthConsultQo);
        if (healthConsultVo != null && healthConsultVo.getData() != null) {
            aiChatGptVo.setAiGc(healthConsultVo.getData().getAnswer());
            log.info("Get answer from Health consult agent: {}", healthConsultVo.getData().getAnswer());
        } else {
            log.warn("Failed to get answer from Health consult agent for inputs: {}", handlerQo.getPrompts());
            aiChatGptVo.setAiGc("I'm here to help with your health questions. Could you please provide more specific details about your health concerns?");
        }

        return aiChatGptVo;

    } catch (Exception fallbackError) {
        log.error("Fallback also failed", fallbackError);

        // 最终降级：返回错误信息
        AiChatGptVo errorResult = new AiChatGptVo();
        errorResult.setAiGc("Sorry, the health consultation service is temporarily unavailable. Please try again later.");
        return errorResult;
    }
}
```

### 7.3 HSD 预约服务 (HsdService)

**服务接口定义**:
```java
public interface HsdService {
    // 获取可用时段
    SlotResultVo getSlots(SlotQo qo);

    // 检查预约可用性
    CheckAppointmentResultVo checkAppointment(CheckAppointmentQo qo);

    // 获取用户预约记录
    MyAppointmentsVo getMyAppointments(MyAppointmentsQo qo);
}
```

**数据结构定义**:
```java
// 时段查询请求
public class SlotQo {
    private String userId;        // 用户ID
    private String apptFromTime;  // 开始时间
    private String apptToTime;    // 结束时间
}

// 时段查询响应
public class SlotResultVo {
    private Integer code;
    private String message;
    private List<SlotWrapperVo> data;
}

public class SlotWrapperVo {
    private Integer scheduleId;      // 时段ID
    private String appointmentDate;  // 预约日期
    private String appointmentTime;  // 预约时间
    private String doctorName;       // 医生姓名
    private String departmentName;   // 科室名称
    private String clinicLocation;   // 诊所位置
    private Double consultationFee;  // 咨询费用
}

// 预约检查请求
public class CheckAppointmentQo {
    private String userId;           // 用户ID
    private List<Integer> slotIdList; // 时段ID列表
}

// 预约检查响应
public class CheckAppointmentResultVo {
    private Integer code;
    private String message;
    private Map<String, String> data; // scheduleId -> appointmentId 映射
}
```

### 7.4 Mock 数据机制

**环境判断**:
```java
public class SpringBizUtil {
    public static boolean isLocal() {
        String[] activeProfiles = SpringUtil.getActiveProfiles();
        return Arrays.asList(activeProfiles).contains("local");
    }
}
```

**Mock 数据生成**:
```java
public class AiClockMockDataUtil {
    public static DailyDetailVo generateDailyDetailMockData() {
        DailyDetailVo mockData = new DailyDetailVo();
        mockData.setCode(0);
        mockData.setMessage("Success");

        List<DailyDetailVo.DailyDetailData> dataList = new ArrayList<>();
        // 生成14天的模拟数据
        for (int i = 0; i < 14; i++) {
            DailyDetailVo.DailyDetailData dailyData = new DailyDetailVo.DailyDetailData();
            dailyData.setRecordDate(LocalDate.now().minusDays(i).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            dailyData.setGoalValue(30);
            dailyData.setGoalUnit("Min");
            dailyData.setCompletedValue(25 + (int)(Math.random() * 20));

            // 添加运动记录
            List<DailyDetailVo.ExerciseRecord> exerciseRecords = new ArrayList<>();
            if (Math.random() > 0.3) { // 70% 概率有运动记录
                DailyDetailVo.ExerciseRecord record = new DailyDetailVo.ExerciseRecord();
                record.setTaskName("Running");
                record.setDuration(30 + (int)(Math.random() * 30));
                record.setDistance(3.0 + Math.random() * 5.0);
                record.setAvgHeartRate(120 + (int)(Math.random() * 40));
                exerciseRecords.add(record);
            }
            dailyData.setExerciseRecords(exerciseRecords);

            dataList.add(dailyData);
        }

        mockData.setData(dataList);
        return mockData;
    }

    // 其他 Mock 数据生成方法...
}
```





**核心方法实现**:

**getUserNutritionDaily** - 获取用户营养数据:
```java
public String getUserNutritionDaily(GetUserNutritionDailyRequest request) {
    String userId = getCurrentUserId();
    log.info("Function call: getUserNutritionDaily with userId: {}, startDate: {}, endDate: {}",
            userId, request.startDate, request.endDate);

    try {
        // 参数验证
        if (userId == null || userId.trim().isEmpty()) {
            return "\"Tool call failed: User ID is not available. Please ensure user is properly authenticated and try again.\"";
        }

        // 日期处理
        String startDate = request.startDate;
        String endDate = request.endDate;

        if (startDate == null || startDate.trim().isEmpty()) {
            startDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        if (endDate == null || endDate.trim().isEmpty()) {
            endDate = startDate;
        }

        // 调用营养数据服务
        NutritionDailyVo nutritionDailyVo = aiClockService.getNutritionDaily(startDate, endDate, userId);

        if (nutritionDailyVo != null && nutritionDailyVo.getCode() == 0 && nutritionDailyVo.getData() != null) {
            StringBuilder result = new StringBuilder();
            result.append("User's nutrition data (").append(startDate).append(" to ").append(endDate).append("):\\n\\n");

            for (NutritionDailyVo.NutritionDailyData dailyData : nutritionDailyVo.getData()) {
                result.append("Date: ").append(dailyData.getRecordDate()).append("\\n");
                result.append("Total Calories: ").append(dailyData.getTotalCalories()).append(" kcal\\n");
                result.append("Protein: ").append(dailyData.getTotalProtein()).append("g\\n");
                result.append("Carbs: ").append(dailyData.getTotalCarbs()).append("g\\n");
                result.append("Fat: ").append(dailyData.getTotalFat()).append("g\\n");
                result.append("Fiber: ").append(dailyData.getTotalFiber()).append("g\\n");

                if (dailyData.getMeals() != null && !dailyData.getMeals().isEmpty()) {
                    result.append("Meals:\\n");
                    for (NutritionDailyVo.MealData meal : dailyData.getMeals()) {
                        result.append("  - ").append(meal.getTimingName()).append(": ");
                        result.append(meal.getFoodName()).append(" (").append(meal.getCalories()).append(" kcal)\\n");
                    }
                }
                result.append("\\n");
            }

            log.info("getUserNutritionDaily success for user: {}, data retrieved for {} days", userId, nutritionDailyVo.getData().size());
            return "\"" + result.toString().replace("\"", "\\\"") + "\"";
        } else {
            log.warn("getUserNutritionDaily failed, code: {}",
                    nutritionDailyVo != null ? nutritionDailyVo.getCode() : "null");
            return "\"Tool call failed: Unable to retrieve user's nutrition data. Please inform the user that their nutrition history is currently unavailable and continue with general nutrition consultation.\"";
        }

    } catch (Exception e) {
        log.error("getUserNutritionDaily error", e);
        return "\"Tool call failed: Nutrition data service is currently unavailable. Please inform the user and continue with general nutrition advice.\"";
    }
}
```

**generateNutritionRecord** - 生成营养记录卡片:
```java
public String generateNutritionRecord(GenerateNutritionRecordRequest request) {
    log.info("Function call: generateNutritionRecord with foods: {}, timing: {}, recordDate: {}",
            request.foods != null ? request.foods.size() : 0, request.timing, request.recordDate);

    try {
        // 参数验证
        if (request.foods == null || request.foods.isEmpty()) {
            return "\"Tool call failed: Foods list is required. Please provide at least one food item with name and calories.\"";
        }

        if (request.timing == null || request.timing < 1 || request.timing > 4) {
            return "\"Tool call failed: Invalid timing value. Please use: 1=Breakfast, 2=Lunch, 3=Dinner, 4=Snack\"";
        }

        // 日期验证
        String recordDate = request.recordDate;
        if (recordDate == null || recordDate.trim().isEmpty()) {
            recordDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }

        // 验证日期格式
        try {
            LocalDate.parse(recordDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } catch (Exception e) {
            return "\"Tool call failed: Invalid date format. Please use YYYY-MM-DD format (e.g., 2024-01-15)\"";
        }

        // 生成扩展的营养记录卡片数据结构
        StringBuilder jsonResult = new StringBuilder();
        jsonResult.append("{\\n");

        // 获取用餐时间名称
        String timingName = getTimingName(request.timing);

        // 计算总热量
        int totalCalories = 0;
        for (FoodItemRequest food : request.foods) {
            if (food.calories != null) {
                totalCalories += food.calories;
            }
        }

        // 确定主要食物名称（第一个食物或组合名称）
        String showFoodName = request.foods.get(0).foodName;
        if (request.foods.size() > 1) {
            showFoodName = request.foods.get(0).foodName + " and others";
        }

        // 构建卡片数据
        jsonResult.append("  \\\"recordDate\\\": \\\"").append(recordDate).append("\\\",\\n");
        jsonResult.append("  \\\"timing\\\": ").append(request.timing).append(",\\n");
        jsonResult.append("  \\\"timingName\\\": \\\"").append(timingName).append("\\\",\\n");
        jsonResult.append("  \\\"showFoodName\\\": \\\"").append(showFoodName).append("\\\",\\n");
        jsonResult.append("  \\\"totalCalories\\\": ").append(totalCalories).append(",\\n");
        jsonResult.append("  \\\"foods\\\": [\\n");

        for (int i = 0; i < request.foods.size(); i++) {
            FoodItemRequest food = request.foods.get(i);
            jsonResult.append("    {\\n");
            jsonResult.append("      \\\"foodName\\\": \\\"").append(food.foodName).append("\\\",\\n");
            jsonResult.append("      \\\"calories\\\": ").append(food.calories != null ? food.calories : 0).append(",\\n");
            jsonResult.append("      \\\"protein\\\": ").append(food.protein != null ? food.protein : 0).append(",\\n");
            jsonResult.append("      \\\"carbs\\\": ").append(food.carbs != null ? food.carbs : 0).append(",\\n");
            jsonResult.append("      \\\"fat\\\": ").append(food.fat != null ? food.fat : 0).append("\\n");
            jsonResult.append("    }");
            if (i < request.foods.size() - 1) {
                jsonResult.append(",");
            }
            jsonResult.append("\\n");
        }

        jsonResult.append("  ]\\n");
        jsonResult.append("}");

        log.info("generateNutritionRecord success, generated record for {} foods with total {} calories",
                request.foods.size(), totalCalories);
        return jsonResult.toString();

    } catch (Exception e) {
        log.error("generateNutritionRecord error", e);
        return "\"Tool call failed: Unable to generate nutrition record parameters. Please inform the user that the nutrition recording function is currently unavailable and continue with nutrition analysis only.\"";
    }
}
```

## 8. AI 接口调用和消息处理机制

### 8.1 AiInterface 核心方法

**simpleChat** - 简单对话:
```java
R simpleChat(E qo);
```
- 用于不需要 Function Calling 的简单对话
- 直接调用 GPT API 获取响应
- 适用于默认聊天场景

**streamChat** - 流式对话:
```java
R streamChat(E qo, AiCallbackService callback);
```
- 支持实时流式响应
- 通过回调函数处理流式数据
- 提供更好的用户体验

**handleWithToolChainingStream** - 工具链路流式调用:
```java
R handleWithToolChainingStream(E qo, AiCallbackService callback);
```
- 分阶段处理：先静默执行工具调用，再进行流式文本生成
- 确保工具调用稳定可靠
- 避免用户看到错误消息片段

### 8.2 Function Calling 机制

**设置 Function Calling**:
```java
// 1. 创建 Function 列表
List<ChatFunction> chatFunctionList = Arrays.asList(
    ChatFunction.builder()
        .name("functionName")
        .description("Function description with clear usage guidelines")
        .executor(RequestClass.class, request -> functionTools.methodName(request))
        .build()
);

// 2. 创建 Function Executor
FunctionExecutor functionExecutor = new FunctionExecutor(chatFunctionList);

// 3. 设置到 AI 请求中
aiChatGptQo.setFunctionExecutor(functionExecutor);
aiChatGptQo.setFunctionCall(ChatCompletionRequest.ChatCompletionRequestFunctionCall.of("auto"));
```

**Function 描述规范**:
- 使用 `MANDATORY` 标记必须调用的工具
- 明确列出触发条件和关键词
- 包含中英文示例
- 说明参数要求和格式

### 8.3 消息处理和卡片机制

**重要说明**: 以下所有"打卡"功能指的是**生成前端展示用的卡片数据结构**，而不是真正调用业务接口进行数据存储。这些工具的作用是为前端提供结构化的展示数据。

**IM 消息类型**:
```java
// 文本消息
ImBotMessageQo textMessage = ImBotMessageQo.of(
    handlerQo.getDoChatHandlerBaseDto(),
    messageText,
    null,
    BizMessageTypeEnum.TEXT.getCode(),
    ImMessageStatusEnum.FINISHED.getCode()
);

// 卡片消息
ImBotMessageQo cardMessage = ImBotMessageQo.of(
    handlerQo.getDoChatHandlerBaseDto(),
    null,
    cardData,
    BizMessageTypeEnum.CARD.getCode(),
    ImMessageStatusEnum.FINISHED.getCode()
);
```

### 8.4 完整卡片数据结构定义

以下是各个场景生成的完整卡片数据结构（content字段内容）：

#### 8.4.1 营养记录卡片数据结构
```json
{
  "recordDate": "2024-01-15",
  "timing": 1,
  "timingName": "Breakfast",
  "showFoodName": "Oatmeal and others",
  "totalCalories": 350,
  "foods": [
    {
      "foodName": "Oatmeal",
      "calories": 300,
      "protein": 10,
      "carbs": 54,
      "fat": 6
    },
    {
      "foodName": "Banana",
      "calories": 50,
      "protein": 1,
      "carbs": 12,
      "fat": 0
    }
  ]
}
```

#### 8.4.2 运动记录卡片数据结构
```json
{
  "taskId": 1001,
  "duration": 1800,
  "recordDate": "2024-01-15",
  "distance": 5.0,
  "avgHeartRate": 140,
  "intensityLevel": "Medium",
  "trackRoute": true,
  "goal": {
    "goalValue": 30,
    "goalUnit": "Min"
  }
}
```

#### 8.4.3 水分记录卡片数据结构
```json
{
  "status": 1,
  "recordDate": "2024-01-15",
  "completedValue": 6,
  "completedUnit": "Glasses",
  "goalValue": 8,
  "goalUnit": "Glasses"
}
```

#### 8.4.4 睡眠记录卡片数据结构
```json
{
  "status": 1,
  "recordDate": "2024-01-15",
  "completedValue": 480,
  "completedUnit": "MIN"
}
```

#### 8.4.5 预约确认卡片数据结构
```json
{
  "appointmentId": "APT123456",
  "scheduleId": 1001,
  "appointmentDate": "2024-01-20",
  "appointmentTime": "14:00",
  "doctorName": "Dr. Smith",
  "departmentName": "General Medicine",
  "clinicLocation": "Main Clinic",
  "consultationFee": 50.0,
  "status": "confirmed"
}
```

### 8.4 卡片生成工具说明

**重要概念澄清**:
- **generateNutritionRecord**: 生成营养记录卡片数据结构，用于前端展示营养打卡信息
- **generateExerciseRecord**: 生成运动记录卡片数据结构，用于前端展示运动打卡信息
- **recordHydration**: 生成水分记录卡片数据结构，用于前端展示饮水打卡信息
- **generateSleepRecord**: 生成睡眠记录卡片数据结构，用于前端展示睡眠打卡信息
- **bookAppointment**: 生成预约确认卡片数据结构，用于前端展示预约确认信息

**这些工具的作用**:
1. **不进行实际数据存储**: 这些工具不会调用业务接口将数据保存到数据库
2. **生成展示数据**: 仅生成结构化的JSON数据供前端展示使用
3. **用户体验优化**: 为用户提供直观的卡片式展示界面
4. **数据格式标准化**: 确保前端接收到统一格式的卡片数据

**Python重构注意事项**:
- 这些方法只需要生成JSON格式的卡片数据结构
- 不需要集成任何数据存储或业务逻辑接口
- 重点关注数据结构的准确性和完整性
- 确保生成的JSON符合前端展示要求

### 8.5 错误处理和降级机制

**分层错误处理**:
```java
try {
    // 主要业务逻辑
    return mainProcess(aiInterface, handlerQo);
} catch (Exception e) {
    log.error("SCENE_ERROR", e);
    return handleFallback(aiInterface, handlerQo);
}
```

**降级策略**:
1. **营养分析降级**: Function Calling 失败 → 简单 AI 对话 → 错误提示
2. **健康咨询降级**: Function Calling 失败 → 外部健康咨询服务 → 错误提示
3. **运动追踪降级**: Function Calling 失败 → 简单 AI 对话 → 错误提示

## 9. 完整提示词模板详解

### 9.1 基础系统提示词

**默认系统提示词** (SYSTEM_TEMPLATE):
```
The Current time is {currentTime}. Now you are a professional and kind medical assistant. Now your task is answering the user question. Note: 1. Your answer should be brief 2. For what you do not know please return do not know, do not construct false data
```

**敏感话题提示词** (SENSITIVE_TOPIC_PROMPT):
```
Important Safety Guidelines:
1. For serious medical symptoms (chest pain, difficulty breathing, severe injuries, mental health crises), immediately recommend seeking emergency medical care
2. Never provide specific medical diagnoses - always suggest consulting healthcare professionals
3. Avoid giving specific medication dosages or treatment recommendations
4. For mental health concerns, provide supportive guidance and recommend professional help
5. Do not provide advice on illegal substances or dangerous activities
6. Maintain professional boundaries and redirect inappropriate requests
```

### 9.2 营养分析场景完整提示词

**系统提示词** (NUTRITION_ANALYSIS_SYSTEM_PROMPTS):
```
You are a professional nutrition analyst and dietary consultant with expertise in food science, nutritional biochemistry, and personalized nutrition planning. Your role is to provide comprehensive nutrition analysis, meal planning guidance, and dietary recommendations based on scientific evidence.

## Core Responsibilities:
1. **Nutritional Analysis**: Analyze food items, meals, and dietary patterns for macro and micronutrient content
2. **Meal Planning**: Provide balanced meal suggestions following evidence-based nutritional guidelines
3. **Dietary Guidance**: Offer personalized nutrition advice based on individual health goals and dietary preferences
4. **Food Tracking**: Help users log and monitor their daily nutritional intake
5. **Health Optimization**: Recommend dietary adjustments to support overall health and wellness

## Nutritional Analysis Framework:
- **Macronutrients**: Protein, Carbohydrates, Fats (with detailed breakdown)
- **Micronutrients**: Vitamins, Minerals, Antioxidants
- **Caloric Content**: Total calories and caloric density
- **Dietary Fiber**: Soluble and insoluble fiber content
- **Nutritional Quality**: Overall nutritional value and health impact

## Key Guidelines:
1. **Evidence-Based**: All recommendations must be based on current nutritional science
2. **Personalized**: Consider individual needs, preferences, and health conditions
3. **Balanced Approach**: Promote balanced, sustainable eating patterns
4. **Food Safety**: Always consider food safety and preparation guidelines
5. **Cultural Sensitivity**: Respect diverse dietary traditions and preferences

## Function Calling Protocol:
- **ALWAYS** call getUserNutritionDaily first for any nutrition-related query
- Use generateNutritionRecord for meal logging requests
- Provide specific nutritional values and recommendations
- Include practical meal planning suggestions

## Response Format:
- Provide clear, actionable nutrition advice
- Include specific nutritional values when available
- Offer practical meal planning suggestions
- Explain the health benefits of recommended foods
- Use encouraging and supportive language

Remember: You are here to educate and guide users toward healthier eating habits while respecting their individual circumstances and preferences.
```

**意图识别提示词** (NUTRITION_ANALYSIS_INTENTION_DICT_TEMPLATE):
```
NUTRITION_ANALYSIS - Purpose: Food component recognition and nutrition balance evaluation
Key Indicators: Food images, meal descriptions, nutrition questions, calorie counting, dietary balance assessment, 2:1:1 ratio evaluation
Trigger Words: food, meal, nutrition, calories, protein, carbs, vegetables, diet balance, eating, recipe
```

### 9.3 运动追踪场景完整提示词

**系统提示词** (EXERCISE_TRACKING_SYSTEM_PROMPTS):
```
You are a certified fitness trainer and exercise physiologist with extensive experience in personalized fitness planning, exercise prescription, and performance optimization. Your expertise covers strength training, cardiovascular fitness, flexibility, sports performance, and injury prevention.

## Core Responsibilities:
1. **Exercise Planning**: Design personalized workout routines based on individual fitness levels and goals
2. **Performance Tracking**: Monitor and analyze exercise progress, intensity, and outcomes
3. **Technique Guidance**: Provide proper exercise form and technique instructions
4. **Goal Setting**: Help establish realistic and achievable fitness objectives
5. **Motivation Support**: Encourage consistent exercise habits and lifestyle changes

## Exercise Analysis Framework:
- **Exercise Types**: Cardiovascular, Strength Training, Flexibility, Sports-Specific
- **Intensity Levels**: Low, Moderate, High, Very High (based on heart rate zones)
- **Duration & Frequency**: Optimal timing and frequency for different exercise types
- **Progressive Overload**: Systematic progression to improve fitness levels
- **Recovery & Rest**: Importance of rest periods and active recovery

## Fitness Assessment Areas:
1. **Cardiovascular Endurance**: Heart rate response, VO2 max estimation
2. **Muscular Strength**: Resistance training progress and strength gains
3. **Flexibility & Mobility**: Range of motion and joint health
4. **Body Composition**: Muscle mass, body fat percentage considerations
5. **Functional Movement**: Daily activity performance and movement quality

## Key Guidelines:
1. **Safety First**: Always prioritize proper form and injury prevention
2. **Progressive Training**: Gradual increase in intensity and volume
3. **Individual Adaptation**: Customize recommendations based on fitness level
4. **Balanced Programming**: Include all components of fitness
5. **Sustainable Habits**: Focus on long-term lifestyle changes

## Function Calling Protocol:
- **ALWAYS** call getDailyDetail first for exercise history and performance analysis
- Use generateExerciseRecord for workout logging
- Provide specific exercise recommendations with proper intensity guidelines
- Include safety considerations and proper form instructions

## Response Format:
- Provide clear, actionable exercise guidance
- Include specific workout parameters (sets, reps, duration, intensity)
- Explain the benefits of recommended exercises
- Offer modifications for different fitness levels
- Use motivational and encouraging language

Remember: Your goal is to help users develop sustainable exercise habits that improve their overall health, fitness, and quality of life while minimizing injury risk.
```

**意图识别提示词** (EXERCISE_TRACKING_INTENTION_DICT_TEMPLATE):
```
EXERCISE_TRACKING - Purpose: Exercise activity recording and fitness guidance
Key Indicators: Workout descriptions, exercise logging, fitness goals, training plans, sports activities
Trigger Words: exercise, workout, training, gym, running, swimming, fitness, sports, sets, reps, weights
```



### 9.4 水分追踪场景完整提示词

**系统提示词** (HYDRATION_TRACKING_SYSTEM_PROMPTS):
```
You are a hydration specialist and wellness consultant with expertise in fluid balance, electrolyte management, and optimal hydration strategies. Your knowledge encompasses the physiological importance of proper hydration for overall health and performance.

## Core Responsibilities:
1. **Hydration Assessment**: Evaluate daily water intake and hydration status
2. **Intake Planning**: Recommend optimal fluid intake based on individual needs
3. **Hydration Education**: Explain the importance of proper hydration for health
4. **Tracking Support**: Help users monitor and improve their hydration habits
5. **Performance Optimization**: Guide hydration strategies for exercise and daily activities

## Hydration Science Framework:
- **Daily Requirements**: Individual fluid needs based on body weight, activity, climate
- **Fluid Sources**: Water, beverages, food-derived moisture
- **Electrolyte Balance**: Sodium, potassium, magnesium considerations
- **Timing Strategies**: Optimal hydration timing throughout the day
- **Performance Impact**: Hydration effects on physical and cognitive performance

## Assessment Factors:
1. **Individual Needs**: Body weight, age, gender, activity level
2. **Environmental Factors**: Temperature, humidity, altitude
3. **Health Conditions**: Medical conditions affecting fluid needs
4. **Exercise Requirements**: Pre, during, and post-exercise hydration
5. **Lifestyle Factors**: Caffeine intake, alcohol consumption, medications

## Key Guidelines:
1. **Personalized Approach**: Tailor recommendations to individual circumstances
2. **Quality Focus**: Emphasize water quality and healthy beverage choices
3. **Practical Strategies**: Provide actionable hydration tips and reminders
4. **Balance Awareness**: Avoid both dehydration and overhydration
5. **Habit Formation**: Support sustainable hydration practices

## Function Calling Protocol:
- **ALWAYS** call getDailyDetail first for hydration history and patterns
- Use generateHydrationRecord for water intake logging
- Provide specific hydration recommendations with timing guidance
- Include practical tips for maintaining optimal hydration

## Response Format:
- Provide clear hydration guidance with specific intake recommendations
- Include timing strategies for optimal hydration throughout the day
- Explain the health benefits of proper hydration
- Offer practical tips for increasing water intake
- Use encouraging and supportive language

Remember: Proper hydration is fundamental to health, affecting everything from physical performance to cognitive function and overall well-being.
```

### 9.5 睡眠追踪场景完整提示词

**系统提示词** (SLEEP_TRACKING_SYSTEM_PROMPTS):
```
You are a sleep specialist and circadian rhythm expert with comprehensive knowledge of sleep science, sleep hygiene, and sleep optimization strategies. Your expertise covers sleep physiology, sleep disorders, and evidence-based approaches to improving sleep quality.

## Core Responsibilities:
1. **Sleep Assessment**: Evaluate sleep patterns, quality, and duration
2. **Sleep Optimization**: Provide strategies to improve sleep quality and consistency
3. **Circadian Health**: Guide users on maintaining healthy sleep-wake cycles
4. **Sleep Hygiene**: Educate on environmental and behavioral factors affecting sleep
5. **Performance Impact**: Explain how sleep affects overall health and daily performance

## Sleep Science Framework:
- **Sleep Stages**: REM, Deep Sleep, Light Sleep cycles and their importance
- **Circadian Rhythms**: Natural sleep-wake cycles and their regulation
- **Sleep Duration**: Age-appropriate sleep requirements and individual variations
- **Sleep Quality**: Factors affecting restorative sleep and sleep efficiency
- **Sleep Environment**: Optimal conditions for quality sleep

## Assessment Areas:
1. **Sleep Duration**: Total sleep time and consistency across nights
2. **Sleep Quality**: Subjective sleep quality and sleep efficiency
3. **Sleep Timing**: Bedtime, wake time, and circadian alignment
4. **Sleep Environment**: Room temperature, lighting, noise, comfort
5. **Sleep Habits**: Pre-sleep routines, screen time, caffeine intake

## Key Guidelines:
1. **Evidence-Based**: All recommendations based on current sleep research
2. **Individual Variation**: Recognize that sleep needs vary among individuals
3. **Holistic Approach**: Consider lifestyle factors affecting sleep
4. **Gradual Changes**: Recommend sustainable, gradual sleep improvements
5. **Professional Referral**: Identify when professional sleep evaluation is needed

## Function Calling Protocol:
- **ALWAYS** call getDailyDetail first for sleep history and patterns
- Use generateSleepRecord for sleep logging and tracking
- Provide specific sleep recommendations with timing guidance
- Include sleep hygiene tips and environmental optimization

## Response Format:
- Provide comprehensive sleep analysis with specific insights
- Include actionable recommendations for sleep improvement
- Explain the science behind sleep recommendations
- Offer both immediate and long-term sleep optimization strategies
- Use supportive and educational language

## Sleep Hygiene Essentials:
- Consistent sleep schedule (same bedtime and wake time daily)
- Optimal sleep environment (cool, dark, quiet)
- Pre-sleep routine (relaxing activities 1 hour before bed)
- Limit screen exposure 2 hours before bedtime
- Avoid caffeine 6 hours before sleep
- Regular exercise (but not close to bedtime)
- Manage stress and anxiety through relaxation techniques

Remember: Quality sleep is essential for physical health, mental well-being, cognitive performance, and overall quality of life.
```

### 9.6 健康分析场景完整提示词

**系统提示词** (HEALTH_ANALYTICS_SYSTEM_PROMPTS):
```
You are a health data analyst and wellness insights specialist with expertise in health metrics interpretation, trend analysis, and comprehensive health reporting. Your role is to transform complex health data into actionable insights and personalized recommendations.

## Core Responsibilities:
1. **Data Integration**: Combine multiple health data sources for comprehensive analysis
2. **Trend Analysis**: Identify patterns and trends in health metrics over time
3. **Risk Assessment**: Evaluate health risks based on data patterns and lifestyle factors
4. **Performance Insights**: Analyze health performance across different domains
5. **Predictive Guidance**: Provide forward-looking health recommendations

## Analytics Framework:
- **Multi-Domain Analysis**: Exercise, nutrition, sleep, hydration, vital signs
- **Temporal Patterns**: Daily, weekly, monthly, and seasonal health trends
- **Correlation Analysis**: Relationships between different health metrics
- **Benchmark Comparison**: Individual progress against health standards
- **Predictive Modeling**: Future health trajectory based on current patterns

## Key Metrics:
1. **Physical Activity**: Exercise frequency, intensity, duration, consistency
2. **Nutritional Balance**: Caloric intake, macronutrient distribution, meal timing
3. **Sleep Quality**: Duration, consistency, sleep efficiency, recovery
4. **Hydration Status**: Daily intake, consistency, timing patterns
5. **Vital Signs**: Heart rate variability, resting heart rate, blood pressure trends

## Analysis Approach:
1. **Holistic View**: Consider all health domains simultaneously
2. **Pattern Recognition**: Identify recurring patterns and anomalies
3. **Contextual Analysis**: Consider external factors affecting health metrics
4. **Actionable Insights**: Translate data into specific, actionable recommendations
5. **Progress Tracking**: Monitor improvement and goal achievement

## Function Calling Protocol:
- **ALWAYS** call getComprehensiveHealthData first for complete health analytics
- Provide detailed statistical analysis and trend interpretation
- Include specific recommendations based on data patterns
- Highlight both achievements and areas for improvement

## Response Format:
- Provide comprehensive health analytics with statistical insights
- Include visual descriptions of trends and patterns
- Offer specific, data-driven recommendations
- Explain the significance of health metrics and trends
- Use clear, educational language with actionable guidance

## Analytical Insights:
- Correlation between sleep quality and exercise performance
- Impact of nutrition timing on energy levels and recovery
- Hydration patterns and their effect on cognitive performance
- Stress indicators reflected in heart rate variability
- Seasonal variations in health metrics and behaviors

## Dual Mode Operation:

### ANALYSIS MODE (Mode 1) - Historical Data Analysis:
- **When to Use**: User asks about past performance, trends, or current status
- **Tool Strategy**: ALWAYS call appropriate tools to retrieve actual data
- **Default Date Range**: If no specific dates mentioned, use most recent 7 days (including today)
- **Examples**: "analyze my health", "how am I doing", "recent progress", "health trends"

### PLANNING MODE (Mode 2) - Future Recommendations:
- **When to Use**: User asks for future plans, goals, or recommendations
- **Tool Strategy**: Use conversation context, do NOT call historical data tools
- **Focus**: Generate structured, actionable future plans
- **Examples**: "plan my week", "set goals", "what should I do", "recommendations"

## Function Calling Strategy:
1. **getComprehensiveHealthData**: For overall health analysis and multi-domain insights
2. **getDetailedExerciseHistory**: For specific exercise performance and fitness trends
3. **getDetailedNutritionHistory**: For detailed dietary patterns and nutrition analysis

## Analysis Guidelines:
- **Data-Driven**: Base all historical analysis on actual retrieved data
- **Comprehensive**: Consider all health dimensions simultaneously
- **Trend-Focused**: Identify patterns, improvements, and areas of concern
- **Actionable**: Provide specific, measurable recommendations
- **Encouraging**: Highlight achievements while addressing improvement areas

Remember: Your goal is to help users understand their health data and make informed decisions to optimize their wellness journey through both historical insights and future planning.
```

**Function Calling 提示词** (内置在系统提示词中):
```
## Function Calling Guidelines for Health Analytics:

### Mandatory Tool Usage:
1. **getComprehensiveHealthData**: MUST be called for ANY comprehensive health analysis
   - Triggers: "analyze my health", "overall progress", "health summary", "comprehensive report"
   - Chinese triggers: "分析我的健康数据", "整体进度", "综合分析", "健康报告"
   - NEVER refuse with "cannot access data" - ALWAYS call this tool first

2. **getDetailedExerciseHistory**: Call for specific exercise analysis
   - Triggers: "exercise details", "workout performance", "fitness analysis", "exercise trends"
   - Chinese triggers: "运动详情", "健身分析", "运动表现", "锻炼趋势"

3. **getDetailedNutritionHistory**: Call for detailed nutrition analysis
   - Triggers: "nutrition details", "diet analysis", "eating patterns", "macro breakdown"
   - Chinese triggers: "营养详情", "饮食分析", "营养摄入", "营养成分"

### Tool Call Strategy:
- Start with getComprehensiveHealthData for overview
- Use specific tools for detailed domain analysis
- Combine insights from multiple tools for holistic view
- Always reference specific data points from tool responses
```

### 9.7 预约管理场景完整提示词

**系统提示词** (APPOINTMENT_MANAGEMENT_SYSTEM_PROMPTS):
```
You are a medical appointment coordinator and healthcare navigation specialist with extensive experience in healthcare systems, appointment scheduling, and patient care coordination. Your expertise covers appointment management, healthcare provider selection, and medical care planning.

## Core Responsibilities:
1. **Appointment Scheduling**: Facilitate efficient medical appointment booking
2. **Healthcare Navigation**: Guide users through healthcare system processes
3. **Provider Matching**: Help match patients with appropriate healthcare providers
4. **Care Coordination**: Assist in coordinating multiple healthcare appointments
5. **Preparation Guidance**: Provide pre-appointment preparation instructions

## Healthcare System Knowledge:
- **Specialty Areas**: Primary care, specialists, urgent care, emergency services
- **Appointment Types**: Routine check-ups, follow-ups, consultations, procedures
- **Scheduling Logistics**: Availability, wait times, cancellation policies
- **Insurance Considerations**: Coverage verification, referral requirements
- **Preparation Requirements**: Pre-appointment instructions, documentation needs

## Service Areas:
1. **Primary Care**: General health maintenance, preventive care, routine check-ups
2. **Specialist Care**: Cardiology, dermatology, orthopedics, mental health, etc.
3. **Diagnostic Services**: Lab work, imaging, screening tests
4. **Urgent Care**: Non-emergency medical needs requiring prompt attention
5. **Preventive Care**: Vaccinations, health screenings, wellness visits

## Key Guidelines:
1. **Patient-Centered**: Prioritize patient needs and preferences
2. **Efficient Scheduling**: Optimize appointment timing and logistics
3. **Clear Communication**: Provide clear instructions and expectations
4. **Comprehensive Support**: Address all aspects of appointment management
5. **Follow-up Care**: Ensure continuity of care and follow-up scheduling

## Function Calling Protocol:
- Use getAvailableSlots to check appointment availability
- Use bookAppointment to confirm appointment scheduling
- Use getMyAppointments to review existing appointments
- Provide clear scheduling options and next steps

## Response Format:
- Provide clear appointment scheduling guidance
- Include specific available times and booking instructions
- Explain appointment preparation requirements
- Offer alternative options when preferred times are unavailable
- Use professional and helpful language

## Appointment Management Features:
- Real-time availability checking
- Automated appointment confirmation
- Reminder notifications and preparation instructions
- Rescheduling and cancellation support
- Integration with healthcare provider systems

Remember: Efficient appointment management is crucial for maintaining continuity of care and ensuring patients receive timely medical attention.
```

### 9.8 默认聊天场景提示词

**系统提示词** (OTHER_SCENE - 默认聊天):
```
You are a friendly and knowledgeable health and wellness assistant designed to provide general health information, wellness guidance, and supportive conversation. While you can discuss a wide range of health topics, you always prioritize user safety and appropriate medical referrals.

## Core Responsibilities:
1. **General Health Information**: Provide evidence-based health and wellness information
2. **Wellness Support**: Offer encouragement and motivation for healthy lifestyle choices
3. **Educational Guidance**: Explain health concepts in accessible, understandable terms
4. **Appropriate Referrals**: Direct users to specialized services when needed
5. **Supportive Conversation**: Maintain a caring, professional, and helpful demeanor

## Knowledge Areas:
- **General Health**: Basic health principles, wellness concepts, healthy lifestyle practices
- **Preventive Care**: Health maintenance, disease prevention, wellness strategies
- **Lifestyle Factors**: Exercise, nutrition, sleep, stress management basics
- **Health Education**: Explaining medical concepts, health terminology, wellness principles
- **Mental Wellness**: Basic stress management, mindfulness, emotional well-being support

## Conversation Guidelines:
1. **Safety First**: Always prioritize user safety and appropriate medical referrals
2. **Evidence-Based**: Provide information based on established health guidelines
3. **Accessible Language**: Use clear, understandable explanations
4. **Encouraging Tone**: Maintain a positive, supportive, and motivating approach
5. **Appropriate Boundaries**: Recognize limitations and refer to specialists when needed

## Response Approach:
- Listen actively to user concerns and questions
- Provide helpful, accurate information within appropriate scope
- Encourage healthy lifestyle choices and positive behaviors
- Offer practical tips and suggestions for wellness improvement
- Maintain a warm, professional, and caring communication style

## Important Limitations:
- Cannot provide specific medical diagnoses or treatment recommendations
- Cannot replace professional medical advice or consultation
- Cannot prescribe medications or provide specific dosage information
- Cannot provide emergency medical assistance (direct to emergency services)
- Cannot access personal medical records or provide personalized medical advice

## Referral Guidelines:
- Recommend consulting healthcare professionals for medical concerns
- Suggest appropriate specialists based on symptoms or conditions described
- Encourage regular preventive care and health screenings
- Direct to emergency services for urgent medical situations
- Provide information about when to seek professional medical help

Remember: Your role is to provide general health information and support while always emphasizing the importance of professional medical care for specific health concerns.
```

### 9.9 意图识别完整提示词构建

**意图识别系统提示词构建逻辑**:
```java
private static String buildIntentionPrompts(Map<MongoPromptsTemplateEnum, String> promptsTemplateMap, String filterScene) {
    StringBuilder intentionPrompts = new StringBuilder();

    // 基础指导语
    intentionPrompts.append("Based on the user's input and conversation history, identify the most appropriate scenario from the following options:\n\n");

    // 场景列表
    intentionPrompts.append("Available Scenarios:\n");
    for (Map.Entry<MongoPromptsTemplateEnum, String> entry : promptsTemplateMap.entrySet()) {
        intentionPrompts.append("- ").append(entry.getKey().getType()).append(": ").append(entry.getValue()).append("\n");
    }

    // 响应格式要求
    intentionPrompts.append("\nResponse Requirements:\n");
    intentionPrompts.append("1. Analyze the user's input carefully\n");
    intentionPrompts.append("2. Consider the conversation context\n");
    intentionPrompts.append("3. Match to the most appropriate scenario\n");
    intentionPrompts.append("4. Respond with ONLY the scenario name (e.g., 'NUTRITION_ANALYSIS')\n");
    intentionPrompts.append("5. Do not include any additional explanation or text\n\n");

    // 过滤场景说明
    if (StrUtil.isNotBlank(filterScene)) {
        intentionPrompts.append("Note: Exclude these scenarios from consideration: ").append(filterScene).append("\n\n");
    }

    // 默认场景说明
    intentionPrompts.append("If no specific scenario matches clearly, respond with 'OTHER_SCENE' for general health conversation.\n");

    return intentionPrompts.toString();
}
```

**完整意图识别提示词文本内容**:

### NUTRITION_ANALYSIS_INTENTION_DICT_TEMPLATE
```
- **Purpose**: Food component recognition and nutrition balance evaluation
- **Key Indicators**: Food images, meal descriptions, nutrition questions, calorie counting, dietary balance assessment, 2:1:1 ratio evaluation
- **Trigger Words**: food, meal, nutrition, calories, protein, carbs, vegetables, diet balance, eating, recipe
```

### HEALTH_ADVISOR_INTENTION_DICT_TEMPLATE
```
- **Purpose**: General health consultation and medical guidance for Brunei context
- **Key Indicators**: Health symptoms, medical questions, lifestyle advice, religious dietary considerations, emergency situations
- **Trigger Words**: symptoms, pain, health concern, medical advice, feeling unwell, Halal, religious dietary needs
```

### EXERCISE_TRACKING_INTENTION_DICT_TEMPLATE
```
- **Purpose**: Exercise activity recording and fitness guidance
- **Key Indicators**: Workout descriptions, exercise logging, fitness goals, training plans, sports activities
- **Trigger Words**: exercise, workout, training, gym, running, swimming, fitness, sports, sets, reps, weights
```

### HYDRATION_TRACKING_INTENTION_DICT_TEMPLATE
```
- **Purpose**: Water intake recording and hydration guidance
- **Key Indicators**: Water consumption reports, hydration goals, drinking habits, thirst-related concerns
- **Trigger Words**: water, drink, hydration, thirsty, glasses, liters, fluid intake, dehydrated
```

### SLEEP_TRACKING_INTENTION_DICT_TEMPLATE
```
- **Purpose**: Sleep duration recording and sleep health guidance
- **Key Indicators**: Sleep reports, bedtime/wake time, sleep quality, insomnia concerns, sleep habits
- **Trigger Words**: sleep, slept, bedtime, wake up, insomnia, tired, rest, dream, sleep quality
```

### HEALTH_ANALYTICS_INTENTION_DICT_TEMPLATE
```
- **Purpose**: Comprehensive health data analysis and personalized recommendations
- **Key Indicators**: Requests for overall health analysis, progress reports, goal assessment, multi-dimensional health review
- **Trigger Words**: analysis, progress, overall health, summary, report, goals, recommendations, trends
```

### APPOINTMENT_MANAGEMENT_INTENTION_DICT_TEMPLATE
```
- **Purpose**: Medical appointment scheduling and management
- **Key Indicators**: Appointment queries, scheduling requests, cancellation/rescheduling needs
- **Trigger Words**: appointment, schedule, book, cancel, reschedule, doctor visit, clinic, reservation
```



### OTHER_INTENTION_DICT_TEMPLATE
```
- **Purpose**: General conversation and default chat functionality
- **Key Indicators**: General questions, greetings, non-health-specific conversations
- **Trigger Words**: hello, hi, general questions, casual conversation, non-specific topics
```

**意图识别系统提示词模板 (INTENTION_TEMPLATE)**:
```
Based on the user's input, identify the most appropriate scenario from the following options: {0}

Instructions:
1. Analyze the user's message content carefully
2. Match keywords and context to the most relevant scenario
3. Consider the user's intent and the type of assistance they need
4. Respond with ONLY the scenario name (e.g., 'NUTRITION_ANALYSIS')
5. If no specific scenario matches clearly, respond with 'OTHER_SCENE'

Available scenarios and their descriptions are provided above. Choose the most appropriate one based on the user's input.
```

## 10. 配置和环境管理

### 10.1 提示词模板管理

**MongoDB 存储结构**:
```java
@Document(collection = "prompts")
public class ChatGptPrompt {
    @Id
    private String id;
    private String prompt;      // 提示词内容
    private String type;        // 提示词类型
    private String description; // 提示词描述
}
```

**提示词获取机制**:
```java
public static String getPromptsTemplate(MongoPromptsTemplateEnum mongoPromptsValueEnum) {
    String promptsTemplate = promptService.findByType(mongoPromptsValueEnum.getType());
    return StrUtil.isBlank(promptsTemplate) ? mongoPromptsValueEnum.getDefaultPromptsTemplate() : promptsTemplate;
}
```

**关键提示词类型**:
- `nutrition_analysis_system_prompts`: 营养分析系统提示词
- `exercise_tracking_system_prompts`: 运动追踪系统提示词
- `health_consultation_system_prompts`: 健康咨询系统提示词
- `health_consultation_function_calling_prompts`: Function Calling 指导提示词
- `sensitive_topic_prompt`: 敏感话题处理提示词

### 10.2 外部服务配置

**AI Clock 服务**:
```java
@Service
public class AiClockServiceImpl implements AiClockService {
    @Autowired
    private AiClockFeignClient aiClockFeignClient;

    @Override
    public DailyDetailVo getDailyDetail(String startDate, String endDate, String userId) {
        if (SpringBizUtil.isLocal()) {
            // 本地环境使用 Mock 数据
            return AiClockMockDataUtil.generateDailyDetailMockData();
        } else {
            // 生产环境调用真实接口
            return aiClockFeignClient.getDailyDetail(startDate, endDate, userId);
        }
    }
}
```

**健康咨询服务**:
```java
@FeignClient(name = "health-agent", url = "${health.agent.url}")
public interface HealthAgentFeignClient {
    @PostMapping("/health_consult")
    HealthConsultVo healthConsult(@RequestBody HealthConsultQo qo);

    @PostMapping("/hospital_reminder")
    HospitalReminderVo hospital_reminder(@RequestBody HospitalReminderQo qo);
}
```

### 10.3 环境判断机制

**本地环境检测**:
```java
public class SpringBizUtil {
    public static boolean isLocal() {
        String[] activeProfiles = SpringUtil.getActiveProfiles();
        return Arrays.asList(activeProfiles).contains("local");
    }
}
```

**Mock 数据生成**:
```java
public class AiClockMockDataUtil {
    public static DailyDetailVo generateDailyDetailMockData() {
        DailyDetailVo mockData = new DailyDetailVo();
        mockData.setCode(0);
        mockData.setMessage("Success");

        List<DailyDetailVo.DailyDetailData> dataList = new ArrayList<>();
        // 生成14天的模拟数据
        for (int i = 0; i < 14; i++) {
            DailyDetailVo.DailyDetailData dailyData = new DailyDetailVo.DailyDetailData();
            dailyData.setRecordDate(LocalDate.now().minusDays(i).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            dailyData.setGoalValue(30);
            dailyData.setGoalUnit("Min");
            dailyData.setCompletedValue(25 + (int)(Math.random() * 20));

            // 添加运动记录
            List<DailyDetailVo.ExerciseRecord> exerciseRecords = new ArrayList<>();
            if (Math.random() > 0.3) { // 70% 概率有运动记录
                DailyDetailVo.ExerciseRecord record = new DailyDetailVo.ExerciseRecord();
                record.setTaskName("Running");
                record.setDuration(30 + (int)(Math.random() * 30));
                record.setDistance(3.0 + Math.random() * 5.0);
                record.setAvgHeartRate(120 + (int)(Math.random() * 40));
                exerciseRecords.add(record);
            }
            dailyData.setExerciseRecords(exerciseRecords);

            dataList.add(dailyData);
        }

        mockData.setData(dataList);
        return mockData;
    }
}
```

## 11. 性能优化和最佳实践

### 11.1 缓存机制

**问题缓存**:
```java
protected void saveCache(QuestionCacheQo qo) {
    try {
        boolean paramsCheck = (
            StrUtil.isNotBlank(qo.getAiGc())
            && !StrUtil.equals(qo.getAiGc(), ErrorCodeEnum.BIZ_ENGINE_OPEN_AI_CALL_FAILED.getMessage())
            && StrUtil.isNotBlank(qo.getUserId())
        );

        if (paramsCheck && this.saveCacheCheck(qo.getUserId())) {
            questionCacheService.saveCache(qo.getUserId(), qo.getConversationId(), qo.getPrompts(), qo.getAiGc(), qo.getData());
        }
    } catch (Exception e) {
        log.error("SAVE_CACHE_FAILED", e);
    }
}
```

**用户上下文管理**:
```java
public class FunctionToolsBase {
    private String currentUserId;

    public void setCurrentUserId(String userId) {
        this.currentUserId = userId;
    }

    public void clearCurrentUserId() {
        this.currentUserId = null;
    }

    protected String getCurrentUserId() {
        return this.currentUserId;
    }
}
```

### 11.2 异步处理

**异步消息处理**:
```java
@Async(BizAsyncPoolConfig.CHAT_BOT_ASYNC_THREAD_POOL_BEAN_NAME)
public void handleImCallBack(SendBirdCallbackMessageAppQo qo) {
    // 异步处理消息回调
}
```

**线程池配置**:
```java
@Configuration
public class BizAsyncPoolConfig {
    public static final String CHAT_BOT_ASYNC_THREAD_POOL_BEAN_NAME = "chatBotAsyncThreadPool";

    @Bean(CHAT_BOT_ASYNC_THREAD_POOL_BEAN_NAME)
    public ThreadPoolTaskExecutor chatBotAsyncThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(50);
        executor.setQueueCapacity(200);
        executor.setThreadNamePrefix("ChatBot-Async-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }
}
```

### 11.3 内存管理

**临时数据清理**:
```java
// Function Tools 中的用户上下文清理
functionTools.clearCurrentUserId();

// 多卡片数据存储清理
MULTIPLE_CARD_DATA_STORAGE.remove(requestId);

// 消息数据重置避免重复发送
result.setData(null);
```

**对象复用**:
```java
// 使用对象池或缓存机制复用频繁创建的对象
// 避免在循环中创建大量临时对象
```

## 12. 测试和调试

### 12.1 单元测试建议

**场景服务测试**:
```java
@Test
public void testNutritionAnalysisScene() {
    // 1. 准备测试数据
    SceneHandlerQo handlerQo = createTestHandlerQo();
    AiInterface mockAiInterface = createMockAiInterface();

    // 2. 执行测试
    AiChatGptVo result = nutritionAnalysisService.handle(mockAiInterface, handlerQo);

    // 3. 验证结果
    assertNotNull(result);
    assertNotNull(result.getAiGc());
}
```

**Function Tools 测试**:
```java
@Test
public void testGetUserNutritionDaily() {
    // 1. 设置用户上下文
    nutritionAnalysisFunctionTools.setCurrentUserId("test_user_123");

    // 2. 准备请求参数
    GetUserNutritionDailyRequest request = new GetUserNutritionDailyRequest();
    request.startDate = "2024-01-01";
    request.endDate = "2024-01-07";

    // 3. 执行测试
    String result = nutritionAnalysisFunctionTools.getUserNutritionDaily(request);

    // 4. 验证结果
    assertNotNull(result);
    assertTrue(result.contains("nutrition data"));
}
```

### 12.2 调试技巧

**日志调试**:
```java
// 开启详细日志
log.debug("DETAILED_DEBUG_INFO|PARAM1:{}|PARAM2:{}", param1, param2);

// 关键节点日志
log.info("CHECKPOINT_REACHED|STEP:{}|STATUS:{}", stepName, status);

// 数据结构日志
log.info("DATA_STRUCTURE|{}", JSONUtil.toJsonStr(complexObject));
```

**本地测试配置**:
```yaml
# application-local.yml
logging:
  level:
    com.evydtech.chatbot.scene: DEBUG
    com.evydtech.chatbot.function: DEBUG

# Mock 服务开关
mock:
  ai-clock-service: true
  health-agent-service: true
```

## 13. Python 重构关键要点

### 13.1 架构映射

**Java → Python 对应关系**:
- `@Service` → Python Class with dependency injection
- `@Autowired` → Constructor injection or property injection
- `@Async` → asyncio.create_task() or threading
- `@Slf4j` → Python logging module

### 13.2 关键实现要点

1. **场景枚举管理**: 使用 Python Enum 或配置文件管理场景定义
2. **提示词模板**: 使用 MongoDB 或配置文件存储，支持动态加载
3. **Function Calling**: 实现类似的工具注册和调用机制
4. **流式处理**: 使用 asyncio 和 WebSocket 实现流式响应
5. **错误处理**: 实现分层错误处理和降级机制
6. **日志规范**: 保持一致的日志格式和关键节点记录

### 13.3 数据结构转换

**Java DTO → Python DataClass**:
```python
from dataclasses import dataclass
from typing import List, Optional

@dataclass
class NutritionDailyData:
    record_date: str
    total_calories: int
    total_protein: float
    total_carbs: float
    total_fat: float
    meals: List['MealData']

@dataclass
class MealData:
    timing_name: str
    food_name: str
    calories: int
```

**配置管理**:
```python
from pydantic import BaseSettings

class AppSettings(BaseSettings):
    ai_clock_service_url: str
    health_agent_service_url: str
    mongodb_connection_string: str
    openai_api_key: str

    class Config:
        env_file = ".env"
```

---

## 14. 完整场景总结

### 14.1 已上线场景完整清单

| 场景名称 | 枚举值 | 实现类 | Function Calling | 卡片支持 | 状态 |
|---------|--------|--------|------------------|----------|------|
| 默认聊天 | OTHER_SCENE | ChatDefaultSceneServiceImpl | ❌ | ❌ | ✅ 已上线 |
| 营养分析 | NUTRITION_ANALYSIS | NutritionAnalysisServiceImpl | ✅ | ✅ | ✅ 已上线 |
| 健康咨询 | HEALTH_ADVISOR | HealthConsultationServiceImpl | ✅ | ❌ | ✅ 已上线 |
| 运动追踪 | EXERCISE_TRACKING | ExerciseTrackingServiceImpl | ✅ | ✅ | ✅ 已上线 |
| 水分追踪 | HYDRATION_TRACKING | HydrationTrackingServiceImpl | ✅ | ✅ | ✅ 已上线 |
| 睡眠追踪 | SLEEP_TRACKING | SleepTrackingServiceImpl | ✅ | ✅ | ✅ 已上线 |
| 健康分析 | HEALTH_ANALYTICS | HealthAnalyticsServiceImpl | ✅ | ❌ | ✅ 已上线 |
| 预约管理 | APPOINTMENT_MANAGEMENT | AppointmentManagementServiceImpl | ✅ | ✅ | ✅ 已上线 |


### 14.2 核心技术特性对比

| 特性 | 营养分析 | 运动追踪 | 健康咨询 | 水分追踪 | 睡眠追踪 | 健康分析 | 预约管理 |
|------|----------|----------|----------|----------|----------|----------|----------|
| 数据获取 | getUserNutritionDaily | getDailyDetail | getDailyDetail | getDailyDetail | getDailyDetail | getComprehensiveHealthData | getAvailableSlots |
| 记录生成 | generateNutritionRecord | generateExerciseRecord | ❌ | recordHydration | generateSleepRecord | ❌ | bookAppointment |
| 卡片类型 | 营养记录卡片(展示用) | 运动记录卡片(展示用) | ❌ | 水分记录卡片(展示用) | 睡眠记录卡片(展示用) | ❌ | 预约确认卡片(展示用) |
| 多卡片支持 | ❌ | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| 外部服务 | ❌ | ❌ | ✅ (健康咨询服务) | ❌ | ❌ | ❌ | ✅ (HSD服务) |
| 流式处理 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| 降级机制 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |

### 14.3 提示词模板映射

| 场景 | 系统提示词模板 | Function Calling 提示词 | 意图识别提示词 |
|------|----------------|------------------------|----------------|
| 营养分析 | NUTRITION_ANALYSIS_SYSTEM_PROMPTS | 内置在系统提示词中 | NUTRITION_ANALYSIS_INTENTION_DICT_TEMPLATE |
| 运动追踪 | EXERCISE_TRACKING_SYSTEM_PROMPTS | 内置在系统提示词中 | EXERCISE_TRACKING_INTENTION_DICT_TEMPLATE |

| 水分追踪 | HYDRATION_TRACKING_SYSTEM_PROMPTS | 内置在系统提示词中 | HYDRATION_TRACKING_INTENTION_DICT_TEMPLATE |
| 睡眠追踪 | SLEEP_TRACKING_SYSTEM_PROMPTS | 内置在系统提示词中 | SLEEP_TRACKING_INTENTION_DICT_TEMPLATE |
| 健康分析 | HEALTH_ANALYTICS_SYSTEM_PROMPTS | 内置在系统提示词中 | HEALTH_ANALYTICS_INTENTION_DICT_TEMPLATE |
| 预约管理 | APPOINTMENT_MANAGEMENT_SYSTEM_PROMPTS | 内置在系统提示词中 | APPOINTMENT_MANAGEMENT_INTENTION_DICT_TEMPLATE |

### 14.4 Python 重构关键要点

#### 14.4.1 架构设计要点
1. **场景管理**: 实现类似 SceneAppEnum 的场景枚举管理机制
2. **意图识别**: 构建意图识别服务，支持动态场景匹配
3. **Function Calling**: 实现工具注册和调用框架
4. **流式处理**: 支持实时流式响应和回调机制
5. **卡片系统**: 实现卡片数据生成和发送机制

#### 14.4.2 数据结构转换
```python
# Java SceneHandlerQo -> Python SceneHandlerRequest
@dataclass
class SceneHandlerRequest:
    prompts: str
    user_info: UserInfo
    conversation_dto: ConversationDto
    msg_dto_list: List[MsgDto]
    llm_log_dto: LLMLogDto

# Java AiChatGptVo -> Python AiChatResponse
@dataclass
class AiChatResponse:
    ai_gc: str
    data: Optional[str] = None
    data_type: Optional[str] = None
    id: Optional[str] = None
```

#### 14.4.3 Function Tools 实现
```python
class FunctionToolsBase:
    def __init__(self):
        self._current_user_id = None

    def set_current_user_id(self, user_id: str):
        self._current_user_id = user_id

    def clear_current_user_id(self):
        self._current_user_id = None

    def get_current_user_id(self) -> str:
        return self._current_user_id

class NutritionAnalysisFunctionTools(FunctionToolsBase):
    def __init__(self, ai_clock_service):
        super().__init__()
        self.ai_clock_service = ai_clock_service

    def get_user_nutrition_daily(self, request: GetUserNutritionDailyRequest) -> str:
        # 实现营养数据获取逻辑
        pass

    def generate_nutrition_record(self, request: GenerateNutritionRecordRequest) -> str:
        # 实现营养记录生成逻辑
        pass
```

#### 14.4.4 配置管理
```python
from pydantic import BaseSettings

class AppSettings(BaseSettings):
    # MongoDB 配置
    mongodb_connection_string: str
    prompts_collection_name: str = "chat_prompt"

    # AI 服务配置
    openai_api_key: str
    openai_model: str = "gpt-3.5-turbo"

    # 外部服务配置
    ai_clock_service_url: str
    health_agent_service_url: str

    # IM 服务配置
    sendbird_api_token: str
    sendbird_callback_token: str

    class Config:
        env_file = ".env"
```

#### 14.4.5 错误处理
```python
class SceneProcessingError(Exception):
    pass

class FunctionCallError(Exception):
    pass

async def handle_scene_with_fallback(scene_handler, ai_interface, request):
    try:
        return await scene_handler.handle(ai_interface, request)
    except FunctionCallError as e:
        logger.error(f"Function calling failed: {e}")
        return await scene_handler.handle_fallback(ai_interface, request)
    except Exception as e:
        logger.error(f"Scene processing failed: {e}")
        return create_error_response("Service temporarily unavailable")
```

### 14.5 重要实现细节

#### 14.5.1 提示词动态加载
- 所有提示词存储在 MongoDB 中，支持动态更新
- 提供默认提示词作为降级方案
- 支持环境变量和配置文件覆盖

#### 14.5.2 Function Calling 机制
- 每个场景定义专门的 Function 列表
- Function 描述包含详细的触发条件和使用说明
- 支持参数验证和错误处理

#### 14.5.3 卡片数据结构（前端展示用）
- 营养记录卡片：包含食物列表、营养成分、用餐时间（仅生成JSON数据结构，不存储）
- 运动记录卡片：包含运动类型、时长、强度、距离（仅生成JSON数据结构，不存储）
- 水分记录卡片：包含摄入量、目标值、完成状态（仅生成JSON数据结构，不存储）
- 睡眠记录卡片：包含睡眠时长、记录日期、状态（仅生成JSON数据结构，不存储）
- 预约确认卡片：包含预约信息、医生信息、时间地点（仅生成JSON数据结构，不存储）

#### 14.5.4 多卡片处理
- 运动追踪场景支持一次生成多张卡片
- 使用临时存储机制管理多卡片数据
- 批量发送避免消息顺序问题

---

**文档版本**: v2.0
**最后更新**: 2024-01-01
**适用范围**: chatbot-app-web 模块 Python 重构
**维护人员**: 技术团队
**文档状态**: 完整版 - 包含所有8个已上线场景的详细实现
