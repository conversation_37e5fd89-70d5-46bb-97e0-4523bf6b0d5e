# 患者数据管理接口文档

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-07-30
- **最后更新**: 2025-07-30
- **维护团队**: 聊天机器人开发团队
- **文档状态**: 正式版

## 目录
- [简介](#简介)
- [接口概览](#接口概览)
- [通用规范](#通用规范)
  - [请求格式](#请求格式)
  - [响应格式](#响应格式)
  - [错误码规范](#错误码规范)
  - [参数校验规范](#参数校验规范)
- [IndicationDataController 指标数据控制器](#indicationdatacontroller-指标数据控制器)
  - [查询患者最新指标数据](#查询患者最新指标数据)
- [PatientRecordController 患者记录控制器](#patientrecordcontroller-患者记录控制器)
  - [查询患者记录](#查询患者记录)
- [数据模型定义](#数据模型定义)
- [业务流程说明](#业务流程说明)
- [安全性说明](#安全性说明)
- [性能指标](#性能指标)
- [常见问题](#常见问题)

## 简介

本文档详细描述了聊天机器人系统中患者数据管理相关的HTTP API接口，包括指标数据查询和患者记录查询功能。这些接口主要用于：

1. **指标数据管理**: 提供患者健康指标数据的查询功能，支持根据患者ID和指标key列表获取最新的指标数据
2. **患者记录查询**: 提供灵活的SQL查询功能，支持从OLAP数据库中获取患者相关记录数据

### 业务背景
- 系统需要为聊天机器人提供患者健康数据查询能力
- 支持实时获取患者最新的健康指标数据
- 提供灵活的数据查询接口，满足不同业务场景的数据需求
- 确保数据查询的安全性和性能

### 技术架构
- **框架**: Spring Boot + Spring MVC
- **数据访问**: Feign Client + 外部API调用
- **参数校验**: JSR-303 Bean Validation
- **日志记录**: SLF4J + Logback
- **响应格式**: 统一的Result包装器

## 接口概览

| 控制器 | 接口路径 | 请求方式 | 功能描述 | 负责人 |
|--------|----------|----------|----------|--------|
| IndicationDataController | `/indication/query-last` | POST | 查询患者最新指标数据 | Claude |
| PatientRecordController | `/patient/record/query` | POST | 查询患者记录 | Claude |

## 通用规范

### 请求格式

所有接口均采用HTTP协议，支持以下通用规范：

#### 请求头要求
```http
Content-Type: application/json
Accept: application/json
```

#### 字符编码
- 统一使用UTF-8编码
- 请求和响应数据均为JSON格式

### 响应格式

所有接口均返回统一的响应格式：

```json
{
  "data": "T",
  "code": "Integer",
  "message": "String"
}
```

#### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| data | T | 响应数据，泛型类型，根据具体接口而定 |
| code | Integer | 状态码，0表示成功，非0表示失败 |
| message | String | 状态描述信息 |

### 错误码规范

| 错误码 | 描述 | 处理建议 |
|--------|------|----------|
| 0 | 成功 | 正常处理返回数据 |
| -1 | 通用失败 | 检查请求参数和系统状态 |
| 400 | 参数校验失败 | 检查请求参数格式和必填项 |
| 500 | 系统内部错误 | 联系技术支持 |

### 参数校验规范

系统采用JSR-303 Bean Validation进行参数校验：

- `@NotBlank`: 字符串不能为空且不能只包含空白字符
- `@NotEmpty`: 集合不能为空
- `@Valid`: 启用嵌套对象校验

校验失败时返回400错误码，message字段包含具体的校验错误信息。

## IndicationDataController 指标数据控制器

### 控制器概述

**类名**: `IndicationDataController`
**包路径**: `com.evydtech.chatbot.controller.IndicationDataController`
**功能描述**: 提供患者指标数据的查询接口，支持根据患者ID和指标key列表查询最新指标数据
**创建日期**: 2025/7/29
**作者**: Claude

#### 业务职责
- 接收患者指标数据查询请求
- 参数校验和格式化
- 调用业务服务层获取指标数据
- 统一异常处理和日志记录
- 返回标准化响应结果

#### 技术特性
- 使用`@RestController`注解，支持RESTful API
- 集成`@Valid`参数校验
- 完整的异常处理机制
- 详细的操作日志记录

### 查询患者最新指标数据

#### 接口基本信息

- **接口名称**: queryLast
- **接口URL**: `/indication/query-last`
- **请求方式**: POST
- **接口负责人**: Claude
- **接口职责**: 根据患者ID和指标key列表查询最新的指标数据

#### 业务场景
1. **健康监控**: 获取患者最新的健康指标数据，如血压、血糖、体重等
2. **数据分析**: 为AI分析提供最新的患者健康数据
3. **报告生成**: 为健康报告生成提供数据支持
4. **趋势分析**: 获取指标数据用于健康趋势分析

#### 请求参数

##### 请求体参数 (IndicationQueryQo)

| 参数名 | 类型 | 是否必须 | 校验规则 | 描述 | 示例值 |
|--------|------|----------|----------|------|--------|
| memberId | String | 是 | @NotBlank | 患者memberId，唯一标识患者 | "patient_12345" |
| keys | List&lt;String&gt; | 是 | @NotEmpty | 指标key列表，指定要查询的指标类型 | ["blood_pressure", "blood_sugar"] |

##### 请求体示例

```json
{
  "memberId": "patient_12345",
  "keys": [
    "blood_pressure",
    "blood_sugar",
    "weight",
    "heart_rate"
  ]
}
```

#### 响应参数

##### 成功响应 (Result&lt;IndicationQueryVo&gt;)

| 参数名 | 类型 | 描述 |
|--------|------|------|
| data | IndicationQueryVo | 指标查询结果对象 |
| code | Integer | 状态码，0表示成功 |
| message | String | 状态描述，成功时为"success" |

##### IndicationQueryVo 对象结构

| 参数名 | 类型 | 描述 |
|--------|------|------|
| total | Integer | 总记录数 |
| list | List&lt;IndicationRecordVo&gt; | 指标记录列表 |

##### IndicationRecordVo 对象结构

| 参数名 | 类型 | 描述 |
|--------|------|------|
| id | Integer | 记录ID，自动生成的序号 |
| indicationDTO | IndicationDTO | 指标数据对象 |

##### IndicationDTO 对象结构

| 参数名 | 类型 | 描述 | 示例值 |
|--------|------|------|--------|
| key | String | 指标key，标识指标类型 | "blood_pressure" |
| value | String | 指标值 | "120/80" |
| valueType | String | 值类型 | "string" |
| extData | String | 扩展数据，JSON字符串格式 | "{\"unit\":\"mmHg\"}" |
| recordDateTime | String | 记录时间 | "2025-07-30 10:30:00" |

#### 响应示例

##### 成功响应示例

```json
{
  "data": {
    "total": 3,
    "list": [
      {
        "id": 1,
        "indicationDTO": {
          "key": "blood_pressure",
          "value": "120/80",
          "valueType": "string",
          "extData": "{\"unit\":\"mmHg\",\"category\":\"normal\"}",
          "recordDateTime": "2025-07-30 10:30:00"
        }
      },
      {
        "id": 2,
        "indicationDTO": {
          "key": "blood_sugar",
          "value": "5.6",
          "valueType": "number",
          "extData": "{\"unit\":\"mmol/L\",\"status\":\"normal\"}",
          "recordDateTime": "2025-07-30 09:15:00"
        }
      },
      {
        "id": 3,
        "indicationDTO": {
          "key": "weight",
          "value": "70.5",
          "valueType": "number",
          "extData": "{\"unit\":\"kg\",\"bmi\":\"22.8\"}",
          "recordDateTime": "2025-07-30 08:00:00"
        }
      }
    ]
  },
  "code": 0,
  "message": "success"
}
```

##### 失败响应示例

###### 参数校验失败
```json
{
  "data": null,
  "code": -1,
  "message": "查询失败: 患者memberId不能为空"
}
```

###### 系统异常
```json
{
  "data": null,
  "code": -1,
  "message": "查询失败: 调用外部指标数据API失败"
}
```

###### 无数据返回
```json
{
  "data": {
    "total": 0,
    "list": []
  },
  "code": 0,
  "message": "success"
}
```

#### 内部处理流程

1. **参数校验**: 使用`@Valid`注解校验请求参数
   - 验证`memberId`不为空
   - 验证`keys`列表不为空
2. **日志记录**: 记录查询请求的关键信息
3. **服务调用**: 调用`IndicationDataService.queryLast()`方法
4. **数据转换**: 将外部API返回的数据转换为内部数据结构
5. **结果封装**: 将查询结果封装为统一的`Result`对象
6. **异常处理**: 捕获并处理可能出现的异常
7. **响应返回**: 返回标准化的JSON响应

#### 业务逻辑说明

##### 数据来源
- 通过`IndicationDataApi`调用外部指标数据服务
- 外部服务返回`IndicationPageQuery`查询结果
- 系统将外部数据转换为内部数据模型

##### 数据处理
1. **ID生成**: 为每条记录自动生成递增的ID
2. **数据映射**: 将外部API的数据结构映射到内部DTO
3. **扩展数据处理**: `extData`字段直接透传，由下游系统处理
4. **时间格式**: 保持原始的时间格式字符串

##### 性能考虑
- 查询结果按指标key进行过滤
- 只返回最新的指标数据
- 支持批量查询多个指标

#### 错误处理机制

| 异常类型 | 处理方式 | 返回码 | 日志级别 |
|----------|----------|--------|----------|
| 参数校验失败 | 返回校验错误信息 | -1 | INFO |
| 外部API调用失败 | 包装异常信息返回 | -1 | ERROR |
| 数据转换异常 | 返回通用错误信息 | -1 | ERROR |
| 系统异常 | 返回通用错误信息 | -1 | ERROR |

## PatientRecordController 患者记录控制器

### 控制器概述

**类名**: `PatientRecordController`
**包路径**: `com.evydtech.chatbot.controller.PatientRecordController`
**功能描述**: 提供患者记录查询的HTTP API接口，支持通过SQL查询获取患者相关数据
**创建日期**: 2025/7/29
**作者**: Claude

#### 业务职责
- 接收患者记录查询请求
- SQL查询参数校验
- 调用OLAP查询服务获取患者记录
- 统一异常处理和安全控制
- 返回结构化查询结果

#### 技术特性
- 使用`@RestController`注解，支持RESTful API
- 集成`@Valid`参数校验
- 通过Feign客户端调用远程OLAP服务
- 支持灵活的SQL查询
- 完整的异常处理和日志记录

#### 安全考虑
- SQL注入防护
- 查询权限控制
- 敏感数据脱敏
- 查询性能监控

### 查询患者记录

#### 接口基本信息

- **接口名称**: queryPatientRecords
- **接口URL**: `/patient/record/query`
- **请求方式**: POST
- **接口负责人**: Claude
- **接口职责**: 通过SQL查询语句获取患者相关记录数据

#### 业务场景
1. **数据分析**: 为数据分析师提供灵活的患者数据查询能力
2. **报表生成**: 支持各种患者数据报表的生成
3. **业务查询**: 满足业务人员的个性化数据查询需求
4. **数据导出**: 支持患者数据的批量导出功能

#### 请求参数

##### 请求体参数 (SqlQueryRequest)

| 参数名 | 类型 | 是否必须 | 校验规则 | 描述 | 示例值 |
|--------|------|----------|----------|------|--------|
| sql | String | 是 | @NotBlank | SQL查询语句，用于在OLAP数据库中执行查询 | "SELECT * FROM patient_records WHERE patient_id = 'P001'" |

##### 请求体示例

```json
{
  "sql": "SELECT patient_id, record_date, record_type, record_content FROM patient_records WHERE patient_id = 'P001' AND record_date >= '2025-07-01' ORDER BY record_date DESC LIMIT 10"
}
```

#### 响应参数

##### 成功响应 (Result&lt;PatientRecordQueryVo&lt;Map&lt;String, Object&gt;&gt;&gt;)

| 参数名 | 类型 | 描述 |
|--------|------|------|
| data | PatientRecordQueryVo&lt;Map&lt;String, Object&gt;&gt; | 患者记录查询结果对象 |
| code | Integer | 状态码，0表示成功 |
| message | String | 状态描述，成功时为"success" |

##### PatientRecordQueryVo 对象结构

| 参数名 | 类型 | 描述 |
|--------|------|------|
| code | Integer | OLAP服务响应状态码，200表示成功 |
| data | List&lt;Map&lt;String, Object&gt;&gt; | 查询结果数据列表，每个Map代表一行记录 |
| message | String | OLAP服务响应消息 |
| timeStamp | Long | 响应时间戳 |

##### 数据结构说明

由于SQL查询的结果结构不确定，系统使用`Map<String, Object>`结构来接收查询结果：
- **Key**: 数据库列名
- **Value**: 对应的列值，类型根据数据库字段类型而定

#### 响应示例

##### 成功响应示例

```json
{
  "data": {
    "code": 200,
    "data": [
      {
        "patient_id": "P001",
        "record_date": "2025-07-30",
        "record_type": "consultation",
        "record_content": "患者血压正常，建议继续监测",
        "doctor_id": "D001",
        "created_time": "2025-07-30 10:30:00"
      },
      {
        "patient_id": "P001",
        "record_date": "2025-07-29",
        "record_type": "examination",
        "record_content": "血常规检查结果正常",
        "doctor_id": "D002",
        "created_time": "2025-07-29 14:20:00"
      },
      {
        "patient_id": "P001",
        "record_date": "2025-07-28",
        "record_type": "medication",
        "record_content": "开具降压药物，每日一次",
        "doctor_id": "D001",
        "created_time": "2025-07-28 16:45:00"
      }
    ],
    "message": "查询成功",
    "timeStamp": 1722326400000
  },
  "code": 0,
  "message": "success"
}
```

##### 失败响应示例

###### SQL语句为空
```json
{
  "data": null,
  "code": -1,
  "message": "查询失败: SQL查询语句不能为空"
}
```

###### OLAP服务异常
```json
{
  "data": null,
  "code": -1,
  "message": "查询失败: 远程OLAP服务调用失败"
}
```

###### 无查询结果
```json
{
  "data": {
    "code": 200,
    "data": [],
    "message": "查询成功，无匹配记录",
    "timeStamp": 1722326400000
  },
  "code": 0,
  "message": "success"
}
```

#### 内部处理流程

1. **参数校验**: 使用`@Valid`注解校验SQL查询语句
   - 验证`sql`字段不为空
   - 基础SQL语法检查
2. **日志记录**: 记录SQL查询语句和关键操作信息
3. **服务调用**: 调用`PatientRecordService.queryPatientRecords()`方法
4. **Feign调用**: 通过`PatientRecordFeignClient`调用远程OLAP服务
5. **数据封装**: 将OLAP服务返回的数据封装为统一响应格式
6. **异常处理**: 捕获并处理网络异常、服务异常等
7. **响应返回**: 返回标准化的JSON响应

#### 业务逻辑说明

##### 数据流转
1. **请求接收**: Controller接收HTTP请求
2. **参数转换**: 将SQL字符串封装为`PatientRecordQueryQo`对象
3. **远程调用**: 通过Feign客户端调用OLAP查询服务
4. **结果处理**: 接收并处理OLAP服务返回的查询结果
5. **响应封装**: 将结果封装为统一的`Result`对象

##### 技术架构
- **Controller层**: 负责HTTP请求处理和参数校验
- **Service层**: 负责业务逻辑处理和数据转换
- **Feign层**: 负责远程服务调用
- **OLAP服务**: 提供实际的数据查询功能

##### 查询特性
- **灵活性**: 支持任意SQL查询语句
- **通用性**: 使用Map结构适配不同的查询结果
- **性能**: 直接查询OLAP数据库，响应速度快
- **扩展性**: 易于扩展支持更多查询功能

#### 错误处理机制

| 异常类型 | 处理方式 | 返回码 | 日志级别 |
|----------|----------|--------|----------|
| SQL参数为空 | 返回参数校验错误 | -1 | INFO |
| Feign调用失败 | 包装网络异常信息 | -1 | ERROR |
| OLAP服务异常 | 返回服务异常信息 | -1 | ERROR |
| 数据解析异常 | 返回数据格式错误 | -1 | ERROR |

## 数据模型定义

### 统一响应对象 (Result&lt;T&gt;)

```java
@Data
public class Result<T> {
    private T data;           // 响应数据，泛型类型
    private Integer code;     // 状态码，0表示成功，非0表示失败
    private String message;   // 状态描述信息

    // 静态工厂方法
    public static <T> Result<T> success(T data);
    public static <T> Result<T> success();
    public static <T> Result<T> fail();
    public static <T> Result<T> fail(String message);
    public static <T> Result<T> fail(Integer code, String message);
}
```

### 指标查询相关模型

#### IndicationQueryQo - 指标查询请求对象
```java
@Data
public class IndicationQueryQo {
    @NotBlank(message = "患者memberId不能为空")
    private String memberId;        // 患者memberId

    @NotEmpty(message = "指标key列表不能为空")
    private List<String> keys;      // 指标key列表
}
```

#### IndicationQueryVo - 指标查询响应对象
```java
@Data
public class IndicationQueryVo {
    private Integer total;                          // 总记录数
    private List<IndicationRecordVo> list;         // 指标记录列表
}
```

#### IndicationRecordVo - 指标记录对象
```java
@Data
public class IndicationRecordVo {
    private Integer id;                    // 记录ID
    private IndicationDTO indicationDTO;   // 指标数据对象
}
```

#### IndicationDTO - 指标数据对象
```java
@Data
public class IndicationDTO {
    private String key;              // 指标key
    private String value;            // 指标值
    private String valueType;        // 值类型
    private String extData;          // 扩展数据，JSON字符串格式
    private String recordDateTime;   // 记录时间
}
```

### 患者记录查询相关模型

#### SqlQueryRequest - SQL查询请求对象
```java
public static class SqlQueryRequest {
    @NotBlank(message = "SQL查询语句不能为空")
    private String sql;              // SQL查询语句

    // getter和setter方法
    public String getSql() { return sql; }
    public void setSql(String sql) { this.sql = sql; }
}
```

#### PatientRecordQueryQo - 患者记录查询请求对象
```java
@Data
public class PatientRecordQueryQo {
    private String sqlVal;           // SQL查询语句
}
```

#### PatientRecordQueryVo - 患者记录查询响应对象
```java
@Data
public class PatientRecordQueryVo<T> {
    private Integer code;            // 响应状态码，200表示成功
    private List<T> data;           // 查询结果数据列表
    private String message;         // 响应消息
    private Long timeStamp;         // 响应时间戳
}
```

## 业务流程说明

### 指标数据查询流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as IndicationDataController
    participant Service as IndicationDataService
    participant API as IndicationDataApi
    participant External as 外部指标服务

    Client->>Controller: POST /indication/query-last
    Controller->>Controller: 参数校验(@Valid)
    Controller->>Service: queryLast(request)
    Service->>API: queryLast(pageQuery)
    API->>External: 调用外部指标API
    External-->>API: 返回指标数据
    API-->>Service: 返回查询结果
    Service->>Service: 数据转换和封装
    Service-->>Controller: 返回IndicationQueryVo
    Controller->>Controller: 封装Result对象
    Controller-->>Client: 返回JSON响应
```

### 患者记录查询流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as PatientRecordController
    participant Service as PatientRecordService
    participant Feign as PatientRecordFeignClient
    participant OLAP as OLAP查询服务

    Client->>Controller: POST /patient/record/query
    Controller->>Controller: SQL参数校验
    Controller->>Service: queryPatientRecords(sql)
    Service->>Feign: queryPost(queryQo)
    Feign->>OLAP: POST /internal/olap/query-post
    OLAP-->>Feign: 返回查询结果
    Feign-->>Service: 返回PatientRecordQueryVo
    Service-->>Controller: 返回查询结果
    Controller->>Controller: 封装Result对象
    Controller-->>Client: 返回JSON响应
```

## 安全性说明

### 数据安全

#### 访问控制
- **接口权限**: 所有接口需要通过身份验证
- **数据权限**: 基于用户角色控制数据访问范围
- **IP白名单**: 限制接口访问来源IP地址

#### SQL注入防护
- **参数化查询**: 使用预编译语句防止SQL注入
- **输入校验**: 对SQL语句进行基础语法检查
- **权限限制**: 限制可执行的SQL操作类型（仅允许SELECT）
- **查询监控**: 记录所有SQL查询操作日志

#### 数据脱敏
- **敏感字段**: 对患者姓名、身份证号等敏感信息进行脱敏
- **日志脱敏**: 日志中不记录完整的敏感数据
- **传输加密**: 使用HTTPS协议加密数据传输

### 系统安全

#### 异常处理
- **统一异常**: 使用统一的异常处理机制
- **错误信息**: 不暴露系统内部实现细节
- **日志记录**: 详细记录异常信息用于问题排查

#### 性能保护
- **查询超时**: 设置合理的查询超时时间
- **结果限制**: 限制单次查询返回的记录数量
- **频率限制**: 对高频查询进行限流保护

## 性能指标

### 响应时间要求

| 接口 | 平均响应时间 | 95%响应时间 | 99%响应时间 |
|------|-------------|-------------|-------------|
| 指标数据查询 | < 200ms | < 500ms | < 1000ms |
| 患者记录查询 | < 500ms | < 1000ms | < 2000ms |

### 并发性能

| 指标 | 目标值 | 说明 |
|------|--------|------|
| 并发用户数 | 1000+ | 支持1000个并发用户同时访问 |
| QPS | 500+ | 每秒处理500个以上请求 |
| 系统可用性 | 99.9% | 年度可用性不低于99.9% |

### 资源使用

| 资源类型 | 使用限制 | 监控指标 |
|----------|----------|----------|
| CPU使用率 | < 70% | 平均CPU使用率 |
| 内存使用率 | < 80% | JVM堆内存使用率 |
| 数据库连接 | < 80% | 连接池使用率 |

## 常见问题

### Q1: 指标数据查询返回空结果怎么办？

**A**: 可能的原因和解决方案：
1. **患者ID不存在**: 检查`memberId`是否正确
2. **指标key不匹配**: 确认`keys`列表中的指标类型是否存在
3. **数据未同步**: 等待数据同步完成后重试
4. **时间范围**: 检查是否在有效的时间范围内

### Q2: SQL查询语句有什么限制？

**A**: SQL查询限制包括：
1. **操作类型**: 仅支持SELECT查询，不支持INSERT/UPDATE/DELETE
2. **表访问**: 只能访问授权的患者数据表
3. **查询复杂度**: 避免过于复杂的JOIN和子查询
4. **结果数量**: 单次查询结果不超过10000条记录

### Q3: 如何处理查询超时？

**A**: 查询超时处理方案：
1. **优化SQL**: 添加适当的索引和WHERE条件
2. **分页查询**: 使用LIMIT限制单次查询数量
3. **异步处理**: 对于大数据量查询考虑异步处理
4. **缓存机制**: 对频繁查询的数据进行缓存

### Q4: 接口返回-1错误码如何排查？

**A**: 错误排查步骤：
1. **检查日志**: 查看详细的错误日志信息
2. **参数校验**: 确认请求参数格式和必填项
3. **网络连接**: 检查与外部服务的网络连接
4. **服务状态**: 确认依赖的外部服务是否正常

### Q5: 如何提高查询性能？

**A**: 性能优化建议：
1. **索引优化**: 为常用查询字段添加数据库索引
2. **查询优化**: 避免SELECT *，只查询需要的字段
3. **缓存策略**: 对热点数据进行缓存
4. **连接池**: 合理配置数据库连接池参数

---

**文档结束**

*如有疑问或需要技术支持，请联系开发团队。*
