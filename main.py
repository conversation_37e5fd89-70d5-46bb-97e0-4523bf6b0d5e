import os

from fastapi import FastAP<PERSON>
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from api.v1 import test, chat, agent, ocr, opa
from dotenv import load_dotenv
from utils.biz_logger import get_logger
from contextlib import asynccontextmanager
from utils.config import get_mongo_uri
from pymongo import MongoClient
from service.prompt.tool import PromptTool


# 初始化全局 logger
logger = get_logger(__name__)
# Load from .env
load_dotenv('.env')

MONGO_URI = os.getenv("MONGO_URI")
MONGO_DB = os.getenv("MONGO_DB")

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时
    mongo_client = MongoClient(MONGO_URI)
    app.state.mongo_client = mongo_client
    # 注册全局 PromptTool mongo_client
    PromptTool.init_with_mongo_client(mongo_client, MONGO_DB)
    yield
    mongo_client.close()


def create_app():
    app = FastAPI(lifespan=lifespan)
    # 添加静态文件服务
    app.mount("/static", StaticFiles(directory="static"), name="static")

    # 添加测试页面路由
    @app.get("/test")
    async def test_page():
        return FileResponse('static/test_chat.html')

    app.include_router(test.router, prefix="/v1/test")
    app.include_router(chat.router, prefix="/v1/chat")
    app.include_router(agent.router, prefix="/v1/agent")
    app.include_router(ocr.router, prefix="/v1/ocr")
    app.include_router(opa.router, prefix="/v1/opa")

    return app

app = create_app()

# 在业务中获取
def get_mongo_client(request):
    return request.app.state.mongo_client

if __name__ == "__main__":
    import uvicorn
    logger.info("Start FastAPI server at 0.0.0.0:8000")
    # uvicorn.run(app, host="0.0.0.0", port=8000)
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)

