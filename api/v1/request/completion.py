from typing import Optional
from pydantic import BaseModel, Field

class CompletionRequest(BaseModel):
    biz_unique_id: Optional[str] = Field(None, description="业务唯一ID")
    biz_user_id: Optional[str] = Field(None, description="业务用户ID")
    message: str = Field(..., description="消息内容")
    custom_data: Optional[str] = Field("{}", description="自定义数据(JSON字符串)")
    stream: bool = Field(False, description="是否流式输出")
    workflow_type: Optional[str] = Field("default", description="工作流类型标识，如: health_scene_workflow, default等") 