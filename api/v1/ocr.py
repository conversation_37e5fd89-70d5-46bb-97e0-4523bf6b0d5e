"""
OCR API接口

提供图片识别功能，支持：
1. 通过文件ID识别图片
2. 通过图片URL识别图片

@author: shaohua.sun
@date: 2025/6/20
"""

from fastapi import APIRouter, Body, Depends, HTTPException
from typing import Optional
from pydantic import BaseModel, Field

from service.ai_service import AIService
from ai.config.ai_config import ModelConfig, FactoryConfig
from utils.biz_logger import get_logger
from utils.response import Result

logger = get_logger(__name__)

router = APIRouter(tags=['OCR'])


class OcrRequest(BaseModel):
    """OCR请求模型"""
    file_id: Optional[str] = Field(None, description="文件ID")
    image_url: Optional[str] = Field(None, description="图片URL")
    biz_user_id: Optional[str] = Field(None, description="业务用户ID")


def build_ai_service() -> AIService:
    """
    构建AI服务实例
    
    Returns:
        AIService: AI服务实例
    """
    factory_config = FactoryConfig()
    model_config = ModelConfig()
    return AIService(factory=factory_config, model=model_config)


@router.post("/process")
async def process_image(
    request: OcrRequest = Body(...),
    ai_service: AIService = Depends(build_ai_service)
):
    """
    处理图片识别请求
    
    Args:
        request: OCR请求对象
        ai_service: AI服务实例
        
    Returns:
        OCR处理结果
    """
    try:
        # 参数验证
        if not request.file_id and not request.image_url:
            return Result.fail(msg="file_id和image_url至少需要提供一个", code=400)
        
        logger.info(f"收到OCR请求: file_id={request.file_id}, image_url={request.image_url}, biz_user_id={request.biz_user_id}")
        
        # 调用AI服务处理OCR
        result = ai_service.ocr_process_image(
            file_id=request.file_id,
            image_url=request.image_url,
            biz_user_id=request.biz_user_id
        )
        
        if result["success"]:
            logger.info(f"OCR处理成功: text_length={len(result['data']['text'] or '')}")
            return Result.success(data=result["data"], message=result["message"])
        else:
            logger.warning(f"OCR处理失败: {result['message']}")
            return Result.fail(msg=result["message"], code=400)
            
    except Exception as e:
        logger.error(f"OCR API异常: {e}", exc_info=True)
        return Result.fail(msg=f"服务器错误: {str(e)}", code=500)


@router.post("/process-simple")
async def process_image_simple(
    file_id: Optional[str] = Body(None, embed=True),
    image_url: Optional[str] = Body(None, embed=True),
    biz_user_id: Optional[str] = Body(None, embed=True),
    ai_service: AIService = Depends(build_ai_service)
):
    """
    简化的图片识别接口
    
    Args:
        file_id: 文件ID（可选）
        image_url: 图片URL（可选）
        biz_user_id: 业务用户ID（可选）
        ai_service: AI服务实例
        
    Returns:
        OCR处理结果
    """
    try:
        # 参数验证
        if not file_id and not image_url:
            return Result.fail(msg="file_id和image_url至少需要提供一个", code=400)
        
        logger.info(f"收到简化OCR请求: file_id={file_id}, image_url={image_url}, biz_user_id={biz_user_id}")
        
        # 调用AI服务处理OCR
        result = ai_service.ocr_process_image(
            file_id=file_id,
            image_url=image_url,
            biz_user_id=biz_user_id
        )
        
        if result["success"]:
            logger.info(f"简化OCR处理成功: text_length={len(result['data']['text'] or '')}")
            return Result.success(data=result["data"], message=result["message"])
        else:
            logger.warning(f"简化OCR处理失败: {result['message']}")
            return Result.fail(msg=result["message"], code=400)
            
    except Exception as e:
        logger.error(f"简化OCR API异常: {e}", exc_info=True)
        return Result.fail(msg=f"服务器错误: {str(e)}", code=500) 