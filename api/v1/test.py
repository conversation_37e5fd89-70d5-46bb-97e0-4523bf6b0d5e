# Test
#
# @author: 崔晓波
# @email: <EMAIL>
# @date: 2025/6/16 18:30
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
from fastapi import APIRouter, Body, Depends, HTTPException

from service.prompt.tool import PromptTool
from utils.response import Result

router = APIRouter(tags=['Test'])
@router.post("/ping")
async def ping():
    return Result.success(data="pong")

@router.get("/prompt")
def get_prompt_api(scene: str, user: str = "test", score: int = 100):
    # 这里参数可根据实际需求调整
    params = {"user": user, "score": score}
    prompt = PromptTool.get_prompt(scene, params)
    return {"prompt": prompt}