# agent
#
# @author: 崔晓波
# @email: <EMAIL>
# @date: 2025/6/18 11:34
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
import json
import time

from fastapi import APIRouter, Body, Depends, HTTPException
from starlette.responses import StreamingResponse

from api.v1.request.completion import CompletionRequest
from api.v1.chat import build_ai_service
from service.ai_service import AIService
from utils.biz_logger import get_logger
from utils.response import Result
from langchain_core.messages import HumanMessage, AIMessage

router = APIRouter(tags=['Agent'])
logger = get_logger(__name__)


def _extract_card_business_data(card_data):
    """
    从卡片数据中提取最终的业务数据，将content的整个结构提取到最外层
    
    Args:
        card_data: 原始卡片数据（现在4个场景已经直接返回content格式）
        
    Returns:
        dict: 最终的业务数据，或None
    """
    if not isinstance(card_data, dict):
        return None
    
    # 现在4个场景已经直接返回content格式，直接返回即可
    return card_data


def _process_chunk_unified(chunk, message_id):
    """
    统一处理数据块，支持多种格式的输入
    
    Args:
        chunk: 数据块
        message_id: 消息ID
        
    Returns:
        tuple: (event_type, data) 或 None
    """
    try:
        chunk_type = chunk.get('chunk_type', 'unknown')
        
        # 处理标准格式的数据块
        if chunk_type == "text":
            # 文本内容 - message事件
            # 不再移除空格，保留所有字符（包括空格、制表符、换行符等）
            content = chunk.get('content', '')
            if content or content == ' ':  # 允许空格字符通过
                # 过滤掉标准流程标记，避免被当作文本内容发送
                if content in ['[ALL_START]', '[TEXT_START]', '[TEXT_END]', '[ALL_END]', 
                              'ALL_START', 'TEXT_START', 'TEXT_END', 'ALL_END']:
                    return None  # 忽略这些标记内容
                
                return ('message', {
                    'id': message_id,
                    'type': 'text', 
                    'content': content
                })
                
        elif chunk_type == "card":
            # 卡片数据 - custom事件，提取最核心的业务数据
            card_data = chunk.get('content', {})
            if card_data:
                # 深度提取最终的业务数据，移除所有包装层
                final_card_data = _extract_card_business_data(card_data)
                if final_card_data:
                    return ('custom', {
                        'id': message_id,
                        'type': 'card',
                        'content': final_card_data
                    })
                
                
        elif chunk_type == "final":
            # 最终结果（降级处理）
            if chunk.get('data'):
                final_card_data = _extract_card_business_data(chunk['data'])
                if final_card_data:
                    return ('custom', {
                        'id': message_id,
                        'type': 'card',
                        'content': final_card_data
                    })
                
        elif chunk_type == "error":
            # 错误处理
            error_msg = chunk.get('error', '处理失败')
            return ('message', {
                'id': message_id,
                'type': 'text',
                'content': f"错误: {error_msg}"
            })
            
        # 处理健康工作流或其他工作流的特殊格式（保持向后兼容）
        elif 'text' in chunk:
            # 可能是JSON字符串格式的文本
            text_content = chunk['text']
            if isinstance(text_content, str):
                try:
                    # 尝试解析JSON
                    text_data = json.loads(text_content)
                    if 'text' in text_data:
                        # 不再移除空格，保留所有字符
                        content = text_data['text']
                        if content or content == ' ':  # 允许空格字符通过
                            # 过滤掉标准流程标记
                            if content in ['[ALL_START]', '[TEXT_START]', '[TEXT_END]', '[ALL_END]', 
                                          'ALL_START', 'TEXT_START', 'TEXT_END', 'ALL_END']:
                                return None  # 忽略这些标记内容
                            
                            return ('message', {
                                'id': message_id,
                                'type': 'text',
                                'content': content
                            })
                except (json.JSONDecodeError, TypeError):
                    # 直接使用文本内容
                    # 不再移除空格，保留所有字符
                    clean_content = text_content
                    if clean_content or clean_content == ' ':  # 允许空格字符通过
                        # 过滤掉标准流程标记
                        if clean_content in ['[ALL_START]', '[TEXT_START]', '[TEXT_END]', '[ALL_END]', 
                                           'ALL_START', 'TEXT_START', 'TEXT_END', 'ALL_END']:
                            return None  # 忽略这些标记内容
                        
                        return ('message', {
                            'id': message_id,
                            'type': 'text',
                            'content': clean_content
                        })
                        
        elif 'card' in chunk:
            # 可能是JSON字符串格式的卡片
            card_content = chunk['card']
            if isinstance(card_content, str):
                try:
                    card_data = json.loads(card_content)
                    # 提取最终的业务数据
                    final_card_data = _extract_card_business_data(card_data)
                    if final_card_data:
                        return ('custom', {
                            'id': message_id,
                            'type': 'card',
                            'content': final_card_data
                        })
                except (json.JSONDecodeError, TypeError):
                    pass
            elif isinstance(card_content, dict):
                # 直接是字典格式的卡片
                final_card_data = _extract_card_business_data(card_content)
                if final_card_data:
                    return ('custom', {
                        'id': message_id,
                        'type': 'card',
                        'content': final_card_data
                    })
                    
        elif 'marker' in chunk:
            # 标记块 - 返回标记但不直接发送，由流程控制
            marker = chunk['marker']
            if marker:
                # 移除中括号，清理标记内容（这里可以保持原样，因为marker不需要格式）
                clean_marker = marker.strip('[]')
                # 过滤掉标准流程标记，避免重复
                if clean_marker in ['ALL_START', 'TEXT_START', 'TEXT_END', 'ALL_END']:
                    return None  # 忽略这些标记，由流程控制
                else:
                    # 其他自定义标记正常返回
                    return ('custom', {
                        'id': message_id,
                        'type': 'marker',
                        'content': clean_marker
                    })
                
        # 如果是其他格式，尝试直接处理
        elif isinstance(chunk, dict) and len(chunk) == 1:
            key, value = list(chunk.items())[0]
            if key in ['success', 'chunk', 'final'] and isinstance(value, str):
                # 不再移除空格，保留所有字符
                content = value
                if content or content == ' ':  # 允许空格字符通过
                    return ('message', {
                        'id': message_id,
                        'type': 'text',
                        'content': content
                    })
        
        return None
        
    except Exception as e:
        logger.error(f"处理数据块时发生错误: {e}, chunk: {chunk}")
        return None


@router.post('/')
async def index():
    return Result.success(data="Hello Agent")


@router.post("/stream-test")
async def stream_test(
        message: str = Body(..., embed=True),
        ai_service: AIService = Depends(build_ai_service)
):
    try:
        logger.info(f"Received stream_chat request: {message}")

        def generate():
            test_id = "test_message_001"
            
            # 使用新的消息格式
            yield f"event: custom\ndata: {json.dumps({'id': test_id, 'type': 'marker', 'content': 'ALL_START'})}\n\n"
            yield f"event: custom\ndata: {json.dumps({'id': test_id, 'type': 'marker', 'content': 'TEXT_START'})}\n\n"
            
            for chunk in ai_service.agent_completion_stream_test():
                logger.info(f"completion_chunk: {chunk}")
                # 文本内容使用message事件
                yield f"event: message\ndata: {json.dumps({'id': test_id, 'type': 'text', 'content': chunk.content})}\n\n"
                
            yield f"event: custom\ndata: {json.dumps({'id': test_id, 'type': 'marker', 'content': 'TEXT_END'})}\n\n"
            
            # 卡片数据使用custom事件，直接提供业务数据
            card_content = {
                "buttonConfirmText": "Book",
                "jumpUrl": "/ihospital/#/appointment/choose/department?scheduleId=9028776&memberId=8ff7f781-1acf-4020-98aa-ac7ec8a3bbb7",
                "clinicName": "PHC General Clinic",
                "apptFromTime": "08:00",
                "apptDate": "2025-06-20",
                "apptToTime": "09:00",
                "hospitalName": "Berakas Health Centre",
                "scheduleId": 9028776,
                "status": 1
            }
            
            yield f"event: custom\ndata: {json.dumps({'id': test_id, 'type': 'card', 'content': card_content})}\n\n"
            yield f"event: custom\ndata: {json.dumps({'id': test_id, 'type': 'marker', 'content': 'ALL_END'})}\n\n"

        return StreamingResponse(
            generate(),
            media_type="text/event-stream; charset=utf-8",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no"
            }
        )
    except Exception as e:
        logger.error(f"stream_chat API exception: {e}", exc_info=True)
        raise HTTPException(500, detail=f"Server error: {str(e)}")


@router.post("/completion")
async def completion(
        body: CompletionRequest = Body(...),
        ai_service: AIService = Depends(build_ai_service)
):
    try:
        logger.info(
            f"Received completion request: biz_unique_id={body.biz_unique_id}, biz_user_id={body.biz_user_id}, workflow_type={body.workflow_type}, stream={body.stream}, message={body.message}")
        
        if body.stream:
            # 流式处理 - 统一处理逻辑
            async def generate():
                # 记录请求开始时间
                request_start_time = time.time()
                first_response_time = None
                final_response_time = None
                
                # 获取消息ID，优先使用biz_unique_id，其次使用thread_id
                message_id = body.biz_unique_id or "unknown"
                
                # 流式数据处理状态
                all_started = False
                text_started = False
                text_ended = False
                has_text_content = False
                pending_cards = []  # 存储待发送的卡片数据
                first_content_sent = False  # 标记是否已发送第一个内容
                
                # 使用统一的工作流流式接口
                async for chunk in ai_service.workflow_completion_stream(
                    biz_unique_id=body.biz_unique_id,
                    biz_user_id=body.biz_user_id, 
                    message=body.message,
                    workflow_type=body.workflow_type,
                    custom_data=body.custom_data
                ):
                    try:
                        processed_data = _process_chunk_unified(chunk, message_id)
                        if processed_data:
                            event_type, data = processed_data
                            
                            # 如果是文本内容
                            if data.get('type') == 'text':
                                # 发送ALL_START标记（仅一次）
                                if not all_started:
                                    yield f"event: custom\ndata: {json.dumps({'id': message_id, 'type': 'marker', 'content': 'ALL_START'})}\n\n"
                                    all_started = True
                                
                                # 发送TEXT_START标记（仅一次）
                                if not text_started:
                                    yield f"event: custom\ndata: {json.dumps({'id': message_id, 'type': 'marker', 'content': 'TEXT_START'})}\n\n"
                                    text_started = True
                                
                                # 发送文本内容并记录第一次响应时间
                                yield f"event: {event_type}\ndata: {json.dumps(data)}\n\n"
                                has_text_content = True
                                
                                # 记录第一次返回结果的时间
                                if not first_content_sent:
                                    first_response_time = time.time()
                                    first_content_duration = (first_response_time - request_start_time) * 1000  # 转换为毫秒
                                    logger.info(f"[耗时日志] 请求到第一次返回结果耗时: {first_content_duration:.2f}ms, biz_unique_id={body.biz_unique_id}, message_id={message_id}")
                                    first_content_sent = True
                                
                            # 如果是卡片数据，先存储，等文本结束后再发送
                            elif data.get('type') == 'card':
                                pending_cards.append((event_type, data))
                                
                            # 如果是marker，跳过不发送（避免重复标记）
                            elif data.get('type') == 'marker':
                                # 忽略原始的marker标记，我们使用自己的标记控制
                                continue
                                
                            else:
                                # 其他类型数据直接发送
                                yield f"event: {event_type}\ndata: {json.dumps(data)}\n\n"
                                
                    except Exception as e:
                        logger.error(f"处理流式数据块失败: {e}")
                        continue
                
                # 文本流结束后的处理
                if text_started and not text_ended:
                    yield f"event: custom\ndata: {json.dumps({'id': message_id, 'type': 'marker', 'content': 'TEXT_END'})}\n\n"
                    text_ended = True
                
                # 发送所有待发送的卡片数据
                for event_type, card_data in pending_cards:
                    yield f"event: {event_type}\ndata: {json.dumps(card_data)}\n\n"
                
                # 发送ALL_END标记（仅在有实际内容时）
                if all_started:
                    yield f"event: custom\ndata: {json.dumps({'id': message_id, 'type': 'marker', 'content': 'ALL_END'})}\n\n"
                
                # 记录最终完成时间
                final_response_time = time.time()
                total_duration = (final_response_time - request_start_time) * 1000  # 转换为毫秒
                logger.info(f"[耗时日志] 请求到最终完成耗时: {total_duration:.2f}ms, biz_unique_id={body.biz_unique_id}, message_id={message_id}")
                
                # 如果没有第一次响应时间记录（可能没有文本内容），也记录一下
                if first_response_time is None:
                    logger.info(f"[耗时日志] 请求无文本内容，总耗时: {total_duration:.2f}ms, biz_unique_id={body.biz_unique_id}, message_id={message_id}")
            
            return StreamingResponse(
                generate(),
                media_type="text/event-stream; charset=utf-8",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "X-Accel-Buffering": "no"
                }
            )
        else:
            # 同步处理 - 直接返回原始结果
            # 记录同步请求开始时间
            sync_start_time = time.time()
            
            result = await ai_service.workflow_completion(
                biz_unique_id=body.biz_unique_id,
                biz_user_id=body.biz_user_id,
                message=body.message,
                workflow_type=body.workflow_type,
                custom_data=body.custom_data,
                stream=False
            )
            
            # 记录同步请求完成时间
            sync_end_time = time.time()
            sync_duration = (sync_end_time - sync_start_time) * 1000  # 转换为毫秒
            logger.info(f"[耗时日志] 同步请求完成耗时: {sync_duration:.2f}ms, biz_unique_id={body.biz_unique_id}")
            
            # 同步模式直接返回大模型交互的原始结果，保持简洁
            return Result.success(data=result)
            
    except Exception as e:
        logger.error(f"completion API exception: {e}", exc_info=True)
        return Result.fail(msg=f"Server error: {str(e)}", code=500)
