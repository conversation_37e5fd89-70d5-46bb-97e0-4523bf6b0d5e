import json
from typing import Literal

from fastapi import APIRouter, Body, Depends, HTTPException
from starlette.responses import StreamingResponse

from ai.router import Router
from ai.config.ai_config import ModelConfig, RouterConfig, FactoryConfig
from service.ai_service import AIService
from utils.biz_logger import get_logger
from utils.response import Result
from ai.manager.ai_manager import AIManager
from api.v1.request.completion import CompletionRequest
from utils.random_util import build_random_uuid
import time

logger = get_logger(__name__)

router = APIRouter(tags=['Chat'])


def build_ai_service() -> AIService:
    """
    Build AI service
    return: AIService Instance
    """
    factory_config = FactoryConfig()
    model_config = ModelConfig()
    return AIService(factory=factory_config, model=model_config)


def build_ai_router() -> Router:
    """
    Build Router service
    return: AI Instance
    """
    config = RouterConfig(
        task_type="text",
        platform="openai"
    )

    return Router(config)


@router.post("/")
async def chat(
        message: str = Body(..., embed=True),
        ai_service: AIService = Depends(build_ai_service)  # Inject dependency, default AI factory
):
    try:
        logger.info(f"Received chat request: {message}")
        response = ai_service.chat(message)
        logger.info(f"chat response: {response}")
        return Result.success(data=response)
    except Exception as e:
        logger.error(f"chat API exception: {e}", exc_info=True)
        return Result.fail(msg=f"Server error: {str(e)}", code=500)


@router.post("/stream")
async def stream_chat(
        message: str = Body(..., embed=True),
        ai_service: AIService = Depends(build_ai_service)
):
    try:
        logger.info(f"Received stream_chat request: {message}")

        def generate():
            yield f"event: marker\ndata: [ALL_START]\n\n"
            for chunk in ai_service.stream_chat(message):
                yield f"event: message\n"
                yield f"data: {json.dumps({'text': chunk})}\n\n"
            card = {"type": "card", "title": "AI卡片", "content": "xxx"}
            yield f"event: card\ndata: {json.dumps(card)}\n\n"
            yield f"event: marker\ndata: [ALL_END]\n\n"

        return StreamingResponse(
            generate(),
            media_type="text/event-stream; charset=utf-8",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no"
            }
        )
    except Exception as e:
        logger.error(f"stream_chat API exception: {e}", exc_info=True)
        raise HTTPException(500, detail=f"Server error: {str(e)}")


@router.post("/router")
async def handle_router(
        message: str = Body(..., embed=True),
        task_type: Literal["text", "image"] = Body("text", embed=False),
        platform: Literal["openai", "deepseek", "qwen"] = Body("openai", embed=False),
        image_url: str = Body(None, embed=True)
):
    try:
        logger.info(f"Received handle_router request: {message}, task_type: {task_type}, image_url: {image_url}")
        response = AIManager().handle_by_router(message, task_type, platform, image_url)
        logger.info(f"handle_router response: {response}")
        return Result.success(data=response)
    except Exception as e:
        logger.error(f"handle_router API exception: {e}", exc_info=True)
        return Result.fail(msg=f"Server error: {str(e)}", code=500)


@router.post("/agent")
async def agent_chat(
        message: str = Body(..., embed=True),
        ai_service: AIService = Depends(build_ai_service)
):
    try:
        logger.info(f"Received agent_chat request: {message}")
        response = ai_service.handle_agent(message)
        logger.info(f"agent_chat response: {response}")
        return Result.success(data=response)
    except Exception as e:
        logger.error(f"agent_chat API exception: {e}", exc_info=True)
        return Result.fail(msg=f"Server error: {str(e)}", code=500)

@router.post("/agent-stream")
async def agent_chat(
        message: str = Body(..., embed=True),
        ai_service: AIService = Depends(build_ai_service)
):
    try:
        logger.info(f"Received agent_chat request: {message}")

        def generate():
            for chunk in ai_service.agent_chat_stream(message):
                yield chunk

        return StreamingResponse(
            generate(),
            media_type="text/event-stream;charset=utf-8",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no"
            }
        )
    except Exception as e:
        logger.error(f"agent_chat API exception: {e}", exc_info=True)
        return Result.fail(msg=f"Server error: {str(e)}", code=500)

@router.post("/agent-mem-short")
async def agent_chat_stream_short(
        message: str = Body(..., embed=True),
        ai_service: AIService = Depends(build_ai_service)
):
    try:
        logger.info(f"Received agent_chat_stream_short request: {message}")
        def generate():
            for chunk in ai_service.agent_chat_stream_short(message):
                yield chunk
        return StreamingResponse(
            generate(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no"
            }
        )
    except Exception as e:
        logger.error(f"agent_chat_stream_short API exception: {e}", exc_info=True)
        return Result.fail(msg=f"Server error: {str(e)}", code=500)

@router.post("/completion")
async def completion(
        body: CompletionRequest = Body(...),
        ai_service: AIService = Depends(build_ai_service)
):
    try:
        logger.info(f"Received completion request: biz_unique_id={body.biz_unique_id}, biz_user_id={body.biz_user_id}, stream={body.stream}, message={body.message}")
        result = ai_service.completion(body.biz_unique_id, body.biz_user_id, body.message, body.stream)
        if body.stream:
            def generate():
                for chunk in result:
                    yield f"data: {json.dumps({'id': build_random_uuid(), 'timestamp': int(time.time() * 1000), 'content': chunk})}\n\n"
            return StreamingResponse(
                generate(),
                media_type="text/event-stream; charset=utf-8",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "X-Accel-Buffering": "no"
                }
            )
        else:
            return Result.success(data=result)
    except Exception as e:
        logger.error(f"completion API exception: {e}", exc_info=True)
        return Result.fail(msg=f"Server error: {str(e)}", code=500)