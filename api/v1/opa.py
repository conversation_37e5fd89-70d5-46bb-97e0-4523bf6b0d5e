from fastapi import APIRouter
from pydantic import BaseModel
from service.opa.client import OPAClient
from utils.biz_logger import get_logger

logger = get_logger(__name__)
router = APIRouter()
opa_client = OPAClient()

class CheckAccessRequest(BaseModel):
    user_id: str
    target_member_id: str

@router.post("/check_access")
def check_access(request: CheckAccessRequest):
    """权限检查接口"""
    logger.info(f"收到权限检查请求: {request}")
    
    result = opa_client.check_access(
        user_id=request.user_id,
        target_member_id=request.target_member_id
    )
    
    logger.info(f"权限检查结果: {result}")
    return result