# ai_factory
#
# @author: 崔晓波
# @email: <EMAIL>
# @date: 2025/6/17 18:05
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD

from langchain_deepseek import ChatDeepSeek
from langchain_openai import ChatOpenAI
from typing import Optional, Any

from langchain_qwq import Chat<PERSON>wen
from openai import OpenAI

from ai.config.ai_config import FactoryConfig, ModelConfig
from ai.config.ai_enum import AIPlatformEnum
from ai.config.ai_properties import AIProperties


class Factory:
    ai_properties: AIProperties
    ai_factory_config: FactoryConfig
    ai_model_config: ModelConfig

    def __init__(self, factory_config: FactoryConfig):
        """
        AI 工厂，根据平台配置实例化 LLM
        """
        self.ai_properties = AIProperties()
        self.ai_factory_config = factory_config or FactoryConfig(platform=AIPlatformEnum.OPENAI)

    def build(self, model_config: Optional[ModelConfig]) -> Any:
        """
        根据工厂和模型配置实例化 LLM
        :param model_config: 模型配置
        :return: LLM 实例
        """
        if model_config is None:
            model_config = ModelConfig(
                api_key=self.ai_properties.openai.api_key,
                model_name=self.ai_properties.openai.model_name,
                temperature=0.5
            )
        platform = self.ai_factory_config.platform.lower() if self.ai_factory_config and self.ai_factory_config.platform else AIPlatformEnum.DEEPSEEK
        if platform == AIPlatformEnum.OPENAI:
            return self._build_openai(model_config)
        elif platform == AIPlatformEnum.DEEPSEEK:
            return self._build_deepseek(model_config)
        elif platform == AIPlatformEnum.QWEN:
            return self._build_qwen(model_config)
        else:
            raise ValueError(f'Platform not supported: {platform}')

    def _build_openai(self, model_config: ModelConfig) -> Any:
        return ChatOpenAI(
            model=model_config.model_name if model_config.model_name else self.ai_properties.openai.model_name,
            api_key=model_config.api_key if model_config.api_key else self.ai_properties.openai.api_key,
            temperature=model_config.temperature if model_config.temperature else self.ai_properties.openai.temperature,
            streaming=model_config.streaming if model_config.streaming else self.ai_properties.openai.streaming,
        )

    def _build_deepseek(self, model_config: ModelConfig) -> Any:
        return ChatQwen(
            model=model_config.model_name if model_config.model_name else self.ai_properties.deepseek.model_name,
            api_key=model_config.api_key if model_config.api_key else self.ai_properties.deepseek.api_key,
            temperature=model_config.temperature if model_config.temperature else self.ai_properties.deepseek.temperature,
            streaming=model_config.streaming if model_config.streaming else self.ai_properties.deepseek.streaming,
        )

    def _build_qwen(self, model_config: ModelConfig) -> Any:
        return ChatOpenAI(
            model=model_config.model_name if model_config.model_name else self.ai_properties.qwen.model_name,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
            api_key=model_config.api_key if model_config.api_key else self.ai_properties.qwen.api_key,
            temperature=model_config.temperature if model_config.temperature else self.ai_properties.qwen.temperature,
            streaming=model_config.streaming if model_config.streaming else self.ai_properties.qwen.streaming,
        )
