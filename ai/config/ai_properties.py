# ai_properties
#
# @author: 崔晓波
# @email: <EMAIL>
# @date: 2025/6/17 12:23
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
import os
from dataclasses import dataclass
from dotenv import load_dotenv

from ai.config.ai_enum import AIPlatformEnum


@dataclass
class Config:
    platform: str
    api_key: str
    model_name: str
    model_name_version: str
    temperature: float = 0
    max_tokens: int = 1000
    top_p: float = 1
    timeout: int = 60
    streaming: bool = False


class AIProperties:
    PLATFORM_OPENAI = 'openai'
    PLATFORM_DEEPSEEK = 'deepseek'
    PLATFORM_QW = 'qwen'

    def __init__(self):
        # Load from .env
        load_dotenv()

        self.openai = Config(
            platform=AIPlatformEnum.OPENAI,
            api_key=os.getenv('OPENAI_API_KEY'),
            model_name=os.getenv('OPENAI_MODEL', 'gpt-4.1-mini'),
            model_name_version=os.getenv('OPENAI_MODEL_VERSION', 'gpt-4.1'),
            temperature=os.getenv('OPENAI_TEMPERATURE', 0.5),
            max_tokens=os.getenv('OPENAI_MAX_TOKENS', 1000),
            top_p=os.getenv('OPENAI_TOP_P', 1),
            timeout=os.getenv('OPENAI_TIMEOUT', 60),
            streaming=False
        )

        self.deepseek = Config(
            platform=AIPlatformEnum.DEEPSEEK,
            api_key=os.getenv('DEEPSEEK_API_KEY'),
            model_name=os.getenv('DEEPSEEK_MODEL', ''),
            model_name_version="None",
            temperature=os.getenv('DEEPSEEK_TEMPERATURE', 0.5),
            max_tokens=os.getenv('DEEPSEEK_MAX_TOKENS', 1000),
            top_p=os.getenv('DEEPSEEK_TOP_P', 1),
            timeout=os.getenv('DEEPSEEK_TIMEOUT', 60),
            streaming=False
        )

        self.qwen = Config(
            platform=AIPlatformEnum.QWEN,
            api_key=os.getenv('QWEN_API_KEY'),
            model_name=os.getenv('QWEN_MODEL', ''),
            model_name_version=os.getenv('QWEN_MODEL_VERSION', ''),
            temperature=os.getenv('QWEN_TEMPERATURE', 0.5),
            max_tokens=os.getenv('QWEN_MAX_TOKENS', 1000),
            top_p=os.getenv('QWEN_TOP_P', 1),
            timeout=os.getenv('QWEN_TIMEOUT', 60),
            streaming=False
        )
