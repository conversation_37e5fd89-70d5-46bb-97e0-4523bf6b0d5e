from enum import Enum

class TaskTypeEnum(str, Enum):
    TEXT = "text"
    IMAGE = "image"

class AIPlatformEnum(str, Enum):
    OPENAI = "openai"
    DEEPSEEK = "deepseek"
    QWEN = "qwen"

class AIModelEnum(str, Enum):
    OPEN_AI_GPT_4_O = "gpt-4o"
    OPEN_AI_GPT_4_O_MINI = "gpt-4o-mini"
    OPEN_AI_GPT_4_1 = "gpt-4.1"
    OPEN_AI_GPT_4_1_MINI = "gpt-4.1-mini"
    OPEN_AI_GPT_4_1_NANO = "gpt-4.1-nano"