# ai_config
#
# @author: 崔晓波
# @email: <EMAIL>
# @date: 2025/6/17 22:41
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
from pydantic import BaseModel, SecretStr, Field, ConfigDict

from ai.config.ai_properties import AIProperties


class RouterConfig(BaseModel):
    """
    AI Router Config
    """
    task_type: str = "text"
    platform: str = "openai"


class FactoryConfig(BaseModel):
    """
    AI Factory Config
    """
    platform: str = "openai"


class ModelConfig(BaseModel):
    model_config = ConfigDict(arbitrary_types_allowed=True)
    ai_properties: AIProperties = AIProperties()
    """
    Configuration class for AI Service
    """
    api_key: SecretStr | None = Field(
        default=None,
        description="API Key (Encrypted)",
        exclude=True  # Hidden in log
    )
    model_name: str = None
    temperature: float = ai_properties.openai.temperature
    max_tokens: int = ai_properties.openai.max_tokens
    top_p: float = ai_properties.openai.top_p
    timeout: int = ai_properties.openai.timeout
    streaming: bool = ai_properties.openai.streaming
    # Add any other AI service configuration parameters here
