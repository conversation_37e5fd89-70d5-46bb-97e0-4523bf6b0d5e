# router
#
# @author: 崔晓波
# @email: <EMAIL>
# @date: 2025/6/17 18:04
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
from ai.config.ai_enum import TaskTypeEnum, AIPlatformEnum
from ai.factory import Factory
from ai.config.ai_config import RouterConfig, FactoryConfig, ModelConfig
from ai.config.ai_properties import AIProperties
from typing import Any


class Router:
    def __init__(self, router_config: RouterConfig):
        """
        AI 路由器，根据配置选择平台和模型
        """
        self.ai_properties = AIProperties()
        self.ai_router_config = router_config or RouterConfig(task_type='text')

    def handle(self, factory_config: FactoryConfig, model_config: ModelConfig) -> Any:
        """
        根据配置路由到不同平台的AI实例
        :param factory_config: 工厂配置
        :param model_config: 模型配置
        :return: AI实例
        """
        ai_platform = self.ai_router_config.platform or factory_config.platform
        task_type = self.ai_router_config.task_type
        if task_type == TaskTypeEnum.TEXT:
            return self._handle_text(ai_platform, factory_config, model_config)
        elif task_type == TaskTypeEnum.IMAGE.value:
            return self._handle_image(ai_platform, factory_config, model_config)
        else:
            raise ValueError(f'Task type not supported: {task_type}')

    def _handle_text(self, ai_platform: str, factory_config: FactoryConfig, model_config: ModelConfig) -> Any:
        if ai_platform is None or ai_platform.lower() == AIPlatformEnum.OPENAI.value.lower():
            factory_config = FactoryConfig(platform=AIPlatformEnum.OPENAI.lower())
            model = ModelConfig(
                api_key=model_config.api_key if model_config.api_key is not None else self.ai_properties.openai.api_key,
                model_name=model_config.model_name if model_config.model_name else self.ai_properties.openai.model_name,
                temperature=model_config.temperature if model_config.temperature else 0.5
            )
        elif ai_platform.lower() == AIPlatformEnum.DEEPSEEK:
            factory_config = FactoryConfig(platform=AIPlatformEnum.DEEPSEEK)
            model = ModelConfig(
                api_key=model_config.api_key if model_config.api_key is not None else self.ai_properties.deepseek.api_key,
                model_name=model_config.model_name if model_config.model_name else self.ai_properties.deepseek.model_name,
                temperature=model_config.temperature if model_config.temperature else 0.5
            )
        else:
            raise ValueError(f'Platform not supported: {ai_platform}')
        ai_factory = Factory(factory_config=factory_config)
        return ai_factory.build(model_config=model)

    def _handle_image(self, ai_platform: str, factory_config: FactoryConfig, model_config: ModelConfig) -> Any:
        if ai_platform is None or factory_config.platform.lower() == AIPlatformEnum.OPENAI:
            factory_config = FactoryConfig(platform=AIPlatformEnum.OPENAI)
            model = ModelConfig(
                api_key=model_config.api_key if model_config.api_key is not None else self.ai_properties.openai.api_key,
                model_name=model_config.model_name if model_config.model_name else self.ai_properties.openai.model_name_version,
                temperature=model_config.temperature if model_config.temperature else 0.5
            )
        elif factory_config.platform.lower() == AIPlatformEnum.QWEN:
            factory_config = FactoryConfig(platform=AIPlatformEnum.QWEN)
            model = ModelConfig(
                api_key=model_config.api_key if model_config.api_key is not None else self.ai_properties.qwen.api_key,
                model_name=model_config.model_name if model_config.model_name else self.ai_properties.qwen.model_name_version,
                temperature=model_config.temperature if model_config.temperature else 0.5
            )
        else:
            raise ValueError(f'Platform not supported for image: {ai_platform}')
        ai_factory = Factory(factory_config=factory_config)
        return ai_factory.build(model_config=model)
