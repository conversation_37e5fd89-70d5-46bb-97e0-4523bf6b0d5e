from typing import Optional, Dict, Any, List, Coroutine

from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from mem0 import Memory, AsyncMemory
from sqlalchemy.util import await_only

from utils import biz_logger

logger = biz_logger.get_logger(__name__)


def _build_mem0() -> Memory:
    return Memory.from_config(build_config())

def _build_mem0_async() -> Coroutine[Any, Any, Any]:
    return AsyncMemory.from_config(build_config())


def build_config():
    config = {
        # "llm": {
        #     "provider": "ollama",
        #     "config": {
        #         "model": "mistral:7b",
        #         "temperature": 0.1,
        #         "max_tokens": 2000,
        #         "ollama_base_url": "https://evyd-ai-deepseek.evyd.tech"
        #     }
        # },
        "embedder": {
            "provider": "ollama",
            "config": {
                "model": "nomic-embed-text",
                "embedding_dims": 768,
                "ollama_base_url": "http://127.0.0.1:11434"
            }
        },
        "vector_store": {
            "provider": "pgvector",
            "config": {
                "user": "postgres",
                "password": "EVYD_AI_TEAM",
                "host": "127.0.0.1",
                "port": "5432",
                "embedding_model_dims": 768
            }
        }
    }
    return config


class AgenticMemory:
    mem_instance: Memory

    def __init__(self):
        self.mem_instance = _build_mem0()
        pass

    def create(
            self,
            messages: List[Dict[str, str]],
            *,
            user_id: Optional[str] = None,
            agent_id: Optional[str] = None,
            thread_id: Optional[str] = None,
            metadata: Optional[Dict[str, Any]] = None,
            infer: bool = True,
            memory_type: Optional[str] = None,
            prompt: Optional[str] = None
    ) -> dict | None:
        """
        Create memory.
        """
        try:
            res = self.mem_instance.add(
                messages,
                user_id=user_id,
                agent_id=agent_id,
                run_id=thread_id,
                metadata=metadata,
                infer=infer,
                memory_type=memory_type,
                prompt=prompt
            )
            logger.info(f"MEMORY_CREATE_SUCCESS: {res}")
            return res
        except Exception as e:
            # If create message failed, will not throw exception
            logger.error(f"MEMORY_CREATE_FAILED: {e}", exc_info=True, stack_info=True)
            return None

    def search(
            self,
            query: str,
            *,
            user_id: Optional[str] = None,
            agent_id: Optional[str] = None,
            thread_id: Optional[str] = None,
            limit: int = 100,
            filters: Optional[Dict[str, Any]] = None,
            threshold: Optional[float] = None,
    ) -> Dict[str, Any]:
        try:
            return self.mem_instance.search(
                query,
                user_id=user_id,
                agent_id=agent_id,
                run_id=thread_id,
                limit=limit,
                filters=filters,
                threshold=threshold,
            )
        except Exception as e:
            logger.error(f"MEMORY_SEARCH_FAILED: {e}", exc_info=True, stack_info=True)
            return {}

class AgenticMemoryAsync:
    mem_instance: AsyncMemory

    def __init__(self, mem_instance: AsyncMemory):
        self.mem_instance = mem_instance
        pass

    @classmethod
    async def initialize(cls):
        # 异步工厂方法，用于创建 AgenticMemoryAsync 实例
        mem_instance = await _build_mem0_async()
        return cls(mem_instance)

    async def create(
            self,
            messages: List[Dict[str, str]],
            *,
            user_id: Optional[str] = None,
            agent_id: Optional[str] = None,
            thread_id: Optional[str] = None,
            metadata: Optional[Dict[str, Any]] = None,
            infer: bool = True,
            memory_type: Optional[str] = None,
            prompt: Optional[str] = None
    ) -> None:
        """
        Create memory.
        """
        try:
            await self.mem_instance.add(
                messages,
                user_id=user_id,
                agent_id=agent_id,
                run_id=thread_id,
                metadata=metadata,
                infer=infer,
                memory_type=memory_type,
                prompt=prompt
            )
        except Exception as e:
            # If create message failed, will not throw exception
            logger.error(f"MEMORY_CREATE_FAILED: {e}", exc_info=True, stack_info=True)

    async def search(
            self,
            query: str,
            *,
            user_id: Optional[str] = None,
            agent_id: Optional[str] = None,
            thread_id: Optional[str] = None,
            limit: int = 100,
            filters: Optional[Dict[str, Any]] = None,
            threshold: Optional[float] = None,
    ) -> Dict[str, Any]:
        try:
            return await self.mem_instance.search(
                query,
                user_id=user_id,
                agent_id=agent_id,
                run_id=thread_id,
                limit=limit,
                filters=filters,
                threshold=threshold,
            )
        except Exception as e:
            logger.error(f"MEMORY_SEARCH_FAILED: {e}", exc_info=True, stack_info=True)
            return {}