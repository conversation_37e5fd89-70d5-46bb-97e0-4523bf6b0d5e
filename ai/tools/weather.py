# weather
#
# @author: 崔晓波
# @email: <EMAIL>
# @date: 2025/6/18 14:35
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
from langchain_core.tools import tool
from pydantic import BaseModel


class Weather(BaseModel):
    """
    A standalone weather tool following the Tool Calling protocol.
    Input/Output schemas are self-contained for interoperability.
    """

    # city: Annotated[str, "City name, e.g. 'Beijing'"]
class WeatherResponse(BaseModel):
    conditions: str


@tool(name_or_callable="get_weather", description="Fetch current weather by city")
def get_weather(city: str) -> str:
    """Core tool logic (could call external API)"""
    return f"Weather in {city}: Sunny, 38°C"
