# time
#
# @author: 崔晓波
# @email: <EMAIL>
# @date: 2025/6/18 20:59
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
from langchain_core.tools import tool
from pydantic import BaseModel


class Time(BaseModel):
    """
    Time tool.
    """


class TimeResponse(BaseModel):
    timezone: str
    current_time: str


@tool(name_or_callable="get_time", description="Fetch current time by timezone")
def get_time(self, timezone: str) -> str:
    """Simulate time lookup."""
    return f"Current time in {timezone}: 12:00 PM"
