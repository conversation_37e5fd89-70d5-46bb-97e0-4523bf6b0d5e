from typing import Dict, Any

from langchain_core.tools import tool
from utils.biz_logger import get_logger

logger = get_logger(__name__)


class AptSlotTool:
    pass


@tool(description=" When the user want to make an appointment, please provide the hospital name and the start time and end time of the appointment.")
def get_apt_slot(start_time: str, end_time: str) -> list[dict[str, Any]]:
    result = [
        {
            "slot_id": "1",
            "hospital_name": "Hospital_1",
            "start_ime": start_time,
            "distance": "1KM"
        },
        {
            "slot_id": "2",
            "hospital_name": "Hospital_2",
            "start_ime": end_time,
            "distance": "20KM"
        }
    ]
    return result


@tool
def recommend_slot(slot_data: list[dict[str, Any]]) -> list[dict[str, Any]]:
    """
        When the user want to make an appointment, please recommend the hospital based on the slot data.
    """
    if not slot_data:
        logger.error(f"slot_data is empty: {slot_data}")
        return []

    card_result = []
    for i, slot_item in slot_data:
        card_data = {
            "hospital_name": slot_item.get("hospital_name"),
            "status": "book" if i == 0 else "unbook",
        }
        card_result.append(card_data)

    return card_result
