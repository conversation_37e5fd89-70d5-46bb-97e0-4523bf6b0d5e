from typing import Optional, Any, Generator
from langchain_core.messages import HumanMessage
from langchain_core.messages.ai import AIMessage

from ai.agent.react_agent import ReactAgent
from ai.config.ai_enum import TaskTypeEnum, AIPlatformEnum
from ai.router import Router
from ai.config.ai_config import ModelConfig, RouterConfig, FactoryConfig
from ai.tools.weather import Weather, get_weather
from utils.biz_logger import get_logger

logger = get_logger(__name__)


class AIManager:
    """
    AI 管理器，负责业务编排和消息组装
    """

    def __init__(self):
        pass

    def handle_by_router(
            self,
            message: str,
            task_type: str = "text",
            platform: str = "openai",
            image_url: Optional[str] = None
    ) -> Any:
        """
        通过路由处理AI请求
        :param message: 用户消息
        :param task_type: 任务类型（text/image）
        :param platform: AI平台（openai/deepseek）
        :param image_url: 图片URL（仅image任务）
        :return: AI响应
        """
        factory_config = FactoryConfig(platform=platform)
        model_config = ModelConfig()
        router_config = RouterConfig(task_type=task_type, platform=platform)
        messages = self._build_messages(message, task_type, image_url)
        ai_router = Router(router_config)
        ai_instance = ai_router.handle(factory_config=factory_config, model_config=model_config)
        response = ai_instance.invoke(messages)
        return response

    def _build_messages(self, message: str, task_type: str, image_url: Optional[str] = None) -> list:
        """
        根据任务类型组装消息
        """
        if task_type == TaskTypeEnum.TEXT:
            return [HumanMessage(content=message)]
        elif task_type == TaskTypeEnum.IMAGE:
            if not image_url:
                raise ValueError("Params empty: image_url")
            return [
                HumanMessage(
                    content=[
                        {"type": "text", "text": message},
                        {"type": "image_url", "image_url": {"url": image_url}}
                    ]
                )
            ]
        else:
            raise ValueError(f"Unsupported task_type: {task_type}")

    def handle_agent(self, biz_unique_id: str, biz_user_id: str, message: str) -> Any:
        logger.info(f"handle_agent_called: {message}")
        try:
            ai_router = Router(RouterConfig())
            factory_config = FactoryConfig()
            factory_config.platform = AIPlatformEnum.OPENAI
            ai_instance = ai_router.handle(factory_config=factory_config, model_config=ModelConfig())
            ai_agent = ReactAgent(
                model=ai_instance,
                store_type="postgres",
                tools=[get_weather],
                system_prompt="You are a weather specialist.",
                thread_id=biz_unique_id,
                biz_user_id=biz_user_id
            )

            # Run a agent
            response = ai_agent.run(message)
            logger.info(f"handle_agent_response: {response}")
            if not response or not response["messages"]:
                return None
            return response["messages"][-1].content
        except Exception as e:
            logger.error(f"handle_agent_error: {e}", exc_info=True)
            raise

    def handle_agent_stream(self, biz_unique_id: str, biz_user_id: str, message: str) -> Any:
        """
        流式处理 agent 请求，返回生成器，每次输出一个字符，只输出 AIMessage
        """
        logger.info(f"handle_agent_stream_called: {message}")
        try:
            ai_router = Router(RouterConfig())
            factory_config = FactoryConfig()
            factory_config.platform = AIPlatformEnum.OPENAI
            ai_instance = ai_router.handle(factory_config=factory_config, model_config=ModelConfig())
            ai_agent = ReactAgent(
                model=ai_instance,
                store_type="postgres",
                tools=[get_weather],
                system_prompt="You are a weather specialist.",
                thread_id=biz_unique_id,
                biz_user_id=biz_user_id
            )

            # 流式运行 agent，提取 AI 回复内容并逐字符输出，只输出 AI的内容
            for chunk in ai_agent.run_stream(message):
                yield chunk
        except Exception as e:
            logger.error(f"handle_agent_stream_error: {e}", exc_info=True)
            raise
