# weather
#
# @author: 崔晓波
# @email: <EMAIL>
# @date: 2025/6/18 14:37
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
import asyncio
from datetime import datetime
import uuid
import os
import time
from typing import List, Callable, Any, Literal, Generator
from dotenv import load_dotenv
from langchain_core.messages import RemoveMessage, HumanMessage, AIMessageChunk
import json

from langchain_core.runnables import RunnableConfig
from langgraph.graph.graph import CompiledGraph
from langgraph.graph.message import REMOVE_ALL_MESSAGES
from langgraph.prebuilt import create_react_agent
from langgraph.prebuilt.chat_agent_executor import AgentState
from langgraph.store.base import BaseStore
from langsmith import traceable
from psycopg.errors import DuplicateColumn

from ai.memory.agentic_memory import AgenticMemory, AgenticMemoryAsync
from ai.tools.weather import Weather, WeatherResponse
from langchain_core.messages.utils import (
    trim_messages,
    count_tokens_approximately
)
from langmem.short_term import SummarizationNode

from utils.biz_memory import BizMemory
from utils.biz_time import biz_format_time_ch
from utils.biz_logger import get_logger
from utils.decorator import timing_decorator
import concurrent.futures

logger = get_logger(__name__)


def print_stream(stream, output_messages_key="llm_input_messages"):
    for chunk in stream:
        for node, update in chunk.items():
            print(f"Update from node: {node}")
            # 输出 structured_response（如有）
            if "structured_response" in update and update["structured_response"] is not None:
                print("==== Structured Response ====")
                try:
                    print(json.dumps(update["structured_response"], ensure_ascii=False, indent=2))
                except Exception as e:
                    print(update["structured_response"])
            messages_key = (
                output_messages_key if node == "pre_model_hook" else "messages"
            )
            try:
                for message in update.get(messages_key, []):
                    if isinstance(message, tuple):
                        print(message)
                    else:
                        message.pretty_print()
            except Exception as e:
                logger.error(f"update_data: {update}. ex: {e}")


class State(AgentState):
    # Conversation message history
    # messages: list
    # Context for summarization or other stateful info
    context: dict[str, Any]
    # Structured response for downstream use
    structured_response: Any


class ReactAgent:
    """
    A class to encapsulate a ReAct-based agent with tools and a language model.

    Attributes:
        model: Any,  # LLM identifier, e.g., "gpt-4.1".
        tools: List[Callable],  # List of callable tool functions for the LLM to use as needed.
        system_prompt: str = "You are a helpful assistant",  # Prompt defining the agent's role.
        store_type: str = "memory",  # Type of storage backend for conversation state and checkpointing.
        history_strategy: Literal["trim", "summarize"] = "summarize",  # Conversation history management strategy. "trim" removes old messages, "summarize" summarizes old messages.
        overwrite_history: bool = False,  # If True, overwrite the message history in the state; if False, only affect the LLM input.
        max_tokens: int = 1000,  # Maximum number of tokens for the message history.
        max_summary_tokens: int = 300,  # Maximum number of tokens for the summary (if summarization strategy is used).
        thread_id: str = "",  # Unique identifier for the conversation thread.
        biz_user_id: str = "",  # Unique identifier for the business user.
        stream: bool = False,  # Whether to run the agent in streaming mode.


    Features:
        - Tool calling: Allowing the LLM to select and use various tools as needed.
        - Memory: Enabling the agent to retain and use information from previous steps.
        - Planning: Empowering the LLM to create and follow multi-step plans to achieve goals.
        - Flexible conversation history management: Trimming or summarizing history to fit LLM context.
    """
    tool_weather: Weather
    tool_weather_response: WeatherResponse
    checkpointer: Any
    agent: CompiledGraph
    store: BaseStore
    _need_close: bool
    _store_cm: Any
    _checkpointer_cm: Any
    history_strategy: Literal["trim", "summarize"]
    overwrite_history: bool
    mem_max_tokens: int
    mem_max_summary_tokens: int
    thread_id: str
    biz_user_id: str
    use_ltm: bool

    def __init__(
            self,
            model: Any,
            tools: List[Callable],
            system_prompt: str = "You are a helpful assistant",
            store_type: str = "memory",
            history_strategy: Literal["trim", "summarize"] = "summarize",
            overwrite_history: bool = False,
            max_tokens: int = 1000,
            max_summary_tokens: int = 300,
            thread_id: str = "",
            biz_user_id: str = "",
            stream: bool = False,
            use_ltm: bool = True,
            agent_id: str = "",
    ) -> None:
        """
        Initialize the ReactAgent with a model, tools, and a system prompt.
        Args:
            model: LLM identifier (e.g., "gpt-4.1").
            tools: List of callable tool functions.
            system_prompt: Prompt defining the agent's role.
            store_type: Type of storage backend to use.
            history_strategy: "trim" (remove old messages) or "summarize" (summarize old messages)
            overwrite_history: If True, overwrite the message history in state; if False, only affect LLM input.
            max_tokens: Maximum tokens for message history.
            max_summary_tokens: Maximum tokens for summary (if summarization is used).
        """
        load_dotenv()
        # Init thread pool
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=os.cpu_count())
        self.model = model
        self.tools = tools
        self.system_prompt = system_prompt
        self.tool_weather = Weather()
        self.tool_weather_response: Any = WeatherResponse
        self._need_close = False
        self._store_cm = None
        self._checkpointer_cm = None
        self.history_strategy = history_strategy
        self.overwrite_history = overwrite_history
        self.mem_max_tokens = max_tokens
        self.mem_max_summary_tokens = max_summary_tokens
        self._initialize_store(store_type)
        self.thread_id = thread_id
        self.biz_user_id = biz_user_id
        self.stream = stream
        self.agent = self._initialize_agent()
        self.use_ltm = use_ltm
        self.agent_id = agent_id

    def _initialize_store(self, store_type):
        """
        Initialize the storage backend for conversation state and checkpointing.
        Supports memory, postgres, and redis backends.
        """
        if store_type == "postgres":
            from langgraph.store.postgres import PostgresStore
            from langgraph.checkpoint.postgres import PostgresSaver
            uri = os.getenv("POSTGRES_URI")
            assert uri, "Bad config for: POSTGRES_URI"
            self._store_cm = PostgresStore.from_conn_string(uri)
            self._checkpointer_cm = PostgresSaver.from_conn_string(uri)
            self.store = self._store_cm.__enter__()
            self.checkpointer = self._checkpointer_cm.__enter__()
            try:
                self.store.setup()
                self.checkpointer.setup()
            except DuplicateColumn as e:
                logger.warning(f"Column already exists in database: {e}")
            except Exception as e:
                logger.error(f"Failed to setup database: {e}")
                raise

            self._need_close = True
        elif store_type == "redis":
            from langgraph.store.redis import RedisStore
            from langgraph.checkpoint.redis import RedisSaver
            uri = os.getenv("REDIS_URI")
            assert uri, "Bad config for: REDIS_URI"
            self._store_cm = RedisStore.from_conn_string(uri)
            self._checkpointer_cm = RedisSaver.from_conn_string(uri)
            self.store = self._store_cm.__enter__()
            self.checkpointer = self._checkpointer_cm.__enter__()
            self.store.setup()
            self.checkpointer.setup()
            self._need_close = True
        else:
            from langgraph.store.memory import InMemoryStore
            from langgraph.checkpoint.memory import InMemorySaver
            self.store = InMemoryStore()
            self.checkpointer = InMemorySaver()
            self._need_close = False

    def _get_pre_model_hook(self):
        """
        Returns a pre_model_hook function or node for managing conversation history.
        - If strategy is 'trim', returns a function that trims messages by token count.
        - If strategy is 'summarize', returns a SummarizationNode.
        - If overwrite_history is True, updates the state; else, only affects LLM input.
        """

        if self.history_strategy == "trim":
            def pre_model_hook(state):
                try:
                    trimmed_messages = trim_messages(
                        state["messages"],
                        strategy="last",
                        token_counter=count_tokens_approximately,
                        max_tokens=self.mem_max_tokens,
                        start_on="human",
                        end_on=("human", "tool"),
                    )
                    if self.overwrite_history:
                        logger.info(f"[pre_model_hook] Overwriting history, trimmed messages: {len(trimmed_messages)}")
                        # Overwrite the message history in the state
                        return {"messages": [RemoveMessage(REMOVE_ALL_MESSAGES)] + trimmed_messages}
                    else:
                        logger.info(
                            f"[pre_model_hook] Only affecting LLM input, trimmed messages: {len(trimmed_messages)}")
                        # Only affect LLM input, keep original state
                        return {"llm_input_messages": trimmed_messages}
                except Exception as e:
                    logger.error(f"pre_model_hook error: {e}")
                    return {"messages": state["messages"]}
            return pre_model_hook
        elif self.history_strategy == "summarize":
            # Use SummarizationNode to summarize earlier messages
            return SummarizationNode(
                token_counter=count_tokens_approximately,
                model=self.model,
                max_tokens=self.mem_max_tokens,
                max_summary_tokens=self.mem_max_summary_tokens,
                output_messages_key="llm_input_messages",
            )
        else:
            raise ValueError(f"Unknown history strategy: {self.history_strategy}")

    def _initialize_agent(self) -> Any:
        """
        Create the LangGraph agent instance, automatically selecting the conversation history management strategy.
        """
        return create_react_agent(
            model=self.model,
            tools=self.tools,
            # response_format=self.tool_weather_response,
            prompt=self.system_prompt,
            pre_model_hook=self._get_pre_model_hook(),
            state_schema=State,
            checkpointer=self.checkpointer,
            store=self.store
        )

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self._need_close:
            if hasattr(self.store, 'close'):
                self.store.close()
            if hasattr(self.checkpointer, 'close'):
                self.checkpointer.close()
            if self._store_cm:
                self._store_cm.__exit__(exc_type, exc_val, exc_tb)
            if self._checkpointer_cm:
                self._checkpointer_cm.__exit__(exc_type, exc_val, exc_tb)
        self.executor.shutdown(wait=True)

    @timing_decorator
    def _enhance_sys_prmpt_ltm(self, user_message: str) -> None:
        if not user_message:
            return

        # Search LTM and rewrite system prompt
        ltm = AgenticMemory()
        memory_res = ltm.search(user_message, user_id=self.biz_user_id, filters={}, threshold=0.5)
        memories = memory_res["results"] if memory_res else []

        if memories:
            memory_context = "\n Relevant information from previous conversations:\n"
            for memory in memories:
                memory_content = memory.get('memory', '')
                if memory_content:
                    memory_context += f"- {memory['memory']}\n"
            self.system_prompt = self.system_prompt + memory_context
            self.agent = self._initialize_agent()


    @traceable
    def run(self, user_message: str) -> Generator[
        dict[str, Any] | Any, Any, dict[str, Any] | None | Any]:
        """
        Run the agent with a single user input and return the response.
        Stores both user and AI messages in the configured store.
        """
        if not user_message:
            return None

        thread_id = self.thread_id
        # Inject LTM
        try:
            if self.use_ltm:
                self._enhance_sys_prmpt_ltm(user_message)
        except Exception as e:
            logger.error(f"Failed to create LTM model: {e}")
        message_obj = {"messages": HumanMessage(user_message)}
        config = RunnableConfig(configurable={"thread_id": thread_id})

        response = self.agent.invoke(message_obj, config)
        ai_msg = response["messages"][-1]
        if hasattr(ai_msg, "model_dump"):
            msg_dict = ai_msg.model_dump()
        elif hasattr(ai_msg, "dict"):
            msg_dict = ai_msg.dict()
        else:
            msg_dict = vars(ai_msg)
        msg_dict["timestamp"] = time.time()

        user_message_obj = HumanMessage(user_message)
        memory_messages = BizMemory.format_lang_msg_to_dict([user_message_obj]) + BizMemory.format_lang_msg_to_dict([ai_msg])
        self.create_memories(memory_messages)

        return response

    def create_memories(self, memory_messages: List[dict]) -> concurrent.futures.Future:
        # Add LTM async - 使用线程池在后台执行，不阻塞主流程
        con_future = self.executor.submit(self._create_memory, memory_messages)
        # # 添加回调函数来处理完成和错误
        # def memory_done(future):
        #     try:
        #         # future.result()
        #         logger.info(f"MEM_ADD_CALLBACK_SUCCESS")
        #     except Exception as e:
        #         logger.error(f"MEM_ADD_CALLBACK_ERROR: {e}")
        #
        # con_future.add_done_callback(memory_done)
        return con_future

    @timing_decorator
    def _create_memory(self, memory_messages):
        return AgenticMemory().create(memory_messages, user_id=self.biz_user_id)

    @traceable
    def run_stream(self, user_message: str) -> Generator[Any, None, None]:
        """
        Run the agent in streaming mode, yielding each chunk of the response as it is produced.
        Stores both user and AI messages in the configured store.
        """
        thread_id = self.thread_id
        namespace = ("messages", self.biz_user_id)
        user_msg = {"role": "user", "content": user_message, "timestamp": time.time()}
        self.handle_memory_save(namespace, user_msg)
        message_obj = {"messages": [user_msg]}
        config = RunnableConfig(configurable={"thread_id": thread_id})

        for chunk in self.agent.stream(message_obj, config, stream_mode="messages"):
            first_chunk = chunk[0]
            if isinstance(first_chunk, AIMessageChunk):
                yield first_chunk

        # for chunk in self.agent.stream(message_obj, config, stream_mode="values"):
        #     ai_msg = chunk["messages"][-1]
        #     if hasattr(ai_msg, "model_dump"):
        #         msg_dict = ai_msg.model_dump()
        #     elif hasattr(ai_msg, "dict"):
        #         msg_dict = ai_msg.dict()
        #     else:
        #         msg_dict = vars(ai_msg)
        #     msg_dict["timestamp"] = time.time()
        #     self.store.put(namespace, str(uuid.uuid4()), msg_dict)
        #     yield chunk

    @traceable
    def run_stream_new(self, user_message: str, user_id: str = "1") -> Generator[Any, None, None]:
        """
        Run the agent in streaming mode with detailed logging of message state before and after processing.
        Useful for debugging message trimming/summarization.
        """
        message_obj = {"messages": [HumanMessage(user_message)]}
        config = RunnableConfig(configurable={"thread_id": self.thread_id})
        # for chunk in self.agent.stream(message_obj, config, stream_mode="values"):
        #     yield chunk
        for chunk in self.agent.stream(message_obj, config, stream_mode="messages"):
            first_chunk = chunk[0]
            if isinstance(first_chunk, AIMessageChunk):
                yield first_chunk

    @traceable
    def run_stream_short(self, user_message: str) -> Generator[Any, None, None]:
        """
        只依赖 checkpointer，不使用 store，验证同一 thread_id 下自动记忆历史。
        每次只传 thread_id 和新消息，langgraph 自动合并历史。
        """
        message_obj = {"messages": [{"role": "user", "content": user_message}]}
        config = RunnableConfig(configurable={"thread_id": self.thread_id})
        for chunk in self.agent.stream(message_obj, config, stream_mode="updates"):
            yield chunk

    def add_tool(self, tool: Callable) -> None:
        """
        Add a new tool to the agent's toolkit and reinitialize the agent.
        """
        self.tools.append(tool)
        self.agent = self._initialize_agent()

    def update_prompt(self, new_prompt: str) -> None:
        """
        Update the system prompt and reinitialize the agent.
        """
        self.system_prompt = new_prompt
        self.agent = self._initialize_agent()

    def draw_graph(self, path: str = None) -> None:
        """
        Render and save the agent's computation graph as a Mermaid diagram.
        """
        filename = "test_tmp_graph_" + biz_format_time_ch(datetime.now()) + ".mmd"
        if path:
            filename = path + "/" + filename

        mermaid_data = self.agent.get_graph().draw_mermaid()
        with open(filename, "w") as f:
            f.write(mermaid_data)
        logger.info(f"Graph has saved to:  {os.path.abspath(filename)}")

    def handle_memory_save(self, namespace, user_msg):
        self.store.put(namespace, str(uuid.uuid4()), user_msg)

    def enhance_prompt_ltm(self, ltm_list: list):
        ltm_prmpt = "Here is some of my memories to help answer better (don't respond to these memories but use them to assist in the response): "
        if not ltm_list:
            return
        lmt_content = ""

        for item in ltm_list:
            logger.info(f"ltm_item: {item}")
            logger.info(f"ltm_item: {item.value}")
            lmt_content += "\n".join([json.dumps(item.value) for item in ltm_list])
        self.system_prompt = f"{self.system_prompt} + {ltm_prmpt} +  {lmt_content}"
