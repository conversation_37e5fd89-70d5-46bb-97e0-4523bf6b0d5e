# State
#
# @author: 崔晓波
# @email: <EMAIL>
# @date: 2025/6/17 14:49
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD

from typing import Annotated

from langgraph.graph import StateGraph
from langgraph.graph.message import add_messages


class State:
    # Messages have the type "list". The `add_messages` function
    # in the annotation defines how this state key should be updated
    # (in this case, it appends messages to the list, rather than overwriting them)
    messages: Annotated[list, add_messages]


graph_builder = StateGraph(State)
