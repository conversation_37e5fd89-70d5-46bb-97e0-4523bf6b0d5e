image: docker:latest  # this sets default image for jobs
services:
  - docker:dind
stages:
  - build

variables:
  CI_COMMIT_TAG: ${CI_COMMIT_TAG}
  CONTAINER_TAG: ${CI_COMMIT_SHORT_SHA}
  DOCKER_DRIVER: overlay2
  DOCKER_REGISTRY: public.ecr.aws/i1v5c3z5
  DOCKER_TLS_CERTDIR: ""
  IMAGE_SCAN: "N"

build:
  stage: build
  only:
    - /^(dev|test|uat|prod)+_.*$/
#    - /^(dev|test|uat|prod)+-\d+\.\d+\.\d$/
  image:
    name: evyd/base-ci-build:ci-build01
#    name: base-ci-build:build-openjdk_11.0.6
    entrypoint: [""]
  script:
    - chmod +x ci/build-image.sh
    - bash ci/build-image.sh