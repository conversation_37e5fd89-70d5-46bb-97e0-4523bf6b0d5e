# evo-mind
> EVYD AI server 

## Framework
> Wiki: https://evyd.atlassian.net/wiki/spaces/EVYD/pages/812089638/POC+Analysis#Framework-Future

![Framework](./docs/images/framework.png)

## Development
### Clone Code
```shell
git clone https://git.evyd.tech/evyd-ai/evo-mind.git
```

### Install UV(https://docs.astral.sh/uv/)
```shell
# Methods
curl -LsSf https://astral.sh/uv/install.sh | sh

# Or
brew install uv

```

### Sync Project
> In project root path
```shell
cd ${root_path}

uv sync 
```
### Add Dependencies(Please use uv)
```shell
uv add langgraph
```

### Init ENV
```shell
copy env-example .env
```

### Debugging
- Pycharm
  - ![Pycharm Debug](./docs/images/debug_pycharm.png)

## Project Structure
```shell
├── Dockerfile                # 项目容器化部署配置文件
├── README.md                 # 项目说明文档，包含安装、使用等信息
├── ai                        # AI核心逻辑模块
│   ├── __init__.py           # 包初始化文件
│   ├── agent                 # 智能体相关实现
│   │   └── __init__.py       # agent子包初始化
│   ├── factory.py            # AI工厂类，负责模型实例化
│   ├── langgraph_core.py    # LangGraph核心逻辑
│   ├── langgraph-demo.py     # LangGraph相关的演示代码
│   ├── router.py             # AI路由分发逻辑，支持多平台/模型
│   └── state.py              # AI状态管理相关代码
├── api                       # API接口层
│   ├── __init__.py           # 包初始化文件
│   └── v1                    # v1版本API
│       ├── __init__.py       # v1子包初始化
│       ├── chat.py           # 聊天相关接口
│       └── test.py           # 测试接口
├── config                    # 配置相关模块
│   ├── __init__.py           # 包初始化文件
│   ├── ai_config.py          # AI模型、工厂等配置类
│   └── ai_properties.py      # AI平台、API Key等属性配置
├── db                        # 数据库相关模块
│   ├── __init__.py           # 包初始化文件
│   └── model
│       └── __init__.py       # 数据库模型初始化
├── demo.py                   # 示例脚本或调试入口
├── docker                    # Docker相关辅助文件目录
├── docs                      # 项目文档目录
│   └── images
│       └── debug_pycharm.png # 调试相关图片
├── env-example               # 环境变量示例文件
├── job
│   └── __init__.py           # 任务调度或定时任务相关代码
├── main.py                   # 项目主入口，FastAPI应用启动文件
├── pyproject.toml            # Python项目依赖与元数据配置
├── service                   # 业务服务层
│   ├── __init__.py           # 包初始化文件
│   └── ai_service.py         # AI服务封装，负责业务逻辑与AI调用
├── tests                     # 测试用例目录
│   ├── __init__.py           # 包初始化文件
│   └── test_chat_api.py      # 聊天API相关测试
├── utils                     # 工具类模块
│   ├── __init__.py           # 包初始化文件
│   ├── config.py             # 配置加载工具
│   └── biz_logger.py             # 日志工具
└── uv.lock                   # 依赖锁定文件，保证环境一致性
```
