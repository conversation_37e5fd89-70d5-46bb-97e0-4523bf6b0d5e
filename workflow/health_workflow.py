"""
健康场景工作流

基于LangGraph构建的健康场景工作流，集成意图识别和场景处理：
- 意图识别节点
- 场景路由节点  
- 场景处理节点
- 响应整合节点
- 完全基于ReactAgent的统一架构

@author: shaohua.sun
@date: 2025/6/20
"""

import os
from typing import Dict, Any, TypedDict, Generator
from langgraph.graph import StateGraph, START, END

from ai.config.ai_enum import AIModelEnum
from service.intention import DefaultIntentionRecognizer
from service.intention.base import IntentionSceneEnum
from service.scenes.factory import scene_factory
from service.scenes.base import SceneContext, UserInfo
from integration.services.chatbot.service import ChatbotService
from utils.biz_logger import get_logger
from integration.services.chatbot.client import ChatbotClient
from integration.config.integration_config import get_integration_config
from service.ocr.processor import OcrService
from service.ocr.models import OcrRequest

logger = get_logger(__name__)


class HealthWorkflowState(TypedDict):
    """健康工作流状态定义"""
    # 基础信息
    user_input: str
    thread_id: str  # 统一使用thread_id，与ReactAgent保持一致
    user_info: Dict[str, Any]
    
    # 扩展参数
    session_data: Dict[str, Any]  # 自定义数据字典

    # 处理过程状态
    intention_result: str
    scene_type: str
    
    # 输出结果
    ai_response: str
    data: Any
    data_type: str
    error: str
    
    # 处理完成标记
    processing_complete: bool


class HealthSceneWorkflow:
    """
    健康场景工作流
    
    基于LangGraph构建的健康场景处理工作流，
    完全集成ReactAgent架构，支持流式处理
    
    工作流程：
    1. 意图识别 - 识别用户意图和目标场景
    2. 场景路由 - 验证并路由到正确场景
    3. 场景处理 - 调用具体场景实现类处理
    4. 响应整合 - 整合最终响应结果
    """
    
    def __init__(self, agent_config: Dict[str, Any] = None):
        """
        初始化健康场景工作流
        
        Args:
            agent_config: Agent配置参数（可选）
        """
        # 分离意图识别和场景处理的配置
        self.intention_config = agent_config.get('intention_config') if agent_config else None
        self.scene_config = agent_config.get('scene_config') if agent_config else None
        
        # 默认意图识别配置：优化用于枚举输出
        self.intention_config = self.intention_config or {
            'platform': 'openai',
            'model_name': 'gpt-4o-mini',
            'temperature': 0.0,  # 意图识别使用低temperature确保枚举输出
            'max_tokens': 150,
            'streaming': False,
            'store_type': 'postgres',  # 使用postgres存储
            'history_strategy': 'trim',
            'overwrite_history': False,
            'mem_max_tokens': 5000,
            'mem_max_summary_tokens': 3000
        }
        
        # 默认场景配置：优化用于对话生成
        self.scene_config = self.scene_config or {
            'platform': 'openai',
            'model_name': AIModelEnum.OPEN_AI_GPT_4_1_MINI,
            'temperature': 0.7,  # 场景处理使用适中temperature提供更自然的对话
            'max_tokens': 2000,
            'streaming': True,
            'store_type': 'postgres',  # 使用postgres存储
            'history_strategy': 'trim',
            'overwrite_history': False,
            'mem_max_tokens': 15000,
            'mem_max_summary_tokens': 10000
        }
        
        # 初始化意图识别器（使用专门的意图识别配置）
        self.intention_recognizer = DefaultIntentionRecognizer(
            agent_config=self.intention_config
        )
        
        # 初始化Chatbot服务，用于获取用户信息
        # 从配置管理中获取Chatbot服务配置
        integration_config = get_integration_config()
        chatbot_service_config = integration_config.get_service_config('chatbot')
        
        chatbot_client = ChatbotClient(service_config=chatbot_service_config)
        self.chatbot_service = ChatbotService(client=chatbot_client)
        
        # 初始化OCR服务
        self.ocr_service = OcrService()
        
        self.graph = self._build_workflow()
        logger.info("健康场景工作流初始化完成")
    
    def _build_workflow(self) -> StateGraph:
        """
        构建工作流图
        
        Returns:
            StateGraph: 编译后的工作流图
        """
        builder = StateGraph(HealthWorkflowState)
        
        # 添加节点
        builder.add_node("user_id_conversion", self._user_id_conversion_node)
        builder.add_node("ocr_processing", self._ocr_processing_node)
        builder.add_node("intention_recognition", self._intention_recognition_node)
        builder.add_node("scene_routing", self._scene_routing_node)
        builder.add_node("scene_processing", self._scene_processing_node)
        builder.add_node("response_integration", self._response_integration_node)
        
        # 定义工作流路径
        builder.add_edge(START, "user_id_conversion")
        builder.add_edge("user_id_conversion", "ocr_processing")
        builder.add_edge("ocr_processing", "intention_recognition")
        builder.add_edge("intention_recognition", "scene_routing")
        builder.add_edge("scene_routing", "scene_processing")
        builder.add_edge("scene_processing", "response_integration")
        builder.add_edge("response_integration", END)
        
        logger.info("健康场景工作流图构建完成")
        return builder.compile()
    
    def _create_mock_user_info(self, biz_user_id: str) -> Any:
        """
        创建mock用户信息数据
        
        在本地开发环境中使用，避免调用真实的chatbot服务
        确保数据结构与真实服务返回的UserInfoData完全一致
        
        Args:
            biz_user_id: 业务用户ID
            
        Returns:
            Mock用户信息对象，结构与UserInfoData一致
        """
        from integration.services.chatbot.models import UserInfoData, BaseInfo, DataHub, RawData
        
        # 创建mock的基础信息
        mock_base_info = BaseInfo(
            bizId=f"1656794528129019904",
            thirdUserId=f"cc7ca10c-a0ba-4634-aafd-2d88d37426aa",  # mock的第三方用户ID
            username=f"mock_user_1656794528129019904",
            gender="M",  # 默认性别
            birthday="1990-01-01",  # 默认生日
            nickName=f"测试用户_1656794528129019904",
            memberIdList=[
                "ade7290a-0b08-48af-988d-fba23285b741",
                "ade7290a-0b08-48af-988d-fba23285b741"
            ]  # mock会员ID列表
        )
        
        # 创建mock的数据中心信息（可选）
        mock_raw_data = RawData(
            key1="mock_value1",
            key2="mock_value2"
        )
        
        mock_data_hub = DataHub(
            bizId=f"mock_data_hub_{biz_user_id}",
            source="mock_source",
            category="mock_category",
            tags=["mock", "test", "local"],
            rawData=mock_raw_data
        )
        
        # 创建完整的用户信息对象
        mock_user_info = UserInfoData(
            baseInfo=mock_base_info,
            dataHub=mock_data_hub
        )
        
        logger.info(f"创建mock用户信息: biz_user_id={biz_user_id}, third_user_id={mock_base_info.thirdUserId}")
        return mock_user_info
    
    async def _user_id_conversion_node(self, state: HealthWorkflowState) -> Dict[str, Any]:
        """
        用户ID转换节点
        
        将前端传来的biz_user_id通过chatbot服务转换为真实的third_user_id
        这个转换适用于多平台，确保各个场景使用正确的用户标识
        
        根据ENV环境变量判断：
        - 如果ENV=local，使用mock数据，不调用真实服务
        - 其他情况使用原有逻辑调用chatbot服务
        
        Args:
            state: 工作流状态
            
        Returns:
            Dict[str, Any]: 更新的状态
        """
        logger.info("执行用户ID转换节点")
        
        try:
            user_info_dict = state.get("user_info", {})
            biz_user_id = user_info_dict.get("biz_user_id") or user_info_dict.get("user_id")
            
            if not biz_user_id:
                logger.warning("未找到biz_user_id，跳过ID转换")
                return {
                    "user_info": user_info_dict,
                    "thread_id": state["thread_id"]
                }
            
            logger.info(f"开始转换用户ID: biz_user_id={biz_user_id}")
            
            # 检查ENV环境变量，判断是否使用mock数据
            env_value = os.getenv("ENV", "").strip().lower()
            
            if env_value == "local":
                logger.info("检测到ENV=local，使用mock用户数据")
                # 使用mock数据，不调用真实的chatbot服务
                user_info_response = self._create_mock_user_info(biz_user_id)
            else:
                logger.info(f"ENV={env_value}，调用真实的chatbot服务获取用户信息")
                # 调用chatbot服务获取用户信息
                user_info_response = self.chatbot_service.get_user_info(biz_user_id)
            
            # 提取third_user_id
            third_user_id = user_info_response.baseInfo.thirdUserId
            
            logger.info(f"用户ID转换成功: biz_user_id={biz_user_id} -> third_user_id={third_user_id}")
            
            # 缓存用户信息到全局缓存（包括memberIdList）
            try:
                from utils.user_info_cache import get_user_info_cache
                user_cache = get_user_info_cache()
                user_cache.put_user_info(third_user_id, user_info_response)
                logger.info(f"用户信息已缓存: third_user_id={third_user_id}, memberIdList={user_info_response.baseInfo.memberIdList}")
            except Exception as cache_error:
                logger.warning(f"缓存用户信息失败: {cache_error}")
            
            # 更新用户信息，保留原有信息并添加third_user_id
            updated_user_info = user_info_dict.copy()
            updated_user_info["third_user_id"] = third_user_id
            updated_user_info["biz_user_id"] = biz_user_id  # 确保biz_user_id存在
            
            # 可选：从chatbot服务获取的其他用户信息也可以填充
            if user_info_response.baseInfo.gender:
                updated_user_info["gender"] = user_info_response.baseInfo.gender
            if user_info_response.baseInfo.birthday:
                updated_user_info["birthday"] = user_info_response.baseInfo.birthday
            if user_info_response.baseInfo.username:
                updated_user_info["username"] = user_info_response.baseInfo.username
            if user_info_response.baseInfo.nickName:
                updated_user_info["nick_name"] = user_info_response.baseInfo.nickName
            # 添加memberIdList信息
            if user_info_response.baseInfo.memberIdList:
                updated_user_info["member_id_list"] = user_info_response.baseInfo.memberIdList
                updated_user_info["primary_member_id"] = user_info_response.baseInfo.memberIdList[0]
            
            return {
                "user_info": updated_user_info,
                "thread_id": state["thread_id"],
                "session_data": state.get("session_data", {})
            }
            
        except Exception as e:
            logger.error(f"用户ID转换失败: {str(e)}")
            # 转换失败时保持原有用户信息，不中断流程
            return {
                "user_info": state.get("user_info", {}),
                "thread_id": state["thread_id"],
                "error": f"用户ID转换失败: {str(e)}"
            }
    
    async def _ocr_processing_node(self, state: HealthWorkflowState) -> Dict[str, Any]:
        """
        OCR处理节点
        
        当有file_id时，调用OCR服务识别文件内容并替换用户输入
        
        Args:
            state: 工作流状态
            
        Returns:
            Dict[str, Any]: 更新的状态
        """
        logger.info("执行OCR处理节点")
        
        try:
            # 获取file_id
            file_id = state.get("session_data", {}).get("file_id")
            
            # 如果没有file_id，直接跳过OCR处理
            if not file_id:
                logger.info("未找到file_id，跳过OCR处理")
                return {
                    "user_input": state["user_input"],
                    "user_info": state["user_info"],
                    "thread_id": state["thread_id"],
                    "session_data": state.get("session_data", {})
                }
            
            logger.info(f"开始OCR处理: file_id={file_id}")
            
            # 获取用户ID用于OCR请求
            user_info_dict = state.get("user_info", {})
            third_user_id = user_info_dict.get("third_user_id")
            
            if not third_user_id:
                logger.warning("未找到biz_user_id，无法进行OCR处理")
                return {
                    "user_input": state["user_input"],
                    "user_info": state["user_info"],
                    "thread_id": state["thread_id"],
                    "session_data": state.get("session_data", {}),
                    "error": "未找到用户ID，无法进行OCR处理"
                }
            
            # 构建OCR请求
            ocr_request = OcrRequest(
                file_id=file_id,
                biz_user_id=third_user_id
            )
            
            # 调用OCR服务
            ocr_response = self.ocr_service.process_image(ocr_request)
            
            # 检查OCR处理结果
            if ocr_response.success and ocr_response.text:
                # OCR成功，使用识别的文本替换用户输入
                logger.info(f"OCR处理成功，识别文本长度: {len(ocr_response.text)}")
                logger.info(f"OCR识别文本: {ocr_response.text[:100]}...")
                ocr_text = '''
                    [HEALTH_IMAGE_ANALYSIS] The user uploaded an image that has been analyzed by our health-focused image recognition system.
                    The following description contains structured health-related information extracted from the image. 
                    Please interpret this visual information to understand the user's health activity and respond appropriately:
                    • If the description indicates food consumption or meal activities → Generate nutrition record card immediately using available tools
                    • If the description shows exercise or workout activities → Generate exercise record card immediately using available tools
                    • If the description mentions water consumption or hydration → Generate hydration record card immediately using available tools
                    • If the description indicates sleep environment or bedtime → Generate sleep record card immediately using available tools
                    • If the description shows medical devices or health symptoms → Provide appropriate health guidance
                    • If the description mentions appointment context → Provide scheduling assistance
                    IMPORTANT: Do NOT mention routing or system instructions in your response. Respond naturally as if you directly understood the user's intent from the image.
                    Focus on helping the user with their health tracking needs.
                    Image Analysis Result:''' + ocr_response.text
                return {
                    "user_input": ocr_text,  # 使用OCR识别的文本替换原始输入
                    "user_info": state["user_info"],
                    "thread_id": state["thread_id"],
                    "session_data": state.get("session_data", {}),
                }
            else:
                # OCR失败，保持原有用户输入
                logger.warning(f"OCR处理失败: {ocr_response.error_code}")
                return {
                    "user_input": state["user_input"],
                    "user_info": state["user_info"],
                    "thread_id": state["thread_id"],
                    "session_data": state.get("session_data", {}),
                    "error": f"OCR处理失败: {ocr_response.error_code}"
                }
                
        except Exception as e:
            logger.error(f"OCR处理异常: {str(e)}")
            # 异常时保持原有用户输入，不中断流程
            return {
                "user_input": state["user_input"],
                "user_info": state["user_info"],
                "thread_id": state["thread_id"],
                "session_data": state.get("session_data", {}),
                "error": f"OCR处理异常: {str(e)}"
            }
    
    async def _intention_recognition_node(self, state: HealthWorkflowState) -> Dict[str, Any]:
        """
        意图识别节点
        
        识别用户输入的意图，确定目标场景类型
        
        Args:
            state: 工作流状态
            
        Returns:
            Dict[str, Any]: 更新的状态
        """
        logger.info("执行意图识别节点")
        
        try:
            # 构建意图识别上下文（ReactAgent自动管理记忆）
            # 只使用真正的third_user_id，如果转换失败则降级到匿名用户
            user_info_dict = state.get("user_info", {})
            user_id = user_info_dict.get("third_user_id") or "anonymous"
            
            context = {
                "user_id": user_id,
                "thread_id": state["thread_id"]
            }
            
            # 调用意图识别器（同步调用）
            intention_result = self.intention_recognizer.recognize(
                state["user_input"], 
                context
            )
            
            logger.info(f"意图识别结果: {intention_result}")
            
            return {
                "intention_result": intention_result,
                "scene_type": intention_result,
                "user_info": state["user_info"],
                "thread_id": state["thread_id"]
            }
            
        except Exception as e:
            logger.error(f"意图识别失败: {str(e)}")
            return {
                "intention_result": IntentionSceneEnum.OTHER_SCENE.value,
                "scene_type": IntentionSceneEnum.OTHER_SCENE.value,
                "error": f"意图识别失败: {str(e)}"
            }
    
    async def _scene_routing_node(self, state: HealthWorkflowState) -> Dict[str, Any]:
        """
        场景路由节点
        
        验证场景类型的有效性，确保能够正确路由到对应的场景实现类
        增加用户ID转换失败的检测，如果转换失败则降级到OTHER_SCENE
        
        Args:
            state: 工作流状态
            
        Returns:
            Dict[str, Any]: 更新的状态
        """
        logger.info(f"执行场景路由节点，目标场景: {state['scene_type']}")
        
        try:
            # 首先检查用户ID转换是否失败
            user_info_dict = state.get("user_info", {})
            biz_user_id = user_info_dict.get("biz_user_id")
            third_user_id = user_info_dict.get("third_user_id")
            conversion_error = state.get("error", "")
            
            # 如果有biz_user_id但没有third_user_id，或者有转换错误，降级到OTHER_SCENE
            if biz_user_id and not third_user_id:
                logger.warning(f"用户ID转换失败，降级到OTHER_SCENE - biz_user_id: {biz_user_id}, 错误: {conversion_error}")
                return {
                    "scene_type": IntentionSceneEnum.OTHER_SCENE.value,
                    "user_info": state["user_info"],
                    "thread_id": state["thread_id"],
                    "error": f"用户ID转换失败，降级到默认场景: {conversion_error}"
                }
            
            # 如果转换状态中有明确的错误信息，也降级到OTHER_SCENE
            if conversion_error and "用户ID转换失败" in conversion_error:
                logger.warning(f"检测到用户ID转换错误，降级到OTHER_SCENE: {conversion_error}")
                return {
                    "scene_type": IntentionSceneEnum.OTHER_SCENE.value,
                    "user_info": state["user_info"],
                    "thread_id": state["thread_id"],
                    "error": f"用户ID转换失败，降级到默认场景: {conversion_error}"
                }
            
            # 验证场景类型
            scene_type_enum = IntentionSceneEnum(state["scene_type"])
            
            # 检查场景是否可用
            if not scene_factory.is_scene_available(scene_type_enum):
                logger.warning(f"场景 {state['scene_type']} 不可用，降级到默认场景")
                scene_type_enum = IntentionSceneEnum.OTHER_SCENE
            
            return {
                "scene_type": scene_type_enum.value,
                "user_info": state["user_info"],
                "thread_id": state["thread_id"]
            }
            
        except ValueError:
            logger.error(f"无效的场景类型: {state['scene_type']}")
            return {
                "scene_type": IntentionSceneEnum.OTHER_SCENE.value,
                "error": f"无效的场景类型: {state['scene_type']}"
            }
    
    async def _scene_processing_node(self, state: HealthWorkflowState) -> Dict[str, Any]:
        """
        场景处理节点
        
        调用场景工厂分发到具体的场景实现类进行处理
        
        Args:
            state: 工作流状态
            
        Returns:
            Dict[str, Any]: 更新的状态
        """
        logger.info(f"执行场景处理节点，场景: {state['scene_type']}")
        
        try:
            # 构建场景上下文
            scene_context = self._build_scene_context(state)
            
            # 获取场景类型枚举
            scene_type_enum = IntentionSceneEnum(state["scene_type"])
            
            # 调用场景工厂处理（使用场景专门的配置）
            result = await scene_factory.handle_scene(
                scene_type_enum,
                scene_context,
                self.scene_config
            )
            
            logger.info(f"场景处理完成，成功: {result.get('success', False)}")
            
            return {
                "ai_response": result.get("ai_response", ""),
                "data": result.get("data"),
                "data_type": result.get("data_type", "text"),
                "error": result.get("error", "")
            }
            
        except Exception as e:
            logger.error(f"场景处理失败: {str(e)}")
            return {
                "ai_response": "抱歉，处理您的请求时出现了问题，请稍后再试。",
                "data": None,
                "data_type": "text",
                "error": str(e)
            }
    
    async def _response_integration_node(self, state: HealthWorkflowState) -> Dict[str, Any]:
        """
        响应整合节点
        
        整合场景处理结果，构建最终响应
        注意：消息历史由ReactAgent自动管理，这里只做状态标记
        
        Args:
            state: 工作流状态
            
        Returns:
            Dict[str, Any]: 最终状态
        """
        logger.info("执行响应整合节点")
        
        try:
            # 简单的响应整合，不手动维护消息历史
            # ReactAgent已经通过thread_id管理了所有对话历史
            
            logger.info("响应整合完成")
            
            return {
                "processing_complete": True
            }
            
        except Exception as e:
            logger.error(f"响应整合失败: {str(e)}")
            return {
                "error": f"响应整合失败: {str(e)}"
            }
    
    def _build_scene_context(self, state: HealthWorkflowState) -> SceneContext:
        """
        构建场景上下文
        
        将工作流状态转换为场景处理所需的上下文格式
        注意：对话记忆由ReactAgent通过thread_id自动管理
        
        Args:
            state: 工作流状态
            
        Returns:
            SceneContext: 场景上下文对象
        """
        # 构建用户信息
        user_info_dict = state.get("user_info", {})
        user_info = None
        
        # 只有在成功转换得到third_user_id时才创建用户信息对象
        # biz_user_id只是前端传递的参数，用于转换，不应该被保留使用
        third_user_id = user_info_dict.get("third_user_id")
        
        if third_user_id:
            # 从用户ID转换成功获得的完整用户信息
            user_info = UserInfo(
                biz_user_id="",  # 不保留biz_user_id，它只是转换参数
                third_user_id=third_user_id,
                name=user_info_dict.get("name", ""),
                age=user_info_dict.get("age"),
                gender=user_info_dict.get("gender"),
                height=user_info_dict.get("height"),
                weight=user_info_dict.get("weight"),
                health_goals=user_info_dict.get("health_goals"),
                preferences=user_info_dict.get("preferences"),
                additional_info=user_info_dict
            )
        
        # 创建场景上下文（ReactAgent通过thread_id自动管理对话记忆）
        scene_context = SceneContext(
            user_input=state["user_input"],
            thread_id=state["thread_id"],
            user_info=user_info
        )
        
        return scene_context
    
    async def process(self, user_input: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        处理用户输入
        
        执行完整的工作流：意图识别 → 场景路由 → 场景处理 → 响应整合
        
        Args:
            user_input: 用户输入
            context: 上下文信息
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        logger.info(f"开始处理用户输入: {user_input[:50]}...")
        
        try:
            # 构建初始状态
            initial_state = {
                "user_input": user_input,
                "thread_id": context.get("thread_id") or context.get("conversation_id", "default"),  # 向后兼容
                "user_info": context.get("user_info", {}),
                "session_data": context.get("session_data", {})
            }
            
            # 执行工作流
            final_state = await self.graph.ainvoke(initial_state)
            
            # 构建响应
            result = {
                "success": not bool(final_state.get("error")),
                "ai_response": final_state.get("ai_response", ""),
                "data": final_state.get("data"),
                "data_type": final_state.get("data_type", "text"),
                "scene_type": final_state.get("scene_type", ""),
                "intention_result": final_state.get("intention_result", ""),
                "error": final_state.get("error")
            }
            
            logger.info(f"处理完成，成功: {result['success']}")
            return result
            
        except Exception as e:
            logger.error(f"工作流处理失败: {str(e)}")
            return {
                "success": False,
                "ai_response": "抱歉，系统暂时无法处理您的请求，请稍后再试。",
                "data": None,
                "data_type": "text",
                "error": str(e)
            }
    
    async def process_stream(self, user_input: str, context: Dict[str, Any] = None) -> Generator[Dict[str, Any], None, None]:
        """
        流式处理用户输入
        
        支持实时流式响应，提供更好的用户体验
        
        Args:
            user_input: 用户输入
            context: 上下文信息
            
        Yields:
            Dict[str, Any]: 流式处理结果
        """
        logger.info(f"开始流式处理用户输入: {user_input[:50]}...")
        
        try:
            # 先进行意图识别和场景路由
            initial_state = {
                "user_input": user_input,
                "thread_id": context.get("thread_id") or context.get("conversation_id", "default"),  # 向后兼容
                "user_info": context.get("user_info", {}),
                "session_data": context.get("session_data", {})
            }
            
            # 执行用户ID转换
            user_conversion_state = await self._user_id_conversion_node(initial_state)
            initial_state.update(user_conversion_state)
            
            # 执行OCR处理
            ocr_state = await self._ocr_processing_node(initial_state)
            initial_state.update(ocr_state)
            
            # 执行意图识别
            intention_state = await self._intention_recognition_node(initial_state)
            initial_state.update(intention_state)
            
            # 执行场景路由
            routing_state = await self._scene_routing_node(initial_state)
            initial_state.update(routing_state)
            
            # 发送意图识别结果
            yield {
                "chunk_type": "intention",
                "scene_type": initial_state.get("scene_type"),
                "intention_result": initial_state.get("intention_result")
            }
            
            # 构建场景上下文
            scene_context = self._build_scene_context(initial_state)
            scene_type_enum = IntentionSceneEnum(initial_state["scene_type"])
            
            # 流式处理场景
            async for chunk in scene_factory.handle_scene_stream(
                scene_type_enum,
                scene_context,
                self.scene_config
            ):
                yield chunk
            
            logger.info("流式处理完成")
            
        except Exception as e:
            logger.error(f"流式处理失败: {str(e)}")
            yield {
                "success": False,
                "error": str(e),
                "chunk_type": "error"
            }


