"""
工作流类型枚举定义

统一管理所有工作流类型，确保前端和后端的一致性
每个工作流都是复杂的多场景综合配置，不是单一场景

@author: <PERSON>
@date: 2025-01-07
"""

from enum import Enum
from typing import Dict, Any, Optional


class WorkflowType(Enum):
    """工作流类型枚举"""
    
    # 健康场景工作流（集成普通对话+各个健康专业场景）
    HEALTH_SCENE = "health_scene_workflow"
    
    # 预留扩展的工作流类型
    # EDUCATION = "education_workflow"      # 教育场景工作流
    # FINANCE = "finance_workflow"          # 金融场景工作流
    # ENTERTAINMENT = "entertainment_workflow"  # 娱乐场景工作流
    
    @classmethod
    def get_all_types(cls) -> Dict[str, str]:
        """获取所有工作流类型"""
        return {member.name: member.value for member in cls}
    
    @classmethod
    def is_valid_type(cls, workflow_type: str) -> bool:
        """检查工作流类型是否有效"""
        if not workflow_type:
            return True  # 空值默认有效，会使用默认工作流
        return workflow_type in [member.value for member in cls] or workflow_type == "default"
    
    @classmethod
    def get_default_type(cls) -> str:
        """获取默认工作流类型"""
        return cls.HEALTH_SCENE.value
    
    @classmethod
    def resolve_workflow_type(cls, workflow_type: Optional[str]) -> str:
        """解析工作流类型，处理默认值和映射"""
        if not workflow_type or workflow_type == "default":
            return cls.get_default_type()
        
        if cls.is_valid_type(workflow_type):
            return workflow_type
        
        # 如果类型无效，返回默认类型
        return cls.get_default_type()
    
    def __str__(self) -> str:
        return self.value