"""
工作流管理器

负责管理和调度不同类型的工作流，支持动态注册和多工作流管理
当前主要管理健康场景工作流，预留扩展能力

@author: shaohua.sun
@date: 2025/7/7
"""

from typing import Dict, Any, Optional, List
import asyncio

from .health_workflow import HealthSceneWorkflow
from utils.biz_logger import get_logger

logger = get_logger(__name__)


class WorkflowRegistry:
    """工作流注册表"""
    
    def __init__(self):
        self._workflows: Dict[str, Any] = {}
        logger.info("工作流注册表初始化完成")
    
    def register(self, workflow_id: str, workflow: Any) -> bool:
        """
        注册工作流
        
        Args:
            workflow_id: 工作流ID
            workflow: 工作流实例
            
        Returns:
            bool: 注册是否成功
        """
        try:
            if workflow_id in self._workflows:
                logger.warning(f"工作流已存在，将被覆盖: {workflow_id}")
            
            self._workflows[workflow_id] = workflow
            logger.info(f"工作流注册成功: {workflow_id}")
            return True
            
        except Exception as e:
            logger.error(f"工作流注册失败: {workflow_id}, 错误: {str(e)}")
            return False
    
    def get_workflow(self, workflow_id: str) -> Optional[Any]:
        """
        获取工作流实例
        
        Args:
            workflow_id: 工作流ID
            
        Returns:
            Optional[Any]: 工作流实例
        """
        return self._workflows.get(workflow_id)
    
    def list_workflows(self) -> List[str]:
        """
        列出所有已注册的工作流ID
        
        Returns:
            List[str]: 工作流ID列表
        """
        return list(self._workflows.keys())


class WorkflowManager:
    """工作流管理器"""
    
    def __init__(self):
        self.registry = WorkflowRegistry()
        self._initialized = False
        self._initialization_lock = asyncio.Lock()
        
        logger.info("工作流管理器创建完成")
    
    async def initialize(self) -> bool:
        """
        初始化工作流管理器
        
        Returns:
            bool: 初始化是否成功
        """
        async with self._initialization_lock:
            if self._initialized:
                return True
            
            try:
                # 注册健康场景工作流
                await self._register_health_workflow()
                
                self._initialized = True
                logger.info("工作流管理器初始化完成")
                return True
                
            except Exception as e:
                logger.error(f"工作流管理器初始化失败: {str(e)}")
                return False
    
    async def _register_health_workflow(self):
        """注册健康场景工作流"""
        try:
            health_workflow = HealthSceneWorkflow()
            success = self.registry.register("health_scene_workflow", health_workflow)
            if success:
                logger.info("健康场景工作流注册成功")
            else:
                raise Exception("健康场景工作流注册失败")
        except Exception as e:
            logger.error(f"注册健康场景工作流失败: {str(e)}")
            raise
    
    async def execute_workflow(
        self, 
        workflow_id: str, 
        user_input: str, 
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        执行指定的工作流
        
        Args:
            workflow_id: 工作流ID
            user_input: 用户输入
            context: 上下文信息
            
        Returns:
            Dict[str, Any]: 工作流执行结果
        """
        if not self._initialized:
            await self.initialize()
        
        # 获取工作流实例
        workflow = self.registry.get_workflow(workflow_id)
        if not workflow:
            error_msg = f"工作流不存在: {workflow_id}"
            logger.error(error_msg)
            return {
                "success": False,
                "ai_response": "抱歉，请求的工作流不可用。",
                "error": error_msg
            }
        
        # 执行工作流
        logger.info(f"开始执行工作流: {workflow_id}, 输入: {user_input[:50]}...")
        result = await workflow.process(user_input, context or {})
        
        logger.info(f"工作流执行完成: {workflow_id}, 成功: {result.get('success', False)}")
        return result
    
    async def register_workflow(self, workflow_type: str, workflow_instance: Any) -> bool:
        """
        动态注册工作流（扩展能力）
        
        Args:
            workflow_type: 工作流类型
            workflow_instance: 工作流实例
            
        Returns:
            bool: 注册是否成功
        """
        try:
            success = self.registry.register(workflow_type, workflow_instance)
            if success:
                logger.info(f"动态注册工作流成功: {workflow_type}")
            return success
        except Exception as e:
            logger.error(f"动态注册工作流失败: {workflow_type}, 错误: {str(e)}")
            return False
    
    def get_workflow_by_type(self, workflow_type: str) -> Optional[Any]:
        """
        根据工作流类型获取工作流实例（通用方法）
        
        Args:
            workflow_type: 工作流类型
            
        Returns:
            Optional[Any]: 工作流实例
        """
        return self.registry.get_workflow(workflow_type)
    
    def get_health_workflow(self) -> Optional[HealthSceneWorkflow]:
        """
        获取健康工作流实例（保留兼容性）
        
        Returns:
            Optional[HealthSceneWorkflow]: 健康工作流实例
        """
        return self.registry.get_workflow("health_scene_workflow")
    
    def is_initialized(self) -> bool:
        """
        检查是否已初始化
        
        Returns:
            bool: 是否已初始化
        """
        return self._initialized
    
    def list_available_workflows(self) -> List[str]:
        """
        列出所有可用的工作流
        
        Returns:
            List[str]: 工作流ID列表
        """
        return self.registry.list_workflows()
    
    def get_workflow_info(self) -> Dict[str, Any]:
        """
        获取工作流管理器信息
        
        Returns:
            Dict[str, Any]: 管理器信息
        """
        return {
            "initialized": self._initialized,
            "available_workflows": self.list_available_workflows(),
            "total_workflows": len(self.registry.list_workflows())
        }