"""
工作流模块

提供工作流编排和处理能力：
- 健康场景工作流（多场景组合流程）
- 意图识别后的场景分发
- 基于LangGraph的流程编排
- 统一的工作流管理框架

支持单场景或多场景组合的工作流，具体包括：
- 单场景流程：专门处理某个特定场景的流程
- 多场景组合流程：如health_workflow这样包含多个场景的复合流程

@author: shaohua.sun
@date: 2025/6/20
"""

from .health_workflow import HealthSceneWorkflow
from .base_workflow import BaseWorkflow, WorkflowContext, WorkflowResult, WorkflowMetadata
from .workflow_manager import WorkflowManager, WorkflowRegistry

__all__ = [
    # 原有工作流类（供扩展和适配使用）
    "HealthSceneWorkflow",

    # 基础工作流框架
    "BaseWorkflow",
    "WorkflowContext",
    "WorkflowResult",
    "WorkflowMetadata",

    # 工作流管理（主要使用接口）
    "WorkflowManager",
    "WorkflowRegistry"
]