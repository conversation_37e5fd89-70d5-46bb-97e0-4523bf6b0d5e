"""
基础工作流抽象类

定义工作流的统一接口，支持单场景或多场景组合的流程

@author: shao<PERSON>.sun
@date: 2025/7/2
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime

from utils.biz_logger import get_logger

logger = get_logger(__name__)


@dataclass
class WorkflowMetadata:
    """工作流元数据"""
    workflow_id: str             # 工作流唯一标识
    name: str                    # 工作流名称
    description: str             # 工作流描述
    version: str                 # 版本号
    author: str                  # 作者
    tags: List[str]             # 标签
    enabled: bool = True         # 是否启用
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


@dataclass
class WorkflowContext:
    """工作流上下文"""
    user_input: str                          # 用户输入
    thread_id: str                          # 线程ID，与ReactAgent保持一致
    user_info: Dict[str, Any]               # 用户信息
    session_data: Dict[str, Any] = None     # 会话数据
    metadata: Dict[str, Any] = None         # 元数据
    timestamp: datetime = None              # 时间戳
    
    def __post_init__(self):
        if self.session_data is None:
            self.session_data = {}
        if self.metadata is None:
            self.metadata = {}
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class WorkflowResult:
    """工作流执行结果"""
    success: bool                           # 是否成功
    workflow_id: str                       # 工作流标识
    response: str                          # 响应内容
    data: Dict[str, Any] = None           # 结果数据
    error: Optional[str] = None           # 错误信息
    execution_time: Optional[float] = None # 执行时间（秒）
    metadata: Dict[str, Any] = None       # 元数据
    
    def __post_init__(self):
        if self.data is None:
            self.data = {}
        if self.metadata is None:
            self.metadata = {}


class BaseWorkflow(ABC):
    """基础工作流抽象类"""
    
    def __init__(self, metadata: WorkflowMetadata):
        """
        初始化基础工作流
        
        Args:
            metadata: 工作流元数据
        """
        self.metadata = metadata
        self.workflow_id = metadata.workflow_id
        self.is_initialized = False
        
        logger.info(f"工作流创建: {metadata.name} (ID: {metadata.workflow_id})")
    
    @abstractmethod
    async def initialize(self) -> bool:
        """
        初始化工作流
        
        Returns:
            bool: 初始化是否成功
        """
        pass
    
    @abstractmethod
    async def process(self, context: WorkflowContext) -> WorkflowResult:
        """
        处理工作流
        
        Args:
            context: 工作流上下文
            
        Returns:
            WorkflowResult: 工作流执行结果
        """
        pass
    
    async def validate_context(self, context: WorkflowContext) -> bool:
        """
        验证上下文是否有效（可选重写）
        
        Args:
            context: 工作流上下文
            
        Returns:
            bool: 上下文是否有效
        """
        # 基础验证
        if not context.user_input or not context.user_input.strip():
            return False
        if not context.thread_id:
            return False
        return True
    
    async def pre_process(self, context: WorkflowContext) -> WorkflowContext:
        """
        预处理（可选重写）
        
        Args:
            context: 工作流上下文
            
        Returns:
            WorkflowContext: 处理后的上下文
        """
        return context
    
    async def post_process(self, result: WorkflowResult, context: WorkflowContext) -> WorkflowResult:
        """
        后处理（可选重写）
        
        Args:
            result: 工作流结果
            context: 工作流上下文
            
        Returns:
            WorkflowResult: 处理后的结果
        """
        return result
    
    async def handle_error(self, error: Exception, context: WorkflowContext) -> WorkflowResult:
        """
        错误处理（可选重写）
        
        Args:
            error: 异常
            context: 工作流上下文
            
        Returns:
            WorkflowResult: 错误处理结果
        """
        logger.error(f"工作流执行失败: {self.metadata.name}, 错误: {str(error)}")
        
        return WorkflowResult(
            success=False,
            workflow_id=self.workflow_id,
            response="抱歉，处理您的请求时出现了问题，请稍后重试。",
            error=str(error),
            metadata={
                "workflow_name": self.metadata.name,
                "error_type": type(error).__name__
            }
        )
    
    def get_info(self) -> Dict[str, Any]:
        """
        获取工作流信息
        
        Returns:
            Dict[str, Any]: 工作流信息
        """
        return {
            "workflow_id": self.workflow_id,
            "name": self.metadata.name,
            "description": self.metadata.description,
            "version": self.metadata.version,
            "author": self.metadata.author,
            "tags": self.metadata.tags,
            "enabled": self.metadata.enabled,
            "initialized": self.is_initialized,
            "created_at": self.metadata.created_at.isoformat() if self.metadata.created_at else None,
            "updated_at": self.metadata.updated_at.isoformat() if self.metadata.updated_at else None
        }
    
    async def execute(self, context: WorkflowContext) -> WorkflowResult:
        """
        执行工作流的完整流程
        
        Args:
            context: 工作流上下文
            
        Returns:
            WorkflowResult: 工作流执行结果
        """
        start_time = datetime.now()
        
        try:
            # 验证上下文
            if not await self.validate_context(context):
                raise ValueError("工作流上下文验证失败")
            
            # 预处理
            context = await self.pre_process(context)
            
            # 执行主流程
            result = await self.process(context)
            
            # 后处理
            result = await self.post_process(result, context)
            
            # 计算执行时间
            execution_time = (datetime.now() - start_time).total_seconds()
            result.execution_time = execution_time
            
            logger.info(f"工作流执行成功: {self.metadata.name}, 耗时: {execution_time:.2f}秒")
            
            return result
            
        except Exception as e:
            # 错误处理
            result = await self.handle_error(e, context)
            execution_time = (datetime.now() - start_time).total_seconds()
            result.execution_time = execution_time
            
            return result
