# biz_time
#
# @author: 崔晓波
# @email: <EMAIL>
# @date: 2025/6/26 14:28
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
from datetime import datetime
from zoneinfo import ZoneInfo


def biz_format_time_ch(time: datetime):
    return time.strftime("%Y-%m-%d %H:%M:%S")


def get_current_cst_time() -> datetime:
    """
    获取东八区（中国标准时间）的当前时间。

    Returns:
        datetime: 包含东八区当前时间的 datetime 对象。
    """
    return datetime.now(ZoneInfo('Asia/Shanghai'))