import yaml
from pathlib import Path
import os
from dotenv import load_dotenv

def load_config(path: str):
    with open(path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

# 加载 .env 文件（默认项目根目录）
load_dotenv(dotenv_path=Path(__file__).parent.parent / '.env', override=True)

def get_prompt_config():
    """
    获取 prompt 相关配置
    支持数据源类型（mongo/api）、连接参数等
    """
    return {
        'source_type': os.getenv('PROMPT_SOURCE_TYPE', 'mongo'),
        'api_url': os.getenv('PROMPT_API_URL'),
    }

def get_mongo_uri():
    host = os.getenv("MONGO_HOST", "localhost")
    port = os.getenv("MONGO_PORT", "27017")
    db = os.getenv("MONGO_DB", "chatbot_test")
    user = os.getenv("MONGO_USER", "")
    pwd = os.getenv("MONGO_PWD", "")
    auth_db = os.getenv("MONGO_AUTH_DB", "admin")
    if user and pwd:
        return f"mongodb://{user}:{pwd}@{host}:{port}/{db}?authSource={auth_db}"
    else:
        return f"mongodb://{host}:{port}/{db}" 