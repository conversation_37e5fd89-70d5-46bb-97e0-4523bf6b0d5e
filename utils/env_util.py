class EnvUtil:
    """
        环境工具类，支持判断当前是否为本地或开发环境
        """
    import os

    @staticmethod
    def is_local() -> bool:
        """
        判断当前环境是否为本地环境（local/test/dev）
        """
        env = EnvUtil.os.environ.get("ENV", "").lower()
        return env in ["", "local"]

    @staticmethod
    def is_dev() -> bool:
        """
        判断当前环境是否为开发环境（dev）
        """
        env = EnvUtil.os.environ.get("ENV", "").lower()
        return env == "dev"

    @staticmethod
    def is_test() -> bool:
        """
        判断当前环境是否为测试环境（test）
        """
        env = EnvUtil.os.environ.get("ENV", "").lower()
        return env == "test"

    @staticmethod
    def is_prod() -> bool:
        """
        判断当前环境是否为生产环境（prod）
        """
        env = EnvUtil.os.environ.get("ENV", "").lower()
        return env == "prod"
