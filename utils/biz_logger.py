import logging
import os
from datetime import datetime, timezone, timedelta

from utils.env_util import EnvUtil

def get_logger(name: str):
    logger = logging.getLogger(name)
    if not logger.handlers:
        # 控制台Handler
        stream_handler = logging.StreamHandler()
        formatter = logging.Formatter('[%(asctime)s] %(levelname)s %(name)s: %(message)s')
        stream_handler.setFormatter(formatter)
        logger.addHandler(stream_handler)

        # 文件Handler，按日期区分（使用东八区时间）
        log_dir = 'logs'
        os.makedirs(log_dir, exist_ok=True)
        china_tz = timezone(timedelta(hours=8))
        log_date = datetime.now(china_tz).strftime('%Y%m%d')
        file_handler = logging.FileHandler(os.path.join(log_dir, f'app_{log_date}.log'), encoding='utf-8')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    logger.setLevel(logging.INFO)

    return logger 

