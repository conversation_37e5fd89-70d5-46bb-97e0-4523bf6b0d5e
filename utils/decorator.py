import asyncio
import time
import functools

from utils.biz_logger import get_logger

logger = get_logger(__name__)

def timing_decorator(func):
    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.time()
        result = await func(*args, **kwargs)
        elapsed = (time.time() - start_time) * 1000  # 毫秒
        logger.info(f"FUNC: {func.__name__} COST_TIME: {elapsed:.2f} ms")
        return result

    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        elapsed = (time.time() - start_time) * 1000  # 毫秒
        logger.info(f"FUNC: {func.__name__} COST_TIME: {elapsed:.2f} ms")
        return result

    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    else:
        return sync_wrapper