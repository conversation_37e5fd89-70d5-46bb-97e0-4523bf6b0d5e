# user_info_cache.py - 全局用户信息缓存管理器
#
# @author: shao<PERSON>.sun
# @date: 2025-01-27
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD

"""
全局用户信息缓存管理器

提供统一的用户信息缓存机制，支持：
1. 用户基础信息缓存（包括memberIdList）
2. 基于user_id的快速查询
3. 自动过期和清理机制
4. 线程安全的操作

设计原则：
- 单例模式，全局唯一实例
- 线程安全，支持并发访问
- 自动过期，避免内存泄漏
- 简单易用，提供便捷接口
"""

import threading
import time
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from integration.services.chatbot.models import UserInfoData
from utils.biz_logger import get_logger

logger = get_logger(__name__)


class UserInfoCache:
    """
    全局用户信息缓存管理器
    
    线程安全的用户信息缓存，支持自动过期和清理
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(UserInfoCache, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """初始化缓存管理器"""
        if self._initialized:
            return
            
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._cache_lock = threading.RLock()
        self._default_ttl = 3600  # 默认缓存1小时
        self._max_size = 1000  # 最大缓存条目数
        self._initialized = True
        
        logger.info("用户信息缓存管理器初始化完成")
    
    def put_user_info(self, user_id: str, user_info: UserInfoData, ttl: Optional[int] = None) -> None:
        """
        缓存用户信息
        
        Args:
            user_id: 用户ID（third_user_id）
            user_info: 用户信息数据
            ttl: 缓存生存时间（秒），为None时使用默认值
        """
        if not user_id or not user_info:
            logger.warning("用户ID或用户信息为空，跳过缓存")
            return
        
        expire_time = time.time() + (ttl or self._default_ttl)
        
        with self._cache_lock:
            # 检查缓存大小，如果超过限制则清理过期项
            if len(self._cache) >= self._max_size:
                self._cleanup_expired_items()
                
                # 如果清理后仍超过限制，移除最旧的项
                if len(self._cache) >= self._max_size:
                    oldest_key = min(self._cache.keys(), 
                                   key=lambda k: self._cache[k]['cached_at'])
                    del self._cache[oldest_key]
                    logger.info(f"缓存已满，移除最旧的用户信息: {oldest_key}")
            
            # 缓存用户信息
            self._cache[user_id] = {
                'user_info': user_info,
                'cached_at': time.time(),
                'expire_at': expire_time
            }
            
            logger.debug(f"缓存用户信息: user_id={user_id}, ttl={ttl or self._default_ttl}秒")
    
    def get_user_info(self, user_id: str) -> Optional[UserInfoData]:
        """
        获取缓存的用户信息
        
        Args:
            user_id: 用户ID（third_user_id）
            
        Returns:
            Optional[UserInfoData]: 用户信息数据，如果不存在或已过期则返回None
        """
        if not user_id:
            return None
        
        with self._cache_lock:
            if user_id not in self._cache:
                logger.debug(f"用户信息不在缓存中: user_id={user_id}")
                return None
            
            cache_item = self._cache[user_id]
            
            # 检查是否过期
            if time.time() > cache_item['expire_at']:
                del self._cache[user_id]
                logger.debug(f"用户信息已过期，从缓存中移除: user_id={user_id}")
                return None
            
            logger.debug(f"从缓存获取用户信息: user_id={user_id}")
            return cache_item['user_info']
    
    def get_member_id_list(self, user_id: str) -> Optional[List[str]]:
        """
        获取用户的会员ID列表
        
        Args:
            user_id: 用户ID（third_user_id）
            
        Returns:
            Optional[List[str]]: 会员ID列表，如果不存在则返回None
        """
        user_info = self.get_user_info(user_id)
        if not user_info or not user_info.baseInfo:
            return None
        
        member_id_list = user_info.baseInfo.memberIdList
        logger.debug(f"获取会员ID列表: user_id={user_id}, member_ids={member_id_list}")
        return member_id_list
    
    def get_primary_member_id(self, user_id: str) -> Optional[str]:
        """
        获取用户的主要会员ID（列表中的第一个）
        
        Args:
            user_id: 用户ID（third_user_id）
            
        Returns:
            Optional[str]: 主要会员ID，如果不存在则返回None
        """
        member_id_list = self.get_member_id_list(user_id)
        if not member_id_list:
            return None
        
        primary_member_id = member_id_list[0]
        logger.debug(f"获取主要会员ID: user_id={user_id}, primary_member_id={primary_member_id}")
        return primary_member_id
    
    def remove_user_info(self, user_id: str) -> bool:
        """
        移除用户信息缓存
        
        Args:
            user_id: 用户ID（third_user_id）
            
        Returns:
            bool: 是否成功移除
        """
        if not user_id:
            return False
        
        with self._cache_lock:
            if user_id in self._cache:
                del self._cache[user_id]
                logger.info(f"移除用户信息缓存: user_id={user_id}")
                return True
            return False
    
    def clear_cache(self) -> int:
        """
        清空所有缓存
        
        Returns:
            int: 清除的缓存条目数
        """
        with self._cache_lock:
            count = len(self._cache)
            self._cache.clear()
            logger.info(f"清空用户信息缓存，共清除 {count} 个条目")
            return count
    
    def _cleanup_expired_items(self) -> int:
        """
        清理过期的缓存项
        
        Returns:
            int: 清理的过期条目数
        """
        current_time = time.time()
        expired_keys = []
        
        for user_id, cache_item in self._cache.items():
            if current_time > cache_item['expire_at']:
                expired_keys.append(user_id)
        
        for key in expired_keys:
            del self._cache[key]
        
        if expired_keys:
            logger.info(f"清理过期用户信息缓存，共清除 {len(expired_keys)} 个条目")
        
        return len(expired_keys)
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            Dict[str, Any]: 缓存统计信息
        """
        with self._cache_lock:
            current_time = time.time()
            expired_count = sum(1 for item in self._cache.values() 
                              if current_time > item['expire_at'])
            
            return {
                'total_items': len(self._cache),
                'expired_items': expired_count,
                'active_items': len(self._cache) - expired_count,
                'max_size': self._max_size,
                'default_ttl': self._default_ttl
            }
    
    def is_cached(self, user_id: str) -> bool:
        """
        检查用户信息是否在缓存中且未过期
        
        Args:
            user_id: 用户ID（third_user_id）
            
        Returns:
            bool: 是否在缓存中且未过期
        """
        return self.get_user_info(user_id) is not None


# 全局单例实例
_user_info_cache = None

def get_user_info_cache() -> UserInfoCache:
    """获取全局用户信息缓存实例"""
    global _user_info_cache
    if _user_info_cache is None:
        _user_info_cache = UserInfoCache()
    return _user_info_cache 