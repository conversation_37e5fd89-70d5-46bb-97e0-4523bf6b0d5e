from pydantic import BaseModel
from typing import Any, Optional

class Result(BaseModel):
    code: int = 0
    message: str = "success"
    data: Optional[Any] = None

    @staticmethod
    def success(data: Any = None, message: str = "success"):
        return Result(code=0, message=message, data=data)

    @staticmethod
    def fail(msg: str = "error", code: int = 1, data: Any = None):
        return Result(code=code, message=msg, data=data)