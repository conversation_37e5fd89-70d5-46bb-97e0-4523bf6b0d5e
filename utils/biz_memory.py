from typing import List

from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage


class BizMemory:
    def __init__(self):
        pass


    @staticmethod
    def format_lang_msg_to_dict(messages: List[BaseMessage]) -> List[dict]:
        """
        Transform LangGraph message object list to role-content dict list.
        """
        result = []
        for msg in messages:
            if isinstance(msg, HumanMessage):
                role = "user"
            elif isinstance(msg, AIMessage):
                role = "assistant"
            elif isinstance(msg, SystemMessage):
                role = "system"
            else:
                role = "unknown"
            result.append({"role": role, "content": msg.content})
        return result

    @staticmethod
    def format_msg_to_dict(messages: List[str]) -> List[dict]:
        """
        Transform LangGraph message object list to role-content dict list.
        """
        result = []
        for msg in messages:
            if isinstance(msg, HumanMessage):
                role = "user"
            elif isinstance(msg, AIMessage):
                role = "assistant"
            elif isinstance(msg, SystemMessage):
                role = "system"
            else:
                role = "unknown"
            result.append({"role": role, "content": msg.content})
        return result
