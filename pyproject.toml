[project]
name = "evo-mind"
version = "0.1.0"
description = "EVYD AI Server"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "python-dotenv>=1.1.0",
    "fastapi>=0.115.12",
    "langchain-community>=0.3.25",
    "langchain-deepseek>=0.1.3",
    "langchain-openai>=0.3.23",
    "langchain[openai]>=0.3.25",
    "langgraph>=0.4.8",
    "langsmith>=0.3.45",
    "requests>=2.32.4",
    "uvicorn>=0.34.3",
    "pytest>=8.4.1",
    "langgraph-checkpoint>=2.1.0",
    "langgraph-checkpoint-postgres>=2.0.21",
    "langgraph-checkpoint-mongodb>=0.1.4",
    "langgraph-checkpoint-redis>=0.0.7",
    "langgraph-checkpoint-mysql>=2.0.15",
    "psycopg[binary,pool]>=3.2.9",
    "langmem>=0.0.27",
    "ipython>=9.3.0",
    "mem0ai>=0.1.114",
    "ollama>=0.5.1",
    "psycopg2-binary>=2.9.10",
    "langchain-ollama>=0.3.6",
    "langchain-qwq>=0.2.0",
]
