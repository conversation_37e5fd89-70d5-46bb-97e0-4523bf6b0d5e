"""
本地提示词模板管理模块

负责管理项目中所有本地提示词模板，与 service/prompt（远程提示词）形成清晰的职责分离：
- service/prompt: 远程提示词管理（MongoDB、敏感话题等）
- service/templates: 本地提示词模板管理（角色定义、格式规则等）

@author: shaohua.sun
@date: 2025/7/2
"""

from .base_templates import TemplateManager
from .intention_templates import IntentionTemplateManager
from .common_components import CommonComponents

__all__ = [
    "TemplateManager",
    "IntentionTemplateManager", 
    "CommonComponents"
]
