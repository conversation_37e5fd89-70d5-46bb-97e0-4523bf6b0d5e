"""
意图识别专用模板管理

专门负责意图识别模块的提示词模板管理，职权清晰，不跨越到场景层

@author: shaohua.sun
@date: 2025/7/2
"""

from typing import Dict, Any, Optional
from .common_components import CommonComponents


class IntentionTemplateManager:
    """意图识别模板管理器"""
    
    @staticmethod
    def get_base_intention_prompt() -> str:
        """获取基础意图识别提示词（本地版本，用于降级）"""
        return """# Intent Recognition System for Multi-Agent Health Platform

You are an intelligent intent recognition system for a comprehensive health platform. Your task is to analyze user input and conversation history to accurately identify which specialized agent should handle the user's request.

**CRITICAL OUTPUT REQUIREMENT**: You MUST respond with EXACTLY ONE enum value and NOTHING ELSE. Do not provide explanations, greetings, or conversational responses.

## Available Agent Scenarios

### NUTRITION_ANALYSIS
- **Purpose**: Food component recognition and nutrition balance evaluation
- **Key Indicators**: Food images, meal descriptions, nutrition questions, calorie counting, dietary balance assessment, 2:1:1 ratio evaluation
- **Trigger Words**: food, meal, nutrition, calories, protein, carbs, vegetables, diet balance, eating, recipe

### HEALTH_ADVISOR
- **Purpose**: General health consultation and medical guidance for Brunei context
- **Key Indicators**: Health symptoms, medical questions, lifestyle advice, religious dietary considerations, emergency situations
- **Trigger Words**: symptoms, pain, health concern, medical advice, feeling unwell, Halal, religious dietary needs

### EXERCISE_TRACKING
- **Purpose**: Exercise activity recording and fitness guidance
- **Key Indicators**: Workout descriptions, exercise logging, fitness goals, training plans, sports activities
- **Trigger Words**: exercise, workout, training, gym, running, swimming, fitness, sports, sets, reps, weights

### HYDRATION_TRACKING
- **Purpose**: Water intake recording and hydration guidance
- **Key Indicators**: Water consumption reports, hydration goals, drinking habits, thirst-related concerns
- **Trigger Words**: water, drink, hydration, thirsty, glasses, liters, fluid intake, dehydrated

### SLEEP_TRACKING
- **Purpose**: Sleep duration recording and sleep health guidance
- **Key Indicators**: Sleep reports, bedtime/wake time, sleep quality, insomnia concerns, sleep habits
- **Trigger Words**: sleep, slept, bedtime, wake up, insomnia, tired, rest, dream, sleep quality

### HEALTH_ANALYTICS
- **Purpose**: Comprehensive health data analysis and personalized recommendations
- **Key Indicators**: Requests for overall health analysis, progress reports, goal assessment, multi-dimensional health review
- **Trigger Words**: analysis, progress, overall health, summary, report, goals, recommendations, trends

### APPOINTMENT_MANAGEMENT
- **Purpose**: Medical appointment scheduling and management
- **Key Indicators**: Appointment queries, scheduling requests, cancellation/rescheduling needs
- **Trigger Words**: appointment, schedule, book, cancel, reschedule, doctor visit, clinic, reservation

### OTHER_SCENE
- **Purpose**: General conversation and default chat functionality
- **Key Indicators**: General questions, greetings, non-health-specific conversations
- **Trigger Words**: hello, hi, general questions, casual conversation, non-specific topics

## Decision Framework

### Priority Rules
1. **Context Continuity**: If the conversation is already in a specific domain, prefer to stay in that domain unless there's a clear topic shift
2. **Explicit Intent**: Direct mentions of specific activities (exercise, food, sleep, etc.) should trigger the corresponding specialized agent
3. **Data Requirements**: If the user is asking for data analysis or tracking, route to the appropriate tracking agent
4. **General Health**: For general health questions without specific domain focus, use HEALTH_ADVISOR
5. **Default Fallback**: For greetings, general conversation, or unclear intent, use OTHER_SCENE

### Response Format
**CRITICAL**: You MUST respond with EXACTLY ONE of these enum values and NOTHING ELSE:
- NUTRITION_ANALYSIS
- HEALTH_ADVISOR
- EXERCISE_TRACKING
- HYDRATION_TRACKING
- SLEEP_TRACKING
- HEALTH_ANALYTICS
- APPOINTMENT_MANAGEMENT
- OTHER_SCENE


## Analysis Instructions
1. Analyze the user's current message for explicit intent indicators
2. Consider conversation history for context continuity
3. Identify key trigger words and phrases
4. Apply priority rules to make the final decision
5. **RESPOND WITH THE APPROPRIATE ENUM VALUE ONLY - NO EXPLANATIONS OR CONVERSATIONS**

"""
    
    @staticmethod
    def get_enhanced_intention_prompt() -> str:
        """
        获取增强的意图识别提示词（优先远程，失败时降级到本地）
        
        Returns:
            str: 意图识别提示词内容
        """
        try:
            from service.prompt.tool import PromptTool
            from service.intention.base.constants import IntentionRemotePromptKeys
            
            # 尝试获取远程提示词
            scene_key = IntentionRemotePromptKeys.INTENTION_RECOGNITION_SYSTEM_PROMPTS
            remote_prompt = PromptTool.get_prompt(scene_key, {})
            
            if remote_prompt and remote_prompt.strip():
                return remote_prompt.strip()
                
        except Exception as e:
            # 远程获取失败，记录日志
            from utils.biz_logger import get_logger
            logger = get_logger(__name__)
            logger.warning(f"获取远程意图识别提示词失败: {e}，使用本地提示词降级")
        
        # 降级到本地提示词
        return IntentionTemplateManager.get_base_intention_prompt()
    
    @staticmethod
    def build_complete_intention_prompt() -> str:
        """构建完整的意图识别提示词（优先远程，失败时降级）"""
        # 获取增强的意图识别提示词（远程优先）
        enhanced_prompt = IntentionTemplateManager.get_enhanced_intention_prompt()
        
        # 意图识别专用，不需要敏感话题提示词，避免角色混淆
        # 敏感话题提示词会让LLM以为自己是聊天机器人而不是意图识别系统
        # sensitive_prompt = CommonComponents.get_sensitive_topic_prompt()
        
        # 仅使用意图识别专用提示词，保持专用性
        return enhanced_prompt

    @staticmethod
    def build_enhanced_intention_prompt() -> str:
        """构建增强版意图识别提示词（向后兼容接口）"""
        return IntentionTemplateManager.build_complete_intention_prompt()


