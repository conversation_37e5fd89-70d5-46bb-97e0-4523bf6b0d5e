"""
远程提示词集成常量管理

定义场景映射、配置常量等，确保企业级设计的一致性
只管理基础提示词，不涉及工具指导和公共提示词

@author: shaohua.sun
@date: 2025/7/7
"""

from typing import Dict
from service.prompt.scenes import PromptScene
from service.intention.base.constants import IntentionRemotePromptKeys
from service.intention.base import IntentionSceneEnum


class ScenePromptMapping:
    """场景与远程提示词的映射关系"""
    
    # 场景名称到远程prompt场景的映射
    SCENE_TO_REMOTE_MAPPING: Dict[str, str] = {
        IntentionSceneEnum.NUTRITION_ANALYSIS.value: PromptScene.NUTRITION_ANALYSIS_SYSTEM_PROMPTS.value,
        IntentionSceneEnum.EXERCISE_TRACKING.value: PromptScene.EXERCISE_TRACKING_SYSTEM_PROMPTS.value,
        IntentionSceneEnum.HYDRATION_TRACKING.value: PromptScene.HYDRATION_TRACKING_SYSTEM_PROMPTS.value,
        IntentionSceneEnum.SLEEP_TRACKING.value: PromptScene.SLEEP_TRACKING_SYSTEM_PROMPTS.value,
        IntentionSceneEnum.APPOINTMENT_MANAGEMENT.value: PromptScene.APPOINTMENT_MANAGEMENT_SYSTEM_PROMPTS.value,
        IntentionSceneEnum.HEALTH_ADVISOR.value: PromptScene.HEALTH_ADVISOR_SYSTEM_PROMPTS.value,
        IntentionSceneEnum.HEALTH_ANALYTICS.value: PromptScene.HEALTH_ANALYTICS_SYSTEM_PROMPTS.value,
        IntentionSceneEnum.OTHER_SCENE.value: PromptScene.OTHER_SCENE_SYSTEM_PROMPTS.value,
        # DefaultChatTemplate 生成的场景名称映射到默认场景
        "DEFAULT_CHAT": PromptScene.OTHER_SCENE_SYSTEM_PROMPTS.value,
        # 意图识别特殊场景 - 使用专门的常量管理
        "INTENTION_RECOGNITION": IntentionRemotePromptKeys.INTENTION_RECOGNITION_SYSTEM_PROMPTS,
    }
    
    # 敏感话题提示词映射
    SENSITIVE_TOPIC_MAPPING: str = PromptScene.SENSITIVE_TOPIC_SYSTEM_PROMPT.value
    
    @classmethod
    def get_remote_scene_key(cls, local_scene: str) -> str:
        """
        根据本地场景名称获取远程提示词场景键
        
        Args:
            local_scene: 本地场景名称
            
        Returns:
            str: 远程提示词场景键
            
        Raises:
            ValueError: 当场景名称不支持时
        """
        if local_scene not in cls.SCENE_TO_REMOTE_MAPPING:
            raise ValueError(f"Unsupported scene for remote prompt: {local_scene}")
        
        return cls.SCENE_TO_REMOTE_MAPPING[local_scene]
    
    @classmethod
    def get_supported_scenes(cls) -> list:
        """获取所有支持远程提示词的场景"""
        return list(cls.SCENE_TO_REMOTE_MAPPING.keys())


class RemotePromptConfig:
    """远程提示词配置常量"""
    
    # 默认超时时间（秒）
    DEFAULT_TIMEOUT = 5.0
    
    # 重试次数
    DEFAULT_RETRY_COUNT = 3
    
    # 降级模式开关
    ENABLE_FALLBACK = True
    
    # 日志前缀
    LOG_PREFIX = "[RemotePrompt]"