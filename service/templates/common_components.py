"""
通用提示词组件

提供所有模块共享的提示词组件，包括：
- 时间信息
- 格式规则
- 行为要求
- 远程提示词集成接口

@author: shaohua.sun
@date: 2025/7/2
"""

from typing import Dict, Any, Optional
from datetime import datetime, timedelta, timezone

from utils.biz_logger import get_logger

logger = get_logger(__name__)


class CommonComponents:
    """通用提示词组件"""
    
    @staticmethod
    def get_current_time_info() -> Dict[str, str]:
        """获取当前时间信息（东八区时区）"""
        # 强制使用东八区时间（UTC+8）
        china_tz = timezone(timedelta(hours=8))
        now = datetime.now(china_tz)
        yesterday = now - timedelta(days=1)
        tomorrow = now + timedelta(days=1)
        
        return {
            "currentDateTime": now.strftime("%Y-%m-%d %H:%M:%S"),
            "currentDate": now.strftime("%Y-%m-%d"),
            "currentTime": now.strftime("%H:%M:%S"),
            "currentDayOfWeek": now.strftime("%A").upper(),
            "currentYear": now.strftime("%Y"),
            "yesterdayDate": yesterday.strftime("%Y-%m-%d"),
            "tomorrowDate": tomorrow.strftime("%Y-%m-%d"),
            "currentTimeUTC": now.strftime("%b %d %H:%M:%S %Y")
        }
    
    @staticmethod
    def build_system_time_info() -> str:
        """构建系统时间信息提示词"""
        time_info = CommonComponents.get_current_time_info()
        
        return f"""**SYSTEM TIME INFORMATION:**
Current Date and Time: {time_info['currentDateTime']}
Current Date: {time_info['currentDate']}
Current Time: {time_info['currentTime']}
Day of Week: {time_info['currentDayOfWeek']}
Current Year: {time_info['currentYear']}

**CRITICAL TIME CONSTRAINTS:**
1. Today's date is {time_info['currentDate']} - use this as the absolute reference point
2. When user mentions "today", it refers to {time_info['currentDate']}
3. When user mentions "yesterday", it refers to {time_info['yesterdayDate']}
4. When user mentions "tomorrow", it refers to {time_info['tomorrowDate']}
5. Current time is {time_info['currentTime']} on {time_info['currentDayOfWeek']}
6. For any date-related queries, calculate based on {time_info['currentDate']}
7. NEVER assume dates - always calculate relative to {time_info['currentDate']}
8. ALL date calculations MUST be based on {time_info['currentDate']} as the reference point
9. If unsure about dates, ASK for clarification rather than guessing

"""

    @staticmethod
    def build_mobile_formatting_rules() -> str:
        """构建移动端格式化规则"""
        return """**MOBILE-OPTIMIZED FORMATTING RULES:**
1. **Use bullet points (•) instead of dashes (-) for better mobile readability**
2. **Keep line lengths under 60 characters when possible**
3. **Use clear section headers with emojis for visual separation**
4. **Avoid complex tables - use simple lists instead**
5. **Use numbered lists for sequential information**
6. **Bold important keywords and values**
7. **Use line breaks generously for better mobile spacing**

"""

    @staticmethod
    def build_markdown_guidelines(emoji_set: str = "general") -> str:
        """
        构建Markdown格式指导
        
        Args:
            emoji_set: 表情符号集合类型，支持的类型包括：
                     - nutrition: 营养相关表情
                     - exercise: 运动相关表情  
                     - hydration: 饮水相关表情
                     - sleep: 睡眠相关表情
                     - appointment: 预约相关表情
                     - health: 健康相关表情
                     - general: 通用表情（默认）
        
        Returns:
            str: 包含Markdown格式指导和相应表情符号的字符串
        """
        emoji_maps = {
            "nutrition": "🥗 🍎 🥛 💪 📊",
            "exercise": "💪 🏃‍♂️ ⏱️ 🔥 📊 🎯",
            "hydration": "💧 🥤 📊 🎯 ⚡",
            "sleep": "😴 🛏️ ⏰ 📊 🌙",
            "appointment": "📅 🏥 👨‍⚕️ ⏰ 📋",
            "health": "🏥 💊 🩺 📋 💡",
            "general": "📊 💡 ✅ ⚠️ 📋"
        }
        
        emojis = emoji_maps.get(emoji_set, emoji_maps["general"])
        
        return f"""**MARKDOWN FORMATTING GUIDELINES:**
• Use **bold** for emphasis on key information
• Use *italics* for specific names and types
• Use `code blocks` for specific measurements and values
• Use > blockquotes for important tips and advice
• Use --- for section separators
• Use ### for sub-headers
• Use emojis to enhance readability: {emojis}

"""

    @staticmethod
    def build_required_behaviors() -> str:
        """构建必需行为要求"""
        return """**✅ REQUIRED BEHAVIORS**
- **ALWAYS scan conversation history**: Review all messages for relevant content
- **ALWAYS reference historical data**: Acknowledge previously shared information
- **ALWAYS maintain context**: Preserve conversation memory across multiple turns
- **ALWAYS use targeted questions**: Ask only for missing information
- **ALWAYS validate completeness**: Ensure all required fields before record generation

"""

    @staticmethod
    def get_sensitive_topic_prompt() -> str:
        """获取敏感话题提示词（远程优先，失败时降级到空字符串）"""
        try:
            from service.prompt.tool import PromptTool
            from service.prompt.scenes import PromptScene
            
            # 通过service/prompt模块获取敏感话题提示词（现在是同步调用）
            scene_key = PromptScene.SENSITIVE_TOPIC_SYSTEM_PROMPT.value
            params = {}
            logger.info(f"调用PromptTool.get_prompt获取敏感词 - 场景: {scene_key}, 参数: {params}")
            try:
                sensitive_prompt = PromptTool.get_prompt(scene_key, params)
                logger.info(f"PromptTool敏感词返回结果 - 场景: {scene_key}, 长度: {len(sensitive_prompt) if sensitive_prompt else 0}, 内容预览: {sensitive_prompt[:100] if sensitive_prompt else 'None'}...")
            except Exception as call_e:
                logger.warning(f"PromptTool敏感词调用异常 - 场景: {scene_key}, 异常: {call_e}")
                raise call_e
            
            if sensitive_prompt and sensitive_prompt.strip():
                logger.info("成功获取远程敏感话题提示词")
                return sensitive_prompt.strip()
            else:
                logger.debug("远程敏感话题提示词为空")
                return ""
            
        except Exception as e:
            logger.warning(f"获取敏感话题提示词失败: {e}")
            return ""

    @staticmethod
    def get_sensitive_topic_prompt_sync() -> str:
        """获取敏感话题提示词（同步版本，兼容旧接口）"""
        # 现在service/prompt模块已改为同步，直接调用主方法
        return CommonComponents.get_sensitive_topic_prompt()

    @staticmethod
    def get_mongodb_prompt(prompt_type: str) -> str:
        """
        获取MongoDB提示词（从service/prompt模块）
        
        Args:
            prompt_type: 提示词类型，指定要获取的特定提示词分类
        
        Returns:
            str: 从MongoDB获取的提示词内容，如果失败则返回空字符串
        """
        try:
            from service.prompt.tool import PromptTool
            
            # 通过service/prompt模块获取指定类型的提示词（现在是同步调用）
            params = {}
            logger.info(f"调用PromptTool.get_prompt获取MongoDB提示词 - 场景: {prompt_type}, 参数: {params}")
            try:
                prompt_content = PromptTool.get_prompt(prompt_type, params)
                logger.info(f"PromptTool MongoDB提示词返回结果 - 场景: {prompt_type}, 长度: {len(prompt_content) if prompt_content else 0}, 内容预览: {prompt_content[:100] if prompt_content else 'None'}...")
            except Exception as call_e:
                logger.warning(f"PromptTool MongoDB提示词调用异常 - 场景: {prompt_type}, 异常: {call_e}")
                raise call_e
            
            if prompt_content and prompt_content.strip():
                logger.info(f"成功获取MongoDB提示词: {prompt_type}")
                return prompt_content.strip()
            else:
                logger.debug(f"MongoDB提示词为空: {prompt_type}")
                return ""
                
        except Exception as e:
            logger.warning(f"Failed to get MongoDB prompt {prompt_type}: {e}")
            return ""

    @staticmethod
    def build_default_chat_prompt() -> str:
        """构建默认聊天场景的基础提示词"""
        time_info = CommonComponents.get_current_time_info()
        
        return f"""The Current time is {time_info['currentTimeUTC']}. Now you are a professional and kind medical assistant. Now your task is answering the user question. Note: 1. Your answer should be brief 2. For what you do not know please return do not know, do not construct false data.

"""
