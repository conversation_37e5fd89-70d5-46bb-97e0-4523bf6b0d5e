"""
企业级远程提示词集成服务

核心设计原则：
1. 优先获取远程提示词，失败时降级到本地提示词
2. 只管理基础角色提示词，不涉及工具指导和公共提示词
3. 支持缓存、超时、重试等企业级特性
4. 统一异常处理和日志记录

@author: shaohua.sun
@date: 2025/7/7
"""

import asyncio
from typing import Optional, Dict, Any

from .remote_prompt_constants import (
    ScenePromptMapping, 
    RemotePromptConfig
)
from .remote_prompt_exceptions import (
    RemotePromptException,
    RemotePromptConnectionError,
    RemotePromptNotFoundError,
    RemotePromptTimeoutError,
    RemotePromptConfigurationError,
    RemotePromptServiceUnavailableError
)
from service.prompt.tool import PromptTool
from utils.biz_logger import get_logger

logger = get_logger(__name__)


class RemotePromptService:
    """
    企业级远程提示词集成服务
    
    提供统一的远程提示词获取接口，支持降级机制和企业级特性
    """
    
    def __init__(self):
        self._initialized = False
        self._service_available = True
        self._last_check_time = 0
        self._check_interval = 60  # 服务可用性检查间隔（秒）
    
    def _init_service(self):
        """初始化服务（延迟初始化）"""
        if self._initialized:
            return
        
        try:
            # 这里可以添加服务健康检查逻辑
            self._initialized = True
            logger.info(f"{RemotePromptConfig.LOG_PREFIX} Remote prompt service initialized")
        except Exception as e:
            logger.warning(f"{RemotePromptConfig.LOG_PREFIX} Failed to initialize service: {e}")
            self._service_available = False
    
    def get_base_role_prompt(
        self, 
        scene: str, 
        fallback_prompt: str = None,
        params: Dict[str, Any] = None
    ) -> str:
        """
        获取基础角色提示词（优先远程，失败时降级到本地）
        
        Args:
            scene: 场景名称（如 "NUTRITION_ANALYSIS"）
            fallback_prompt: 降级使用的本地提示词
            params: 模板渲染参数
            
        Returns:
            str: 提示词内容（远程或本地）
        """
        # 1. 尝试获取远程提示词
        remote_prompt = self._fetch_remote_prompt(scene, params)
        
        # 2. 远程获取成功，直接返回
        if remote_prompt:
            logger.info(f"{RemotePromptConfig.LOG_PREFIX} Using remote prompt for scene: {scene}")
            return remote_prompt
        
        # 3. 远程获取失败，降级到本地提示词
        if fallback_prompt:
            logger.warning(f"{RemotePromptConfig.LOG_PREFIX} Fallback to local prompt for scene: {scene}")
            return fallback_prompt
        
        # 4. 本地提示词也不存在，抛出异常
        logger.error(f"{RemotePromptConfig.LOG_PREFIX} No prompt available for scene: {scene}")
        raise RemotePromptNotFoundError(scene)
    
    def get_sensitive_topic_prompt(self, params: Dict[str, Any] = None) -> str:
        """
        获取敏感话题提示词
        
        Args:
            params: 模板渲染参数
            
        Returns:
            str: 敏感话题提示词，获取失败时返回空字符串
        """
        try:
            # 初始化服务
            self._init_service()
            
            if not self._service_available:
                logger.debug(f"{RemotePromptConfig.LOG_PREFIX} Service unavailable, skipping sensitive topic prompt")
                return ""
            
            # 使用场景映射获取敏感话题提示词
            remote_scene = ScenePromptMapping.SENSITIVE_TOPIC_MAPPING
            prompt = self._fetch_remote_prompt_with_retry(remote_scene, params or {})
            
            if prompt:
                logger.info(f"{RemotePromptConfig.LOG_PREFIX} Retrieved sensitive topic prompt")
                return prompt
            else:
                logger.debug(f"{RemotePromptConfig.LOG_PREFIX} No sensitive topic prompt found")
                return ""
                
        except Exception as e:
            logger.warning(f"{RemotePromptConfig.LOG_PREFIX} Failed to get sensitive topic prompt: {e}")
            return ""
    
    def _fetch_remote_prompt(
        self, 
        scene: str, 
        params: Dict[str, Any] = None
    ) -> Optional[str]:
        """
        获取远程提示词（内部方法）
        
        Args:
            scene: 场景名称
            params: 模板渲染参数
            
        Returns:
            Optional[str]: 远程提示词内容，失败时返回None
        """
        try:
            # 初始化服务
            self._init_service()
            
            if not self._service_available:
                logger.debug(f"{RemotePromptConfig.LOG_PREFIX} Service unavailable, skipping remote fetch")
                return None
            
            # 获取远程场景键
            try:
                remote_scene = ScenePromptMapping.get_remote_scene_key(scene)
            except ValueError as e:
                logger.debug(f"{RemotePromptConfig.LOG_PREFIX} Scene not supported for remote prompt: {scene}")
                return None
            
            # 获取远程提示词
            prompt = self._fetch_remote_prompt_with_retry(remote_scene, params or {})
            return prompt
            
        except Exception as e:
            logger.warning(f"{RemotePromptConfig.LOG_PREFIX} Failed to fetch remote prompt for {scene}: {e}")
            return None
    
    def _fetch_remote_prompt_with_retry(
        self, 
        remote_scene: str, 
        params: Dict[str, Any]
    ) -> Optional[str]:
        """
        带重试机制的远程提示词获取
        
        Args:
            remote_scene: 远程场景键
            params: 模板渲染参数
            
        Returns:
            Optional[str]: 提示词内容
        """
        retry_count = 0
        last_exception = None
        
        while retry_count < RemotePromptConfig.DEFAULT_RETRY_COUNT:
            try:
                # 直接调用同步方法（不再需要超时处理）
                logger.info(f"{RemotePromptConfig.LOG_PREFIX} 调用PromptTool.get_prompt - 场景: {remote_scene}, 参数: {params}")
                prompt = PromptTool.get_prompt(remote_scene, params)
                logger.info(f"{RemotePromptConfig.LOG_PREFIX} PromptTool返回结果 - 场景: {remote_scene}, 长度: {len(prompt) if prompt else 0}, 内容预览: {prompt[:100] if prompt else 'None'}...")
                
                # 验证返回内容
                if prompt and prompt.strip():
                    return prompt.strip()
                else:
                    logger.debug(f"{RemotePromptConfig.LOG_PREFIX} Empty prompt returned for scene: {remote_scene}")
                    return None
                    
            except Exception as e:
                last_exception = RemotePromptConnectionError(f"Connection failed: {str(e)}")
                logger.warning(f"{RemotePromptConfig.LOG_PREFIX} Connection error on attempt {retry_count + 1}: {e}")
            
            retry_count += 1
            
            # 重试间隔（使用同步sleep）
            if retry_count < RemotePromptConfig.DEFAULT_RETRY_COUNT:
                import time
                time.sleep(0.5 * retry_count)  # 递增延迟
        
        # 所有重试都失败
        if RemotePromptConfig.ENABLE_FALLBACK:
            logger.warning(f"{RemotePromptConfig.LOG_PREFIX} All retries failed for {remote_scene}, enabling fallback")
            return None
        else:
            raise last_exception or RemotePromptServiceUnavailableError()
    
    async def health_check(self) -> bool:
        """
        服务健康检查
        
        Returns:
            bool: 服务是否可用
        """
        try:
            # 尝试获取一个简单的提示词来检查服务状态
            test_prompt = await asyncio.wait_for(
                PromptTool.get_prompt("health_check", {}),
                timeout=2.0
            )
            self._service_available = True
            return True
        except Exception as e:
            logger.warning(f"{RemotePromptConfig.LOG_PREFIX} Health check failed: {e}")
            self._service_available = False
            return False
    
    def is_service_available(self) -> bool:
        """检查服务是否可用"""
        return self._service_available
    
    def get_supported_scenes(self) -> list:
        """获取支持的场景列表"""
        return ScenePromptMapping.get_supported_scenes()


# 全局单例实例
_remote_prompt_service = None

def get_remote_prompt_service() -> RemotePromptService:
    """获取远程提示词服务单例"""
    global _remote_prompt_service
    if _remote_prompt_service is None:
        _remote_prompt_service = RemotePromptService()
    return _remote_prompt_service