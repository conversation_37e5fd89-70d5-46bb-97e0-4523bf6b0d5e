"""
远程提示词集成异常管理

定义统一的异常类型，确保错误处理的一致性和可维护性

@author: shaohua.sun  
@date: 2025/7/7
"""


class RemotePromptException(Exception):
    """远程提示词基础异常类"""
    
    def __init__(self, message: str, error_code: str = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "REMOTE_PROMPT_ERROR"
    
    def __str__(self):
        return f"[{self.error_code}] {self.message}"


class RemotePromptConnectionError(RemotePromptException):
    """远程提示词连接异常"""
    
    def __init__(self, message: str = "Failed to connect to remote prompt service"):
        super().__init__(message, "CONNECTION_ERROR")


class RemotePromptNotFoundError(RemotePromptException):
    """远程提示词不存在异常"""
    
    def __init__(self, scene: str):
        message = f"Remote prompt not found for scene: {scene}"
        super().__init__(message, "PROMPT_NOT_FOUND")
        self.scene = scene


class RemotePromptTimeoutError(RemotePromptException):
    """远程提示词请求超时异常"""
    
    def __init__(self, timeout: float):
        message = f"Remote prompt request timeout after {timeout}s"
        super().__init__(message, "REQUEST_TIMEOUT")
        self.timeout = timeout


class RemotePromptConfigurationError(RemotePromptException):
    """远程提示词配置异常"""
    
    def __init__(self, message: str = "Remote prompt service configuration error"):
        super().__init__(message, "CONFIGURATION_ERROR")


class RemotePromptRenderError(RemotePromptException):
    """远程提示词渲染异常"""
    
    def __init__(self, template_error: str):
        message = f"Failed to render remote prompt template: {template_error}"
        super().__init__(message, "RENDER_ERROR")
        self.template_error = template_error


class RemotePromptServiceUnavailableError(RemotePromptException):
    """远程提示词服务不可用异常"""
    
    def __init__(self, message: str = "Remote prompt service is currently unavailable"):
        super().__init__(message, "SERVICE_UNAVAILABLE")