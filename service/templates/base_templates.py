"""
基础模板管理器

提供模板管理的基础架构和通用功能

@author: shaohua.sun
@date: 2025/7/2
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from .common_components import CommonComponents


class TemplateManager:
    """基础模板管理器"""
    
    @staticmethod
    def build_enhanced_prompt_unified(
        base_prompt: str,
        include_time_info: bool = True,
        include_sensitive: bool = True,
        include_formatting: bool = True,
        emoji_set: str = "general"
    ) -> str:
        """
        构建增强版提示词（统一方法，同步方式处理敏感词）

        Args:
            base_prompt: 基础提示词
            include_time_info: 是否包含时间信息
            include_sensitive: 是否包含敏感话题提示词
            include_formatting: 是否包含格式化规则
            emoji_set: 表情符号集合

        Returns:
            str: 增强版提示词
        """
        components = []

        # 1. 时间信息
        if include_time_info:
            components.append(CommonComponents.build_system_time_info())

        # 2. 敏感话题提示词（同步方式获取）
        if include_sensitive:
            sensitive_prompt = CommonComponents.get_sensitive_topic_prompt_sync()
            if sensitive_prompt:
                components.append(sensitive_prompt)

        # 3. 格式化规则
        if include_formatting:
            components.append(CommonComponents.build_mobile_formatting_rules())
            components.append(CommonComponents.build_markdown_guidelines(emoji_set))
            components.append(CommonComponents.build_required_behaviors())

        # 4. 基础提示词
        components.append(base_prompt)

        return "\n".join(components)

