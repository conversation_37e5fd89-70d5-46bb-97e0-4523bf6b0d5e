"""
prompt 模板渲染模块
- 支持 {var} 变量替换
- 异步友好，便于扩展
"""

from typing import Dict
import re

class PromptTemplate:
    """Prompt 模板渲染，支持 {var} 变量替换"""
    var_pattern = re.compile(r"{(.*?)}")

    @staticmethod
    def render(template: str, params: Dict[str, str]) -> str:
        """同步渲染"""
        def replacer(match):
            key = match.group(1)
            return str(params.get(key, f"{{{key}}}"))
        return PromptTemplate.var_pattern.sub(replacer, template)

    @staticmethod
    async def async_render(template: str, params: Dict[str, str]) -> str:
        return PromptTemplate.render(template, params) 