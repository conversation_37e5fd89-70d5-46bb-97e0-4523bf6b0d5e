"""
prompt service 单元测试
- mock 数据源
- 覆盖模板渲染、service、工具类
"""

import unittest
from .datasource import PromptDataSource, Prompt_Mgnt_Prompts
from .prompt_service import PromptService
from .tool import PromptTool
from .scenes import PromptScene

class MockPromptDataSource(PromptDataSource):
    def get_prompt_by_scene(self, scene: str) -> Prompt_Mgnt_Prompts:
        if scene in [
            "TEST_SCENE",
            "OTHER_SCENE_SYSTEM_PROMPTS",
            PromptScene.HEALTH_ADVISOR_SYSTEM_PROMPTS,
            PromptScene.HEALTH_CONSULTATION_SYSTEM_PROMPTS
        ]:
            return Prompt_Mgnt_Prompts(
                id=1,
                name="test",
                content="Hello, {user}! Your score is {score}.",
                description="test desc",
                type=scene
            )
        return None

class PromptServiceTestCase(unittest.TestCase):
    def setUp(self):
        self.mock_ds = MockPromptDataSource()
        self.service = PromptService(self.mock_ds)
        PromptTool.init_with_datasource(self.mock_ds)

    def test_get_prompt_with_mock(self):
        result = self.service.get_prompt(PromptScene.HEALTH_ADVISOR_SYSTEM_PROMPTS, {"user": "Bob", "score": 88})
        self.assertEqual(result, "Hello, Bob! Your score is 88.")

    def test_prompt_render(self):
        result = self.service.get_prompt(PromptScene.HEALTH_CONSULTATION_SYSTEM_PROMPTS, {"user": "Alice", "score": 95})
        print("result Alice:", result, "\n")
        self.assertIsNotNone(result)

    def test_tool(self):
        result = PromptTool.get_prompt(PromptScene.HEALTH_ADVISOR_SYSTEM_PROMPTS, {"user": "Bob", "score": 88})
        print("result Bob:", result, "\n")
        self.assertIsNotNone(result)

    def test_build_system_prompts_head(self):
        messages = [{"role": "user", "content": "hello"}]
        system_prompts = ["sys_head"]
        new_msgs = PromptTool.build_system_prompts(messages, system_prompts, index=0)
        self.assertEqual(new_msgs[0]["role"], "system")
        self.assertEqual(new_msgs[0]["content"], "sys_head")
        self.assertEqual(new_msgs[1]["role"], "user")
        self.assertEqual(new_msgs[1]["content"], "hello")

    def test_build_system_prompts_insert_head(self):
        messages = [{"role": "user", "content": "hi"}]
        system_prompts = ["sys1", "sys2"]
        new_msgs = PromptTool.build_system_prompts(messages, system_prompts, index=0)
        print("new_msgs:", new_msgs)
        self.assertEqual(new_msgs[0]["role"], "system")
        self.assertEqual(new_msgs[1]["content"], "sys2")
        self.assertEqual(new_msgs[2]["role"], "user")

    def test_build_system_prompts_append(self):
        messages = [{"role": "user", "content": "hi"}]
        system_prompts = ["sys3"]
        new_msgs = PromptTool.build_system_prompts(messages, system_prompts)
        self.assertEqual(new_msgs[-1]["content"], "sys3")

    def test_build_enhanced_system_prompts_context(self):
        messages = []
        system_prompts = ["hello, {user}"]
        context = {"user": "张三"}
        new_msgs = PromptTool.build_enhanced_system_prompts(messages, system_prompts, context)
        self.assertEqual(new_msgs[0]["content"], "hello, 张三")

if __name__ == "__main__":
    unittest.main() 