"""
prompt 场景枚举类型
- 统一管理所有典型场景
- 便于类型安全和代码提示
"""

from enum import Enum

class PromptScene(str, Enum):
    #默认场景,聊天模式
    OTHER_SCENE_SYSTEM_PROMPTS = "system"

    # 营养分析场景
    NUTRITION_ANALYSIS_SYSTEM_PROMPTS = "nutrition_analysis_system_prompts"

    #健康顾问场景
    HEALTH_ADVISOR_SYSTEM_PROMPTS = "health_consultation_system_prompts"

    #运动追踪场景
    EXERCISE_TRACKING_SYSTEM_PROMPTS = "exercise_tracking_system_prompts"

    #水分追踪场景
    HYDRATION_TRACKING_SYSTEM_PROMPTS = "hydration_tracking_system_prompts"

    #睡眠追踪场景
    SLEEP_TRACKING_SYSTEM_PROMPTS = "sleep_tracking_system_prompts"

    #健康分析场景
    HEALTH_ANALYTICS_SYSTEM_PROMPTS = "health_analytics_system_prompts"

    #预约管理场景 数据库原来是 APP_SCENE_APT_SYS
    APPOINTMENT_MANAGEMENT_SYSTEM_PROMPTS = "appointment_management_system_prompts"

    #健康咨询助手场景 废弃
    HEALTH_CONSULTATION_SYSTEM_PROMPTS = "health_consultation_system_prompts"

    #敏感话题提示词
    SENSITIVE_TOPIC_SYSTEM_PROMPT = "sensitive_topic_prompt"