"""
prompt 统一调用工具类
- 自动读取配置并初始化数据源
- 对外暴露统一接口
- 屏蔽数据源和 service 细节
"""

from typing import Dict, Optional
from .prompt_service import PromptService
from .datasource import MongoPromptDataSource, ApiPromptDataSource
from utils.config import get_prompt_config
from .template import PromptTemplate


class PromptTool:
    """Prompt 统一调用工具类（自动配置，单例模式）"""
    _service: Optional[PromptService] = None
    _mongo_client = None
    _db_name = None

    @classmethod
    def init_with_mongo_client(cls, client, db_name):
        cls._mongo_client = client
        cls._db_name = db_name
        cls._service = None  # 强制下次重新初始化

    @classmethod
    def _init_service(cls):
        if cls._mongo_client and cls._db_name:
            datasource = MongoPromptDataSource(cls._mongo_client, cls._db_name)
        else:
            cfg = get_prompt_config()
            source_type = cfg.get('source_type', 'mongo')
            if source_type == 'mongo':
                # 只允许用全局注册的 mongo_client
                raise RuntimeError("PromptTool: 未注册全局 mongo_client，请在 main.py lifespan 事件中调用 PromptTool.init_with_mongo_client")
            elif source_type == 'api':
                datasource = ApiPromptDataSource(cfg.get('api_url'))
            else:
                raise ValueError(f"不支持的数据源类型: {source_type}")
        cls._service = PromptService(datasource)

    @classmethod
    def get_prompt(cls, scene: str, params: Dict[str, str]) -> Optional[str]:
        """
        获取 prompt
        :param scene: 场景
        :param params: 参数
        :return: prompt
        """
        if not cls._service:
            cls._init_service()
        return cls._service.get_prompt(scene, params)

    @classmethod
    def build_system_prompts(cls, messages: list, system_prompts: list, index: int = None) -> list:
        """
        将 system_prompts 插入到消息流 messages 的指定位置
        :param messages: 原始消息流（list，每条为 dict 或自定义结构）
        :param system_prompts: system prompt 列表（每条为 str 或 dict）
        :param index: 插入索引，None/超界则追加到尾部
        :return: 新的消息流 list
        """
        if not cls._service:
            cls._init_service()
        return cls._service.build_system_prompts(messages, system_prompts, index)

    @classmethod
    def build_enhanced_system_prompts(cls, messages: list, system_prompts: list, context: dict = None, index: int = None) -> list:
        """
        增强型 system prompt 组装，支持上下文动态增强
        :param messages: 原始消息流
        :param system_prompts: system prompt 列表
        :param context: 上下文参数（如需动态渲染）
        :param index: 插入索引
        :return: 新的消息流 list
        """
        if not cls._service:
            cls._init_service()
        return cls._service.build_enhanced_system_prompts(messages, system_prompts, context, index)

    @classmethod
    def init_with_datasource(cls, datasource):
        """
        测试专用：允许注入mock数据源
        :param datasource: PromptDataSource实例
        """
        cls._service = PromptService(datasource) 


    @classmethod
    async def render_template(cls, template: str, params: Dict[str, str]) -> str:
        """
        渲染模板字符串
        :param template: 模板字符串
        :param params: 参数字典
        :return: 渲染后的字符串
        """

        return await PromptTemplate.async_render(template, params)