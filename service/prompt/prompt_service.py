"""
prompt service 主体
- 组合数据源与模板渲染
- 支持多场景、多参数
- 暴露统一异步接口
"""

from typing import Dict, Optional
from .datasource import PromptDataSource, Prompt_Mgnt_Prompts
from .template import PromptTemplate

from utils.biz_logger import get_logger


class PromptService:
    """Prompt 统一服务，支持多数据源和多场景"""
    def __init__(self, datasource: PromptDataSource):
        self.datasource = datasource

    def get_prompt(self, scene: str, params: Dict[str, str]) -> Optional[str]:
        """
        获取渲染后的 prompt
        :param scene: 场景名
        :param params: 动态参数
        :return: 渲染后的 prompt
        """
        prompt_obj: Optional[Prompt_Mgnt_Prompts] = self.datasource.get_prompt_by_scene(scene)
        logger = get_logger(__name__)
        logger.info(f"成功获取远程提示词，场景: {scene}")
        if prompt_obj and prompt_obj.content:
            logger.info(f"Prompt对象内容长度: {len(prompt_obj.content)}")
        else:
            logger.info("Prompt对象内容为空")
        if not prompt_obj or not prompt_obj.content:
            return None
        return PromptTemplate.render(prompt_obj.content, params)

    def build_system_prompts(self, messages: list, system_prompts: list, index: int = None) -> list:
        """
        将 system_prompts 插入到消息流 messages 的指定位置
        :param messages: 原始消息流（list，每条为 dict 或自定义结构）
        :param system_prompts: system prompt 列表（每条为 str 或 dict）
        :param index: 插入索引，None/超界则追加到尾部
        :return: 新的消息流 list
        """
        if not system_prompts:
            return messages
        # 统一格式为 dict
        sys_msgs = [p if isinstance(p, dict) else {"role": "system", "content": p} for p in system_prompts]
        msgs = list(messages) if messages else []
        if index is None or index < 0 or index > len(msgs):
            msgs.extend(sys_msgs)
        else:
            for i, m in enumerate(sys_msgs):
                msgs.insert(index + i, m)
        return msgs

    def build_enhanced_system_prompts(self, messages: list, system_prompts: list, context: dict = None, index: int = None) -> list:
        """
        增强型 system prompt 组装，支持上下文动态增强
        :param messages: 原始消息流
        :param system_prompts: system prompt 列表
        :param context: 上下文参数（如需动态渲染）
        :param index: 插入索引
        :return: 新的消息流 list
        """
        # 动态渲染 system prompt
        sys_msgs = []
        for p in system_prompts:
            content = p
            if context and isinstance(p, str):
                content = PromptTemplate.render(p, context)
            sys_msgs.append({"role": "system", "content": content})
        msgs = list(messages) if messages else []
        if index is None or index < 0 or index > len(msgs):
            msgs.extend(sys_msgs)
        else:
            for i, m in enumerate(sys_msgs):
                msgs.insert(index + i, m)
        return msgs 