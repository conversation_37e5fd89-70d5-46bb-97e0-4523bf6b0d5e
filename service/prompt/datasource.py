"""
prompt 数据源适配层
- 支持 MongoDB、API 等多种数据源
- 异步接口
"""

from typing import Any, Dict, Optional, List
from abc import ABC, abstractmethod
from dataclasses import dataclass

# Prompt 数据结构
@dataclass
class Prompt_Mgnt_Prompts:
    id: Any
    name: str
    content: str
    description: Optional[str] = None
    type: Optional[str] = None
    display_type: Optional[List[int]] = None
    tag_ids: Optional[List[int]] = None
    create_user: Optional[str] = None
    create_time: Optional[Any] = None
    update_user: Optional[str] = None
    update_time: Optional[Any] = None

class PromptDataSource(ABC):
    """prompt 数据源抽象基类，所有数据源需实现"""
    @abstractmethod
    def get_prompt_by_scene(self, scene: str) -> Optional[Prompt_Mgnt_Prompts]:
        pass

class MongoPromptDataSource(PromptDataSource):
    """
    MongoDB 数据源适配器
    """
    PROMPT_MONGO_COLLECTION = "prompt"

    def __init__(self, mongo_client, db_name: str):
        self.client = mongo_client
        self.db = db_name
        self.collection = self.PROMPT_MONGO_COLLECTION

    def get_prompt_by_scene(self, scene: str) -> Optional[Prompt_Mgnt_Prompts]:
        doc = self.client[self.db][self.collection].find_one({"type": scene})
        if doc:
            if '_id' in doc:
                doc['id'] = str(doc.pop('_id'))
            return Prompt_Mgnt_Prompts(**doc)
        return None

class ApiPromptDataSource(PromptDataSource):
    """API 数据源适配器（预留接口）"""
    def get_prompt_by_scene(self, scene: str) -> Optional[Prompt_Mgnt_Prompts]:
        # TODO: 实现 API 调用
        raise NotImplementedError("同步环境下请使用MockPromptDataSource或实现同步数据源") 