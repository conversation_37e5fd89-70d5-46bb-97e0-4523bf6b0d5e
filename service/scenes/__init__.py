"""
健康场景模块

包含健康相关场景的完整处理架构：
- 基础架构（base）：定义统一的场景处理基类和上下文
- 场景实现（implementations）：具体的健康场景处理类
- 场景工具（tools）：各场景专用的工具函数
- 场景工厂（factory）：统一的场景创建和管理

基于ReactAgent和LangGraph构建，提供：
- 统一的消息管理和记忆机制
- 完整的工具调用能力
- 流式处理支持
- 与意图识别模块的无缝集成

注意：当前为接口设计版本，具体业务实现逻辑待后续开发

@author: shaohua.sun
@date: 2025/6/20
"""

# 基础架构
from .base import (
    BaseScene,
    SceneContext,
    UserInfo,
    scene_registry
)

# 场景工厂（主要入口）
from .factory import scene_factory

__all__ = [
    # 基础架构（类型导出）
    "BaseScene",
    "SceneContext",
    "UserInfo",

    # 核心实例（统一入口）
    "scene_registry",  # 场景注册器实例（推荐使用）
    "scene_factory",   # 场景工厂实例（推荐使用）

    # 场景实现类请按需导入：
    # from service.scenes.implementations import NutritionAnalysisScene, ...
]