"""
Appointment management tools implementation

@author: shaohua.sun
@date: 2025/7/2
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from langchain_core.tools import tool
import json
import time

from integration.services.hsd.service import HsdService
from utils.biz_time import biz_format_time_ch, get_current_cst_time
from utils.biz_logger import get_logger
from utils.user_info_cache import get_user_info_cache

logger = get_logger(__name__)

# Global HSD service instance
_hsd_service = None


def get_hsd_service() -> HsdService:
    """Get HSD service instance (singleton pattern)"""
    global _hsd_service
    if _hsd_service is None:
        _hsd_service = HsdService()
    return _hsd_service


def get_user_member_id(user_id: str) -> Optional[str]:
    """
    获取用户的会员ID（主要用于预约卡片跳转链接）
    
    Args:
        user_id: 用户ID（third_user_id）
        
    Returns:
        Optional[str]: 主要会员ID，如果获取失败则返回空字符串
    """
    try:
        # 从缓存获取用户的主要会员ID
        user_cache = get_user_info_cache()
        member_id = user_cache.get_primary_member_id(user_id)

        if member_id:
            logger.debug(f"从缓存获取会员ID: user_id={user_id}, member_id={member_id}")
            return member_id
        else:
            logger.warning(f"缓存中未找到用户会员ID信息: user_id={user_id}")
            logger.info("建议在工作流的用户ID转换阶段缓存用户信息，包括memberIdList")
            return ""  # 返回空字符串作为默认值

    except Exception as e:
        logger.error(f"获取用户会员ID失败: user_id={user_id}, error={str(e)}")
        return ""


@tool
def get_appointment_slot_list_tool(
        appt_from_timestamp: Optional[str] = None,
        appt_to_timestamp: Optional[str] = None,
        user_id: Optional[str] = None
) -> str:
    """
    🏥 **GET AVAILABLE APPOINTMENT SLOTS** 🏥
    
    Retrieve available appointment time slots from the healthcare system.
    
    **PURPOSE**:
    - Show available appointment times to users
    - Help users see what slots are available
    - Provide appointment options for user selection
    
    **WHEN TO USE**:
    - User want to get some recommend appointment slots
    - User asks about appointment availability
    - User wants to see available appointment times
    - User inquires about booking appointments
    - User want to go to the hospital or clinic
    - User wants to see a doctor

    **SIMPLE USAGE**:
    - Call this tool when user asks about appointments or booking appointments
    - Show the results to help user make choices
    - Let user specify preferences based on available options
    
    Args:
        appt_from_timestamp: Start date for search (optional, Format: YYYY-MM-DD HH:MM:SS)
        appt_to_timestamp: End date for search (optional, Format: YYYY-MM-DD HH:MM:SS)
        user_id: User ID for personalized search (automatically provided)
    
    Returns:
        str: JSON response with available appointment slots
    """
    try:
        # 参数默认值处理
        if not appt_from_timestamp:
            appt_from_timestamp = get_current_cst_time().strftime('%Y-%m-%d %H:%M:%S')
        if not appt_to_timestamp:
            # 解析完整的 appt_from_timestamp 时间字符串
            from_time = datetime.strptime(appt_from_timestamp, '%Y-%m-%d %H:%M:%S')
            appt_to_timestamp = (from_time + timedelta(days=2)).strftime('%Y-%m-%d %H:%M:%S')

        # 用户ID验证
        if not user_id or not user_id.strip():
            logger.warning("User ID is missing or empty")
            return json.dumps({
                "code": -1,
                "message": "Tool call failed: User ID is not available. Please ensure user is properly authenticated and try again.",
                "data": []
            }, ensure_ascii=False)

        # 调用HSD服务
        hsd_service = get_hsd_service()
        slot_wrapper_data = hsd_service.get_slots_java_compatible(
            appt_from_timestamp=appt_from_timestamp,
            appt_to_timestamp=appt_to_timestamp,
            user_id=user_id
        )

        if slot_wrapper_data:
            logger.info(f"Successfully retrieved {len(slot_wrapper_data)} slot wrapper(s)")
            result = {
                "code": 0,
                "message": "success",
                "data": [wrapper.model_dump() for wrapper in slot_wrapper_data]
            }
            return json.dumps(result, ensure_ascii=False)
        else:
            logger.warning("No slot data returned from HSD service")
            return json.dumps({
                "code": 0,
                "message": "No appointments available at this time",
                "data": []
            }, ensure_ascii=False)

    except Exception as e:
        logger.error(f"Get appointment slot list failed: {e}")
        # Always return valid JSON response even on complete failure
        return json.dumps({
            "code": 0,
            "message": "Appointment service is temporarily unavailable. Please try again later.",
            "data": []
        }, ensure_ascii=False)


@tool
def generate_appointment_recommendations_card_tool(
        hospital_name: str,
        clinic_name: str,
        schedule_id: str,
        appointment_date: Optional[str] = None,
        appointment_from_time: Optional[str] = None,
        appointment_to_time: Optional[str] = None,
        user_id: Optional[str] = None
) -> str:
    """
    **APPOINTMENT RECOMMENDATION CARD GENERATION**

    Generate appointment recommendation card using real schedule data.

    **CRITICAL WORKFLOW**:
    1. FIRST: get_appointment_slot_list_tool() returns slot data with scheduleId
    2. THEN: Extract scheduleId from slot data and call this tool
    3. NEVER use fake/generated schedule_id values
    4. Based on user requirements, such as time and distance, recommend 1-2 slots.
    5. Pay attention to the slot time. If it is earlier than the current system time, do not recommend it.

    **PARAMETER REQUIREMENTS**:
    - hospital_name: Hospital name (REQUIRED - from slot data)
    - clinic_name: Department/clinic name (REQUIRED - from slot data)
    - schedule_id: Real schedule ID (REQUIRED - extract from visitTimeList.scheduleId)
    - appointment_date: Date in YYYY-MM-DD format (REQUIRED - from slot data)
    - appointment_from_time: Time in HH:MM format (REQUIRED - from slot data: filed: "apptFromTime")
    - appointment_to_time: Time in HH:MM format (REQUIRED - from slot data: filed: "apptToTime")

    Args:
        hospital_name: Hospital name (required)
        clinic_name: Department/clinic name (required)
        schedule_id: Real schedule ID from slot data (required)
        appointment_date: Appointment date (required)
        appointment_from_time: Appointment from time (required)
        appointment_to_time: Appointment to time (required)
        user_id: User ID (optional)

    Returns:
        str: JSON array with single appointment card
    """
    try:
        logger.info(
            f"Generate appointment card: clinic={clinic_name}, schedule_id={schedule_id}, date={appointment_date}, time={appointment_from_time}")

        # 参数验证
        if not hospital_name or not hospital_name.strip():
            return json.dumps({
                "error": "医院信息不完整，无法生成预约卡片"
            }, ensure_ascii=False)

        if not clinic_name or not clinic_name.strip():
            return json.dumps({
                "error": "科室信息不完整，无法生成预约卡片"
            }, ensure_ascii=False)

        if not schedule_id or not schedule_id.strip():
            return json.dumps({
                "error": "预约时段信息不完整，无法生成预约卡片"
            }, ensure_ascii=False)

        # 系统参数验证
        if not user_id or not user_id.strip():
            logger.warning("User ID is missing or empty")
            return json.dumps({
                "error": "Tool call failed: User ID is not available. Please ensure user is properly authenticated and try again."
            }, ensure_ascii=False)

        # 生成预约卡片（使用真实的schedule_id）
        return _generate_appointment_cards(
            hospital_name=hospital_name,
            clinic_name=clinic_name,
            schedule_id=schedule_id,  # 传入真实的schedule_id
            appointment_date=appointment_date or "",
            appointment_from_time=appointment_from_time or "",
            appointment_to_time=appointment_to_time or "",
            user_id=user_id
        )

    except Exception as e:
        logger.error(f"Failed to generate appointment cards: {e}")
        # 始终返回有效JSON，避免工具调用完全失败
        error_card_content = {
            "title": "OVA",
            "subTitle": "Appointment Service",
            "summary": "Please try again later.",
            "coverImgUrl": "",
            "jumpUrl": "",
            "buttonConfirmText": "Confirm",
            "buttonCancelText": "",
            "status": 1,
            "contentId": f"error_{int(time.time() * 1000)}",
            "type": 1,
            "data": json.dumps({"error": "Service temporarily unavailable"})
        }

        return json.dumps([error_card_content], ensure_ascii=False)


def _generate_appointment_cards(
        hospital_name: str,
        clinic_name: str,
        schedule_id: str,
        appointment_date: Optional[str],
        appointment_from_time: Optional[str],
        appointment_to_time: Optional[str],
        user_id: Optional[str]
) -> str:
    """根据用户偏好生成预约卡片（使用真实schedule_id）"""
    try:
        logger.info(
            f"生成预约卡片: clinic={clinic_name}, schedule_id={schedule_id}, date={appointment_date}, time={appointment_from_time}")

        # 使用传入的真实schedule_id
        logger.info(f"使用真实 schedule_id: {schedule_id}")

        # 构建卡片数据（基于Java版本OvaCard结构）
        ova_card_data = {
            "hostpialName": hospital_name,
            "clinicName": clinic_name,
            "apptDate": appointment_date or "",
            "apptFromTime": appointment_from_time or "",
            "apptToTime": appointment_to_time or "",
            "scheduleId": schedule_id
        }

        # 检查预约状态（基于Java版本getSlotOrderId逻辑）
        slot_order_id = None
        if user_id and schedule_id.isdigit():
            try:
                hsd_service = get_hsd_service()
                status_data = hsd_service.check_appointment(user_id, [int(schedule_id)])
                if isinstance(status_data, dict):
                    slot_order_id = status_data.get(str(schedule_id))
                    if slot_order_id:
                        slot_order_id = slot_order_id.strip()
                        logger.info(f"检测到已预约状态: schedule_id={schedule_id}, slot_order_id={slot_order_id}")
            except Exception as e:
                logger.error(f"Failed to check appointment status: {e}")

        # 获取用户的会员ID（基于Java版本逻辑）
        member_id = get_user_member_id(user_id) if user_id else ""

        # 按钮配置（基于Java版本sendCardToIm逻辑）
        if slot_order_id:
            # 已预约状态
            button_text = "View Appointment"
            jump_url = f"/ihospital/#/appointment/detail?orderId={slot_order_id}"
            status = 2  # 已预约状态
        else:
            # 未预约状态
            button_text = "Book"
            # 拼接memberIdList中的第一个会员ID到跳转链接（基于Java版本逻辑）
            jump_url = f"/ihospital/#/appointment/choose/department?scheduleId={schedule_id}&memberId={member_id}&type=1"
            status = 1  # 未预约状态

        # Build card content structure
        card_content = {
            "title": "OVA",
            "subTitle": f"{clinic_name} - {hospital_name}",
            "summary": f"Appointment time: {appointment_date or ''} {appointment_from_time or ''}",
            "coverImgUrl": "",
            "jumpUrl": jump_url,
            "buttonConfirmText": button_text,
            "buttonCancelText": "",
            "status": status,
            "contentId": f"appointment_{schedule_id}_{int(time.time() * 1000)}",
            "type": 1,
            "data": json.dumps(ova_card_data, ensure_ascii=False)
        }

        logger.info(f"成功生成预约推荐卡片: {card_content}")
        return json.dumps([card_content], ensure_ascii=False)

    except Exception as e:
        logger.error(f"生成预约卡片失败: {e}")
        return json.dumps({
            "error": f"生成预约推荐时发生错误: {str(e)}"
        }, ensure_ascii=False)
