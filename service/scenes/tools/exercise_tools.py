"""
Exercise tracking tools implementation

Based on document requirements, provides complete exercise tracking functionality:
- Daily exercise data query and analysis
- Exercise record generation and tracking
- Exercise performance monitoring and optimization
- Exercise card data generation

@author: shaohua.sun
@date: 2025/7/2
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from langchain_core.tools import tool

from integration.services.ai_clock.service import AiClockService
from utils.biz_logger import get_logger

logger = get_logger(__name__)

# Global AI Clock service instance
_ai_clock_service = None

def get_ai_clock_service() -> AiClockService:
    """Get AI Clock service instance (singleton pattern)"""
    global _ai_clock_service
    if _ai_clock_service is None:
        _ai_clock_service = AiClockService()
    return _ai_clock_service


class ExerciseConstants:
    """Exercise tracking constants definition"""
    
    # Exercise types
    EXERCISE_TYPES = {
        "cardio": "Cardio",
        "strength": "Strength Training",
        "flexibility": "Flexibility",
        "sports": "Sports",
        "walking": "Walking",
        "running": "Running",
        "cycling": "Cycling",
        "swimming": "Swimming"
    }
    
    # Exercise recommendations
    EXERCISE_BASICS = {
        "daily_recommendations": {
            "adults": {"min": 30, "max": 60},  # 30-60 minutes per day
            "cardio_weekly": 150,  # 150 minutes per week
            "strength_weekly": 2   # 2 sessions per week
        },
        "intensity_levels": {
            1: "Light",
            2: "Moderate", 
            3: "Vigorous",
            4: "High Intensity"
        },
        "recommendations": [
            "Aim for at least 150 minutes of moderate exercise weekly",
            "Include both cardio and strength training",
            "Start slowly and gradually increase intensity",
            "Listen to your body and rest when needed",
            "Stay hydrated during exercise"
        ]
    }


@tool
def get_daily_detail_tool(
    start_date: str,
    end_date: str,
    user_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    🚨 **MANDATORY FIRST CALL** 🚨 Get user's daily exercise progress, goals, and historical exercise data
    
    **WHEN TO CALL (HIGH PRIORITY)**:
    - User asks about exercise progress: "我的运动进度如何?", "How's my exercise progress?"
    - User asks about exercise goals: "我的运动目标是什么?", "What are my exercise goals?"
    - User asks about recent exercise: "最近的运动情况", "recent exercise activities"
    - User asks for exercise analysis: "分析我的运动", "analyze my exercise"
    - User asks about exercise statistics: "运动统计", "exercise stats"
    
    **CALL EXAMPLES**:
    - "我这周运动了多少?" → Call with current week date range
    - "我昨天运动了吗?" → Call with yesterday's date
    - "最近一个月的运动情况" → Call with last month date range
    - "我的运动目标完成了吗?" → Call with today's date
    
    **CRITICAL**: This tool provides essential context for ALL exercise-related conversations. Always call this FIRST before providing exercise advice or analysis.
    
    Args:
        start_date: Start date in YYYY-MM-DD format (e.g., 2025-01-15)
        end_date: End date in YYYY-MM-DD format (e.g., 2025-01-15)
        user_id: User ID (automatically provided by system)
    
    Returns:
        Dict[str, Any]: Exercise data, progress analysis, and personalized recommendations
    """
    logger.info(f"Get user exercise daily detail: user_id={user_id}, start_date={start_date}, end_date={end_date}")
    
    try:
        if not user_id:
            return {
                "success": False,
                "error": "User ID cannot be empty",
                "data": {}
            }
        
        # Call AI Clock service to get daily detail data
        ai_clock_service = get_ai_clock_service()
        data = ai_clock_service.get_daily_detail(user_id, start_date, end_date)
        
        # Process response data
        if data:
            # Extract exercise data and calculate summary
            exercise_summary = _extract_exercise_summary(data, start_date, end_date)
            
            return {
                "success": True,
                "data": data,
                "summary": exercise_summary,
                "date_range": f"{start_date} to {end_date}",
                "recommendations": _generate_exercise_recommendations(exercise_summary)
            }
        else:
            return {
                "success": True,
                "data": {},
                "summary": _get_empty_exercise_summary(),
                "date_range": f"{start_date} to {end_date}",
                "note": "No exercise data found for the specified period"
            }
        
    except Exception as e:
        logger.error(f"Failed to get exercise data: {e}")
        return {
            "success": False,
            "error": f"Failed to get exercise data: {str(e)}",
            "data": {}
        }


@tool
def get_all_exercise_list_tool() -> str:
    """
    🚨 **MANDATORY PREREQUISITE TOOL** 🚨 Get all available exercise types with taskIDs, names, and icons
    
    **WHEN TO CALL (PREREQUISITE FOR RECORDING)**:
    - ALWAYS before calling generate_exercise_record_tool (MANDATORY)
    - When user mentions any exercise activity that needs recording
    - When you need to validate exercise types
    - When user asks about available exercise options: "有哪些运动类型?", "What exercise types are available?"
    - When user asks about supported exercises: "支持哪些运动?", "What exercises are supported?"
    
    **CALL EXAMPLES**:
    - Before recording: "我跑步了30分钟" → FIRST call this tool → THEN generate_exercise_record_tool
    - User inquiry: "你们支持哪些运动?" → Call this tool to show options
    - Exercise validation: Need to match user's exercise → Call this tool first
    
    **CRITICAL WORKFLOW REQUIREMENT**: 
    Call this tool BEFORE generate_exercise_record_tool when you need to match user's exercise type to valid taskID. This is NOT optional - it's MANDATORY for proper exercise recording.
    
    **STANDARD USAGE PATTERN**:
    1. User mentions exercise: "我做了30分钟跑步", "I did 30 minutes running"
    2. **IMMEDIATELY** call this tool to get exercise types
    3. Match user's exercise to appropriate taskId from the response
    4. **THEN** call generate_exercise_record_tool with correct taskId
    
    **SMART MATCHING EXAMPLES**:
    - User says "跑步/running" → Internally match to "Running" → Use corresponding taskId
    - User says "举重/weight lifting" → Internally match to "Strength Training" → Use corresponding taskId  
    - User says "瑜伽/yoga" → If not found → Use taskId=99999
    - User says "游泳/swimming" → Internally match to "Swimming" → Use corresponding taskId
    
    **CRITICAL OUTPUT RULES**:
    - NEVER expose taskId, trackRoute, or system fields to users
    - Only show user-friendly exercise names when user asks about available exercises
    - Use system mapping internally for exercise matching and parameter requirements
    - Ask for distance naturally (not "trackRoute requires distance")
    
    This tool provides exercise type mapping for internal system use and user-friendly exercise lists.

    Returns:
        str: Exercise types list with taskIDs, names, and route tracking information in formatted string
    """
    logger.info("Function call: getAllExerciseList")
    
    try:
        ai_clock_service = get_ai_clock_service()
        exercise_list_response = ai_clock_service.get_all_exercise_list()
        
        if exercise_list_response:
            exercise_data = exercise_list_response
            
            if exercise_data:
                # Build user-friendly exercise list (hide system fields)
                user_visible_exercises = []
                system_exercise_mapping = {}
                
                for exercise in exercise_data:
                    exercise_name = getattr(exercise, 'name', 'Unknown')
                    exercise_id = getattr(exercise, 'id', 0)
                    track_route = getattr(exercise, 'trackRoute', False)
                    exercise_icon = getattr(exercise, 'icon', '')
                    
                    # Store system mapping for internal use
                    system_exercise_mapping[exercise_name.lower()] = {
                        'taskId': exercise_id,
                        'trackRoute': track_route,
                        'name': exercise_name,
                        'icon': exercise_icon
                    }
                    
                    # Add to user-visible list (no system fields)
                    user_visible_exercises.append(exercise_name)
                
                # Format response for AI assistant (system fields included for matching)
                result = "SYSTEM EXERCISE MAPPING (for AI use only - DO NOT show to user):\\n"
                for exercise in exercise_data:
                    exercise_name = getattr(exercise, 'name', 'Unknown')
                    exercise_id = getattr(exercise, 'id', 0)
                    track_route = getattr(exercise, 'trackRoute', False)
                    
                    result += f"- {exercise_name}: taskId={exercise_id}, trackRoute={track_route}\\n"
                
                result += "\\nUSER-FRIENDLY EXERCISE LIST (safe to show user):\\n"
                result += "Available exercise types: " + ", ".join(user_visible_exercises) + "\\n"
                
                result += "\\n📝 AI INSTRUCTIONS:\\n"
                result += "- NEVER expose taskId or trackRoute to users\\n"
                result += "- Use system mapping to match user input to correct taskId\\n"
                result += "- For unknown exercises, use taskId=99999\\n"
                result += "- Ask for distance naturally if trackRoute=true\\n"
                
                logger.info(f"getAllExerciseList success, found {len(exercise_data)} exercise types")
                return f'"{result}"'
            else:
                logger.warn("getAllExerciseList returned empty data")
                return '"Tool call failed: No exercise types found. Please use taskId=99999 for unknown exercise types and continue with exercise recording."'
        else:
            logger.warn("getAllExerciseList failed, service returned None")
            return '"Tool call failed: Unable to retrieve exercise types list. Please use taskId=99999 for unknown exercise types and continue with exercise recording."'
    except Exception as e:
        logger.error(f"getAllExerciseList error: {e}")
        return '"Tool call failed: Exercise types list service is currently unavailable. Please use taskId=99999 for unknown exercise types and continue with exercise recording."'


@tool
def get_exercise_daily_tool(
    start_date: str,
    end_date: str,
    user_id: Optional[str] = None
) -> str:
    """
    📊 **DETAILED EXERCISE ANALYSIS TOOL** 📊 Get comprehensive exercise history for in-depth analysis
    
    **WHEN TO CALL (SPECIFIC ANALYSIS)**:
    - User asks for detailed exercise breakdown: "详细的运动记录", "detailed exercise records"
    - User asks for specific workout data: "具体的锻炼数据", "specific workout data"
    - User asks for exercise analysis: "运动分析", "exercise analysis"
    - User asks for workout breakdown: "锻炼分解", "workout breakdown"
    - User asks for exercise details: "运动详情", "exercise details"
    - User asks for comprehensive exercise review: "全面的运动回顾", "comprehensive exercise review"
    
    **CALL EXAMPLES**:
    - "详细分析我这周的运动" → Call with current week date range
    - "我昨天具体做了什么运动?" → Call with yesterday's date
    - "给我看看上个月的详细运动记录" → Call with last month date range
    - "分析我的运动模式" → Call with appropriate date range
    
    **DIFFERENCE FROM get_daily_detail_tool**:
    - get_daily_detail_tool: Basic progress and goals (CALL FIRST)
    - get_exercise_daily_tool: Detailed workout breakdowns (CALL FOR ANALYSIS)
    
    **USAGE PATTERN**:
    1. First call get_daily_detail_tool for basic context
    2. Then call this tool if user needs detailed analysis
    
    Args:
        start_date: Start date in YYYY-MM-DD format (e.g., 2025-01-15)
        end_date: End date in YYYY-MM-DD format (e.g., 2025-01-15)
        user_id: User ID (automatically provided by system)

    Returns:
        str: JSON formatted detailed exercise records with comprehensive workout breakdowns
    """
    logger.info(f"Get exercise daily data: user_id={user_id}, start_date={start_date}, end_date={end_date}")
    
    try:
        if not user_id:
            return '"Tool call failed: User ID is not available. Please ensure user is properly authenticated and try again."'
        
        if not start_date or start_date.strip() == "":
            from datetime import datetime
            start_date = datetime.now().strftime('%Y-%m-%d')
        if not end_date or end_date.strip() == "":
            end_date = start_date
        
        # Call AI Clock service to get exercise daily data
        ai_clock_service = get_ai_clock_service()
        response = ai_clock_service.get_exercise_daily(user_id, start_date, end_date)

        # Process response data
        if response:
            exercise_data = response
            
            result = f"User's detailed exercise records ({start_date} to {end_date}):\\n\\n"
            
            for date in exercise_data.keys():
                result += f"📅 {date}:\\n"
                
                records = exercise_data[date]
                if not records:
                    result += "  No exercise records\\n\\n"
                    continue
                
                total_duration = 0
                total_calories = 0
                
                for record in records:
                    result += f"  🏃‍♂️ {record.get('name', 'Unknown Exercise')}:\\n"
                    duration = record.get('duration', 0)
                    calories = record.get('calories', 0)
                    distance = record.get('distance', 0)
                    intensity = record.get('intensityLevel', 'Not specified')
                    
                    result += f"    Duration: {duration} minutes\\n"
                    result += f"    Calories burned: {calories} kcal\\n"
                    if distance > 0:
                        result += f"    Distance: {distance} meters\\n"
                    result += f"    Intensity: {intensity}\\n"
                    
                    total_duration += duration
                    total_calories += calories
                    result += "\\n"
                
                if total_duration > 0:
                    result += f"  📊 Daily Summary:\\n"
                    result += f"    Total Duration: {total_duration} minutes\\n"
                    result += f"    Total Calories: {total_calories} kcal\\n\\n"
            
            logger.info(f"getExerciseDaily success for user: {user_id}")
            return f'"{result}"'
        else:
            logger.warn("getExerciseDaily failed, service returned None")
            return '"Tool call failed: Unable to retrieve user\'s exercise records. Please inform the user that their exercise history is currently unavailable and continue with general exercise guidance."'
    
    except Exception as e:
        logger.error(f"getExerciseDaily error: {e}")
        return '"Tool call failed: Exercise records service is currently unavailable. Please inform the user that their exercise history cannot be accessed at the moment and continue with general exercise guidance."'


@tool
def generate_exercise_record_tool(
    task_id: int,
    duration: int,
    record_date: str,
    distance: Optional[float] = None,
    avg_heart_rate: Optional[int] = None,
    intensity_level: Optional[str] = None,
    user_id: Optional[str] = None
) -> str:
    """
    📝 **EXERCISE RECORD GENERATION TOOL** 📝 Generate exercise record card for workout logging
    
    **WHEN TO CALL (EXERCISE LOGGING)**:
    - User wants to log exercise: "我做了30分钟跑步", "I did 30 minutes running"
    - User wants to record workout: "记录我的锻炼", "record my workout"
    - User wants to add training session: "添加训练记录", "add training session"
    - User mentions specific exercise activity: "我刚跑了5公里", "I just ran 5km"
    - User wants to track exercise: "跟踪我的运动", "track my exercise"
    
    **CALL EXAMPLES**:
    - "我今天跑步30分钟" → Record running exercise
    - "记录我的健身房锻炼" → Record gym workout
    - "我做了1小时瑜伽" → Record yoga session
    - "登记我的游泳训练" → Record swimming training
    
    🚨 **CRITICAL PARAMETER COLLECTION WORKFLOW** 🚨
    
    **MANDATORY PRE-CALL REQUIREMENT**: 
    Before calling this tool, you MUST call get_all_exercise_list_tool() to obtain valid taskId values. NEVER guess or assume taskId values.
    
    **PARAMETER COLLECTION SEQUENCE**:
    1. **FIRST**: Call get_all_exercise_list_tool() to get available exercise types and their taskIds
    2. **THEN**: Match user's exercise description to appropriate taskId from the list
    3. **IF NO MATCH**: Use taskId=99999 for unknown exercise types
    4. **FINALLY**: Call this tool with the correct taskId
    
    **REQUIRED PARAMETERS (MUST COLLECT - NEVER SKIP)**:
    - task_id: Exercise type ID from get_all_exercise_list_tool, use 99999 for unknown types (MANDATORY)
    - duration: Exercise duration in SECONDS (MANDATORY, convert user input: 30min → 1800sec, 1hr → 3600sec)
    - record_date: Record date in YYYY-MM-DD format (MANDATORY, cannot be future date)
    - intensity_level: Exercise intensity: 'light', 'moderate', 'high' (MANDATORY, accepts Chinese: 轻度→'light', 中等→'moderate', 高强度→'high')
    
    **CONDITIONAL REQUIRED PARAMETERS (MANDATORY FOR SPECIFIC EXERCISES)**:
    - distance: Distance in METERS (MANDATORY for exercises with trackRoute=true, convert user input: 3km → 3000m, 1 mile → 1609m)
    
    **OPTIONAL PARAMETERS (RECOMMENDED BUT NOT REQUIRED)**:
    - avg_heart_rate: Average heart rate 40-220 BPM (OPTIONAL but recommended, use BPM directly)
    
    **PARAMETER VALIDATION RULES**:
    - Duration: Must be positive integer in SECONDS, max 86400 seconds (24 hours) - MANDATORY
    - Distance: Must be positive number in METERS for trackRoute exercises - CONDITIONAL MANDATORY
    - Heart Rate: Must be 40-220 BPM if provided - OPTIONAL
    - Date: Cannot be future date, format YYYY-MM-DD - MANDATORY
    - Intensity: Must be exact string 'light', 'moderate', or 'high' - MANDATORY
    
    **SMART QUESTIONING EXAMPLES**:
    - "To record your running, I need the following required information: 1) duration, 2) distance, 3) intensity. How long did you run?" (为了记录您的跑步，我需要知道以下必填信息：1)运动时长，2)跑步距离，3)运动强度。您跑了多长时间？)
    - "For route-tracking exercises like running, distance is required. Please provide running distance (km or meters)." (对于路径追踪运动如跑步，距离是必填项。请提供跑步距离（公里或米）。)
    - "Please tell me the exercise intensity: light, moderate, or high? (Required)" (请告诉我这次运动的强度：轻度、中等还是高强度？（必填）)
    - "What's your average heart rate? (Optional but recommended, range 40-220)" (您的平均心率是多少？（可选但建议提供，范围40-220）)
    
    **WORKFLOW EXAMPLE**:
    User: "I ran for 30 minutes" (我跑步30分钟)
    1. Call get_all_exercise_list_tool() → Get taskId for "Running" and check trackRoute=true
    2. Ask for MANDATORY distance: "How far did you run? Please specify in kilometers or meters."
    3. Ask for MANDATORY intensity: "What was the exercise intensity: light, moderate, or high?"
    4. Ask for RECOMMENDED heart rate: "What was your average heart rate during the run?"
    5. Convert parameters: duration=1800sec (30min), distance=3000m (3km), intensity='moderate'
    6. Call generate_exercise_record_tool(task_id=X, duration=1800, distance=3000, intensity_level='moderate', avg_heart_rate=150)
    
    Args:
        task_id: Exercise type ID (REQUIRED, get from get_all_exercise_list_tool)
        duration: Exercise duration in SECONDS (REQUIRED, convert user input: 30min → 1800sec)
        record_date: Record date in YYYY-MM-DD format (REQUIRED, cannot be future date)
        distance: Distance in METERS (CONDITIONAL REQUIRED for trackRoute exercises, convert: 3km → 3000m)
        avg_heart_rate: Average heart rate in BPM (OPTIONAL, range: 40-220)
        intensity_level: Exercise intensity: 'light', 'moderate', 'high' (REQUIRED)
        user_id: User ID (automatically provided by system)

    Returns:
        str: JSON formatted exercise record card data structure (用于前端展示，不进行实际数据存储)
    """
    logger.info(f"Generate exercise record: task_id={task_id}, duration={duration}, record_date={record_date}, distance={distance}, avg_heart_rate={avg_heart_rate}, intensity_level={intensity_level}")
    
    try:
        # Enhanced parameter validation with smart error messages
        if not task_id or task_id <= 0:
            return '"Tool call failed: Exercise type ID (taskId) is required. Please call get_all_exercise_list_tool() first to get available exercise types and their IDs."'
        
        if not duration or duration <= 0:
            return '"Tool call failed: Exercise duration must be a positive number in seconds. Please provide exercise duration (convert minutes to seconds: 30min = 1800sec)."'
        
        if duration > 86400:  # More than 24 hours in seconds
            return '"Tool call failed: Exercise duration cannot exceed 24 hours (86400 seconds). Please provide a reasonable exercise duration."'
        
        if not record_date:
            return '"Tool call failed: Record date is required. Please provide date in YYYY-MM-DD format."'
        
        # Date format validation with future date check
        try:
            record_dt = datetime.strptime(record_date, '%Y-%m-%d')
            # Check if date is not in the future
            if record_dt.date() > datetime.now().date():
                return '"Tool call failed: Record date cannot be in the future. Please use today or a past date."'
        except ValueError:
            return '"Tool call failed: Invalid date format. Please use YYYY-MM-DD format (e.g., 2024-01-15)"'
        
        # Intensity level validation (MANDATORY field)
        if not intensity_level or intensity_level.strip() == "":
            return '"Tool call failed: Exercise intensity level is required. Please provide intensity level: \'light\', \'moderate\', or \'high\' (supports Chinese: 轻度/中等/高强度)."'
        
        # Heart rate validation (if provided)
        if avg_heart_rate is not None and (avg_heart_rate < 40 or avg_heart_rate > 220):
            return '"Tool call failed: Average heart rate must be between 40-220 BPM. Please provide a realistic heart rate value."'
        
        # Get exercise type information from AI Clock service
        exercise_name = "General Exercise"
        exercise_icon = ""
        track_route = False
        goal_value = 30  # Default fallback goal  
        goal_unit = "Min"
        
        # Get user's actual exercise goal from business interface (match Java version)
        if user_id:
            try:
                # Directly call AI Clock service to get user's actual exercise goal (same as Java)
                ai_clock_service = get_ai_clock_service()
                data = ai_clock_service.get_daily_detail(user_id, record_date, record_date)
                
                if data:
                    
                    # Extract goal value from the actual user data (matching Java logic exactly)
                    for date_str, day_data in data.items():
                        if isinstance(day_data, list):  # day_data is List[TaskCategory]
                            categories = day_data
                            
                            for category in categories:
                                # category is TaskCategory model object
                                if hasattr(category, 'type') and hasattr(category, 'goals'):
                                    category_type = category.type if category.type else ''
                                    goals = category.goals
                                    
                                    # Match Java logic: look for physical_activity category type
                                    if category_type == 'physical_activity' and goals:
                                        for goal in goals:
                                            # goal is TaskGoal model object
                                            if hasattr(goal, 'goal') and goal.goal:
                                                # Extract actual goal value from user's data
                                                goal_info = goal.goal  # This is Goal model object
                                                if hasattr(goal_info, 'goalValue') and goal_info.goalValue is not None:
                                                    goal_value = goal_info.goalValue
                                                    goal_unit = goal_info.goalUnit if goal_info.goalUnit else 'Min'
                                                    logger.info(f"Found user's actual exercise goal: {goal_value} {goal_unit}")
                                                    break
                                    
                                    if goal_value != 30:  # Found actual goal, no need to continue
                                        break
                        
                        if goal_value != 30:  # Found actual goal, no need to continue
                            break
                            
            except Exception as e:
                logger.warning(f"Failed to get user's actual exercise goal, using default: {e}")
                # Continue with default values if goal extraction fails
        else:
            logger.warning("No user_id provided, using default exercise goal")
        
        # Try to get exercise info from AI Clock service
        try:
            ai_clock_service = get_ai_clock_service()
            exercise_data = ai_clock_service.get_all_exercise_list()
            
            if exercise_data:
                
                # Find matching exercise by task_id
                for exercise in exercise_data:
                    if hasattr(exercise, 'id') and exercise.id == task_id:
                        exercise_name = exercise.name if exercise.name else 'General Exercise'
                        exercise_icon = exercise.icon if exercise.icon else ''
                        # trackRoute is Optional[str] in model, convert to boolean
                        # Support multiple possible values: "Y", "true", "1", etc.
                        track_route = exercise.trackRoute in ["Y", "true", "1", "yes", "True"] if exercise.trackRoute else False
                        logger.info(f"Found exercise: name={exercise_name}, taskId={task_id}, trackRoute_raw={exercise.trackRoute}, track_route={track_route}")
                        break
                else:
                    # If not found and task_id is 99999, use "Other Exercise"
                    if task_id == 99999:
                        exercise_name = "Other Exercise"
                        exercise_icon = "https://healthresource-**********.cos.ap-singapore.myqcloud.com/30e19def-47eb-45b8-84f0-8fde00ef0195.png?imageMogr2/thumbnail/24x24/auto-orient"
                        track_route = False
        except Exception as e:
            logger.warn(f"Failed to get exercise info from service: {e}")
        
        # Distance validation for trackRoute exercises (MANDATORY field for route-tracking exercises)
        if track_route and (distance is None or distance <= 0):
            return f'"Tool call failed: Distance is required for {exercise_name} exercise. Please provide distance in meters (1km = 1000m, 1 mile = 1609m)."'
        
        # 使用用户提供的运动强度（不进行推算）- 与Java版本保持一致
        user_provided_intensity_level = intensity_level.lower().strip() if intensity_level else "light"
        
        # 规范化强度值 - 与Java版本保持一致
        if user_provided_intensity_level == "轻度":
            user_provided_intensity_level = "light"
        elif user_provided_intensity_level == "中等":
            user_provided_intensity_level = "moderate"
        elif user_provided_intensity_level == "高强度":
            user_provided_intensity_level = "high"
        # 保持原值，如果不在预期范围内，后续验证会处理（与Java版本一致）
        
        intensity_level = user_provided_intensity_level
        
        # Convert duration from seconds to minutes for display
        duration_minutes = duration
        
        # Build business data structure (matching Java version exactly)
        business_data = {
            "status": 1,
            "recordDate": record_date,
            "taskId": task_id,
            "name": exercise_name,
            "icon": exercise_icon,
            "duration": duration_minutes,
            "durationUnit": "Min",
            "intensityLevel": intensity_level,
            "goal": {
                "goalValue": goal_value,
                "goalUnit": goal_unit
            }
        }
        
        # Distance field: only include if exercise supports route tracking and distance is provided
        logger.info(f"Distance field check: track_route={track_route}, distance={distance}, distance_check={distance is not None and distance > 0}")
        if track_route and distance is not None and distance > 0:
            # Convert distance from meters to kilometers for display
            distance_km = distance
            business_data["distance"] = distance_km
            business_data["distanceUnit"] = "Km"
            logger.info(f"Distance field added: {distance_km} km")
        else:
            logger.info(f"Distance field skipped: track_route={track_route}, distance={distance}")
        
        # Heart rate field: only include if provided by user
        if avg_heart_rate is not None and avg_heart_rate > 0:
            business_data["avgHeartRate"] = avg_heart_rate
            business_data["avgHeartRateUnit"] = "BPM"
        
        # Build complete card structure with content wrapper (统一到工具层处理，保持与其他场景一致)
        import time
        import json

        card_content = {
            "title": "Exercise Record",
            "subTitle": "Workout Tracking",
            "summary": f"Your {exercise_name.lower()} exercise has been recorded",
            "coverImgUrl": "",
            "jumpUrl": "",
            "buttonConfirmText": "Confirm",
            "buttonCancelText": "",
            "status": 1,
            "contentId": f"exercise_{int(time.time() * 1000)}",
            "type": 3,  # RT-Sport according to standard card structure
            "data": json.dumps(business_data, ensure_ascii=False)
        }
        
        logger.info(f"Exercise record generated successfully: {duration_minutes} minutes of {exercise_name} on {record_date}")
        return json.dumps(card_content, ensure_ascii=False)
        
    except Exception as e:
        logger.error(f"Failed to generate exercise record: {e}")
        return '"Tool call failed: Unable to generate exercise record parameters. Please inform the user that the exercise recording function is currently unavailable and continue with exercise analysis only."'


def _extract_exercise_summary(data: Dict[str, Any], start_date: str, end_date: str) -> Dict[str, Any]:
    """Extract exercise summary from daily detail data"""
    total_exercise_minutes = 0
    total_days = 0
    exercise_days = 0
    active_days = 0
    
    try:
        # Calculate date range
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        total_days = (end_dt - start_dt).days + 1
        
        # Process each day's data
        for date_str, day_data in data.items():
            if isinstance(day_data, dict) and 'categories' in day_data:
                categories = day_data['categories']
                
                for category in categories:
                    if isinstance(category, dict) and 'goals' in category:
                        goals = category['goals']
                        
                        for goal in goals:
                            if isinstance(goal, dict):
                                # Filter exercise-related goals
                                goal_code = goal.get('goalCode', '')
                                title = goal.get('title', '').lower()
                                
                                if (goal_code == 'exercise' or 
                                    'exercise' in title or 
                                    'workout' in title or
                                    'fitness' in title or
                                    '运动' in title or
                                    '锻炼' in title):
                                    
                                    completed_value = goal.get('completedValue', 0)  # in minutes
                                    
                                    total_exercise_minutes += completed_value
                                    if completed_value > 0:
                                        exercise_days += 1
                                        
                                        # Consider active if >= 30 minutes
                                        if completed_value >= 30:
                                            active_days += 1
                                    break
    
    except Exception as e:
        logger.error(f"Error extracting exercise summary: {e}")
    
    # Calculate averages
    avg_exercise_minutes = round(total_exercise_minutes / total_days, 1) if total_days > 0 else 0
    exercise_consistency_rate = round(exercise_days / total_days * 100, 1) if total_days > 0 else 0
    active_days_rate = round(active_days / total_days * 100, 1) if total_days > 0 else 0
    
    return {
        "total_exercise_minutes": total_exercise_minutes,
        "total_exercise_hours": round(total_exercise_minutes / 60, 1),
        "exercise_days": exercise_days,
        "active_days": active_days,
        "total_days": total_days,
        "avg_exercise_minutes": avg_exercise_minutes,
        "exercise_consistency_rate": exercise_consistency_rate,
        "active_days_rate": active_days_rate
    }


def _get_empty_exercise_summary() -> Dict[str, Any]:
    """Get empty exercise summary for cases with no data"""
    return {
        "total_exercise_minutes": 0,
        "total_exercise_hours": 0,
        "exercise_days": 0,
        "active_days": 0,
        "total_days": 0,
        "avg_exercise_minutes": 0,
        "exercise_consistency_rate": 0,
        "active_days_rate": 0
    }


def _generate_exercise_recommendations(summary: Dict[str, Any]) -> List[str]:
    """Generate exercise recommendations based on summary data"""
    recommendations = []
    
    avg_minutes = summary.get("avg_exercise_minutes", 0)
    consistency_rate = summary.get("exercise_consistency_rate", 0)
    active_days_rate = summary.get("active_days_rate", 0)
    
    # Exercise duration recommendations
    if avg_minutes < 15:
        recommendations.append("Your exercise level is below recommended. Aim for at least 30 minutes of activity daily.")
    elif avg_minutes < 30:
        recommendations.append("Good start! Try to increase your daily exercise to 30-60 minutes for optimal health benefits.")
    elif avg_minutes >= 60:
        recommendations.append("Excellent exercise duration! You're exceeding recommended guidelines.")
    else:
        recommendations.append("Great exercise habits! You're meeting the recommended 30+ minutes daily.")
    
    # Consistency recommendations
    if consistency_rate < 50:
        recommendations.append("Try to establish a more regular exercise routine for better fitness results.")
    elif consistency_rate < 80:
        recommendations.append("Good exercise consistency! Aim to be active most days of the week.")
    
    # Activity level recommendations
    if active_days_rate < 50:
        recommendations.append("Focus on increasing exercise intensity and duration for better health outcomes.")
    
    # General exercise tips
    if not recommendations:
        recommendations.append("Outstanding exercise habits! Continue maintaining this active lifestyle.")
    
    recommendations.extend([
        "Include both cardio and strength training",
        "Listen to your body and rest when needed"
    ])
    
    return recommendations[:3]  # Limit to 3 recommendations
