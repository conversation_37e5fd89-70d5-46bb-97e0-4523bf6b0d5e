"""
Hydration tracking tools implementation

Based on Java version requirements, provides core hydration tracking functionality:
- Daily hydration data query (getDailyDetail equivalent)
- Hydration record generation (recordHydration equivalent)

@author: shaohua.sun
@date: 2025/7/2
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from langchain_core.tools import tool
import json

from integration.services.ai_clock.service import AiClockService
from utils.biz_logger import get_logger

logger = get_logger(__name__)

# Global AI Clock service instance
_ai_clock_service = None

def get_ai_clock_service() -> AiClockService:
    """Get AI Clock service instance (singleton pattern)"""
    global _ai_clock_service
    if _ai_clock_service is None:
        _ai_clock_service = AiClockService()
    return _ai_clock_service


@tool
def get_daily_detail_tool(
    start_date: str,
    end_date: str,
    user_id: Optional[str] = None
) -> str:
    """
    MANDATORY: Get user's daily hydration progress, goals, and historical water intake data.
    
    This tool is REQUIRED for ALL hydration-related queries including: 'how much water did I drink today', 'my hydration progress', 'water intake history', 'recent drinking patterns', 'hydration status', 'daily water consumption', 'am I drinking enough', 'water intake summary', '今天喝了多少水', '我的饮水进度', '最近喝水情况', '饮水记录', '喝水够吗', '水分摄入量'.
    
    NEVER refuse with 'cannot access data' - this tool must be called for hydration queries.

    Args:
        start_date: Start date in YYYY-MM-DD format (e.g., 2025-01-15). If not specified, defaults to today.
        end_date: End date in YYYY-MM-DD format (e.g., 2025-01-15). If not specified, defaults to start_date.
        user_id: User ID (automatically provided by system)

    Returns:
        str: User's daily task details in formatted string (matching Java version exactly)
    """
    logger.info(f"Get user hydration daily detail: user_id={user_id}, start_date={start_date}, end_date={end_date}")

    try:
        # Parameter validation
        if not user_id:
            return "Tool call failed: User ID is not available. Please ensure user is properly authenticated and try again."

        # Default date handling
        if not start_date:
            start_date = datetime.now().strftime('%Y-%m-%d')
        if not end_date:
            end_date = start_date

        # Call AI Clock service to get daily detail data
        ai_clock_service = get_ai_clock_service()
        daily_detail_data = ai_clock_service.get_daily_detail(user_id, start_date, end_date)

        if daily_detail_data:
            # Format response exactly like Java version
            result = f"User's daily task details ({start_date} to {end_date}):\\n\\n"
            
            # Service layer already returns the data dict directly, not wrapped in response
            data = daily_detail_data
            for date_str in sorted(data.keys()):  # Sort dates chronologically
                result += f"📅 {date_str}:\\n"
                
                categories = data[date_str]
                if not categories:
                    result += "  No task data available\\n\\n"
                    continue
                
                for category in categories:
                    # Only process hydration-related categories (matching Java logic exactly)
                    category_type = getattr(category, 'type', '') if hasattr(category, 'type') else category.get('type', '') if isinstance(category, dict) else ''
                    category_title = getattr(category, 'title', '') if hasattr(category, 'title') else category.get('title', '') if isinstance(category, dict) else ''
                    
                    is_hydration_category = (category_type == 'hydration' or 
                                           category_type == 'drink_water' or
                                           (category_title and 'hydration' in category_title.lower()))
                    
                    # Check if has hydration goals
                    has_hydration_goal = False
                    goals = getattr(category, 'goals', []) if hasattr(category, 'goals') else category.get('goals', []) if isinstance(category, dict) else []
                    if goals:
                        for goal in goals:
                            goal_code = getattr(goal, 'goalCode', '') if hasattr(goal, 'goalCode') else goal.get('goalCode', '') if isinstance(goal, dict) else ''
                            goal_title = getattr(goal, 'title', '') if hasattr(goal, 'title') else goal.get('title', '') if isinstance(goal, dict) else ''
                            if (goal_code == 'drinkWater' or
                                (goal_title and ('hydration' in goal_title.lower() or 
                                                'water' in goal_title.lower() or 
                                                '饮水' in goal_title.lower()))):
                                has_hydration_goal = True
                                break
                    
                    if is_hydration_category or has_hydration_goal:
                        logger.info(f"Found hydration category: type={category_type}, title={category_title}")
                        
                        result += f"  💧 {category_title} ({category_type}):\\n"
                        
                        if goals:
                            for goal in goals:
                                goal_code = getattr(goal, 'goalCode', '') if hasattr(goal, 'goalCode') else goal.get('goalCode', '') if isinstance(goal, dict) else ''
                                goal_title = getattr(goal, 'title', '') if hasattr(goal, 'title') else goal.get('title', '') if isinstance(goal, dict) else ''
                                
                                # Filter hydration-related goals only
                                if (goal_code == 'drinkWater' or 
                                    (goal_title and ('water' in goal_title.lower() or 
                                                   'hydration' in goal_title.lower() or 
                                                   '饮水' in goal_title.lower()))):
                                    
                                    logger.info(f"Processing hydration goal: goalCode={goal_code}, title={goal_title}")
                                    
                                    completed_value = getattr(goal, 'completedValue', 0) if hasattr(goal, 'completedValue') else goal.get('completedValue', 0) if isinstance(goal, dict) else 0
                                    completed_pct = getattr(goal, 'completedPct', 0) if hasattr(goal, 'completedPct') else goal.get('completedPct', 0) if isinstance(goal, dict) else 0
                                    goal_info = getattr(goal, 'goal', None) if hasattr(goal, 'goal') else goal.get('goal', {}) if isinstance(goal, dict) else {}
                                    
                                    if goal_info:
                                        goal_value = getattr(goal_info, 'goalValue', 8) if hasattr(goal_info, 'goalValue') else goal_info.get('goalValue', 8) if isinstance(goal_info, dict) else 8
                                        goal_unit = getattr(goal_info, 'goalUnit', 'Glasses') if hasattr(goal_info, 'goalUnit') else goal_info.get('goalUnit', 'Glasses') if isinstance(goal_info, dict) else 'Glasses'
                                        goal_min = getattr(goal_info, 'goalMinValue', None) if hasattr(goal_info, 'goalMinValue') else goal_info.get('goalMinValue') if isinstance(goal_info, dict) else None
                                        goal_max = getattr(goal_info, 'goalMaxValue', None) if hasattr(goal_info, 'goalMaxValue') else goal_info.get('goalMaxValue') if isinstance(goal_info, dict) else None
                                    else:
                                        goal_value = 8
                                        goal_unit = 'Glasses'
                                        goal_min = None
                                        goal_max = None
                                    
                                    result += f"    🎯 {goal_title}:\\n"
                                    result += f"      Completed: {completed_value} {goal_unit}\\n"
                                    result += f"      Progress: {completed_pct}%\\n"
                                    result += f"      Goal: {goal_value} {goal_unit}"
                                    
                                    if goal_min is not None and goal_max is not None:
                                        result += f" (Range: {goal_min}-{goal_max})"
                                    result += "\\n"
                                    
                                    # Special hydration status (matching Java logic)
                                    if goal_code == 'drinkWater':
                                        remaining = goal_value - completed_value
                                        if remaining > 0:
                                            result += f"      💧 Need {remaining} more Glasses to reach goal\\n"
                                        else:
                                            result += f"      ✅ Hydration goal achieved! ({completed_value}/{goal_value} Glasses)\\n"
                        
                        result += "\\n"
            
            logger.info(f"getDailyDetail success for user: {user_id}")
            return f'"{result.replace(chr(34), chr(92)+chr(34))}"'  # Escape quotes for JSON string
        else:
            logger.warn(f"getDailyDetail failed, no data returned")
            return '"Tool call failed: Unable to retrieve user\'s daily task details. Please inform the user that their task data is currently unavailable and continue with general hydration guidance."'
            
    except Exception as e:
        logger.error(f"getDailyDetail error: {e}")
        return '"Tool call failed: Daily task service is currently unavailable. Please inform the user that their task data cannot be accessed at the moment and continue with general hydration guidance."'


@tool
def generate_hydration_record_tool(
    completed_value: int,
    record_date: str,
    user_id: Optional[str] = None
) -> str:
    """
    Generate hydration record card data structure for water intake logging and tracking.
    
    🚨 **SMART HYDRATION TRACKING** 🚨
    
    **WHEN TO CALL**: When user reports water consumption including:
    - Direct intake: 'I drank 500ml water', 'had 2 glasses of water', 'drank a bottle of water'
    - Logging requests: 'log my water intake', 'record drinking', 'add hydration'
    - Chinese expressions: '记录喝水500毫升', '添加饮水记录', '登记水分摄入', '喝了一瓶水', '补充了水分'
    
    **CRITICAL UNIT CONVERSION SYSTEM** (Match Java version exactly):
    - 1 Glass = 250ml (standard measurement unit)
    - 1 cup = 1 Glass
    - 1 bottle(500ml) = 2 Glasses
    - 1 liter = 4 Glasses
    
    **INTELLIGENT CONVERSION EXAMPLES**:
    - User: "I drank 500ml" → completedValue: 2 (glasses)
    - User: "had a bottle of water" → completedValue: 2 (glasses, assuming 500ml standard bottle)
    - User: "drank 1 liter" → completedValue: 4 (glasses)
    - User: "3 cups of water" → completedValue: 3 (glasses)
    - User: "250ml water" → completedValue: 1 (glass)
    
    **PARAMETER VALIDATION RULES**:
    - completedValue: Must be 1-20 glasses per day (realistic daily intake range)
    - recordDate: Cannot be future dates or older than 30 days
    - Use current date for 'today', yesterday's date for 'yesterday'
    
    **SMART QUESTIONING FOR MISSING INFO**:
    - "How much water did you drink? (in glasses, ml, or bottles)"
    - "When did you have this water? Today or another date?"
    - "Was it a small glass (250ml) or larger amount?"
    
    Args:
        completed_value: Number of glasses of water consumed in this intake (required). Must be positive integer between 1-20 Glasses per day. 1 Glass = 250ml. Convert from other units: 1 cup=1 Glass, 1 bottle(500ml)=2 Glasses, 1 liter=4 Glasses.
        record_date: Record date in yyyy-MM-dd format, e.g.: 2024-01-01. Cannot be future dates or older than 30 days. Use current date for 'today', yesterday's date for 'yesterday', etc. (required)
        user_id: User ID (automatically provided by system)

    Returns:
        str: JSON formatted hydration record card data structure for frontend display (generates card parameters, does not perform actual data storage)
    """
    logger.info(f"Generate hydration record: completed_value={completed_value}, record_date={record_date}")

    try:
        # Parameter validation (matching Java version exactly)
        if not completed_value or completed_value <= 0:
            return '"Tool call failed: Water intake amount must be a positive number. Please provide water intake amount in glasses."'

        if completed_value > 20:
            return '"Tool call failed: Water intake cannot exceed 20 glasses per day. Please provide a reasonable amount."'

        if not record_date:
            return '"Tool call failed: Record date is required. Please provide date in yyyy-MM-dd format."'

        # Date format validation
        try:
            record_dt = datetime.strptime(record_date, '%Y-%m-%d')
            # Check if date is not in the future (use real-time current date)
            current_date = datetime.now().date()  # 使用实时当前日期
            if record_dt.date() > current_date:
                return '"Tool call failed: Record date cannot be in the future. Please use today or a past date."'
            
            # Check if date is not older than 90 days (放宽限制以支持测试数据)
            ninety_days_ago = current_date - timedelta(days=90)
            if record_dt.date() < ninety_days_ago:
                return '"Tool call failed: Record date cannot be older than 90 days. Please provide a recent date."'
        except ValueError:
            return '"Tool call failed: Invalid date format. Please use yyyy-MM-dd format (e.g., 2025-01-01)"'

        # Determine unit display (singular/plural)
        cur_unit = "Glass" if completed_value == 1 else "Glasses"

        # Get user's current hydration data and goal information (matching Java version exactly)
        current_completed_value = 0
        goal_value = 8  # Default goal
        goal_unit = "Glasses"
        
        if user_id and user_id.strip():
            try:
                ai_clock_service = get_ai_clock_service()
                daily_detail_data = ai_clock_service.get_daily_detail(user_id, record_date, record_date)
                
                if daily_detail_data:
                    # Service layer already returns the data dict directly, not wrapped in response
                    data = daily_detail_data
                    for date_str in data.keys():
                        categories = data[date_str]
                        for category in categories:
                            goals = getattr(category, 'goals', []) if hasattr(category, 'goals') else category.get('goals', []) if isinstance(category, dict) else []
                            if goals:
                                for goal in goals:
                                    goal_code = getattr(goal, 'goalCode', '') if hasattr(goal, 'goalCode') else goal.get('goalCode', '') if isinstance(goal, dict) else ''
                                    if goal_code == 'drinkWater':
                                        current_completed_value = getattr(goal, 'completedValue', 0) if hasattr(goal, 'completedValue') else goal.get('completedValue', 0) if isinstance(goal, dict) else 0
                                        goal_info = getattr(goal, 'goal', None) if hasattr(goal, 'goal') else goal.get('goal', {}) if isinstance(goal, dict) else {}
                                        if goal_info:
                                            goal_value = getattr(goal_info, 'goalValue', -1) if hasattr(goal_info, 'goalValue') else goal_info.get('goalValue', -1) if isinstance(goal_info, dict) else -1
                                            goal_unit = "Glass" if goal_value == 1 else "Glasses"
                                            if goal_value == -1:
                                                # 抛异常，提示用户没有设置目标
                                                raise Exception("Failed to get current hydration data")
                                        break
            except Exception as e:
                logger.warning(f"Failed to get current hydration data: {e}")

        # Build business data structure (matching Java version exactly)
        business_data = {
            "status": 1,
            "recordDate": record_date,
            "curValue": completed_value,
            "completedValue": current_completed_value + completed_value,
            "curUnit": cur_unit,
            "goal": {
                "goalValue": goal_value,
                "goalUnit": goal_unit
            }
        }

        # Build complete card structure with content wrapper (统一到工具层处理)
        import time
        import json

        card_content = {
            "title": "Hydration Check-in",
            "subTitle": "Water Intake Tracking",
            "summary": "Please confirm your water intake record",
            "coverImgUrl": "",
            "jumpUrl": "",
            "buttonConfirmText": "Confirm",
            "buttonCancelText": "",
            "status": 1,
            "contentId": f"hydration_{int(time.time() * 1000)}",
            "type": 4,  # Matching Java version: type 4 for hydration cards
            "data": json.dumps(business_data, ensure_ascii=False)
        }

        logger.info(f"Hydration record generated successfully: {completed_value} glasses on {record_date}")
        return json.dumps(card_content, ensure_ascii=False)

    except Exception as e:
        logger.error(f"Failed to record hydration: {e}")
        return '"Tool call failed: Unable to generate hydration record parameters. Please inform the user that the water intake recording function is currently unavailable and continue with hydration guidance only."'
