"""
营养分析相关工具

基于AI Clock服务接口实现完整的营养分析功能：
- 营养记录生成工具
- 营养历史查询工具  
- 用餐时间信息工具
- 日期转换工具

@author: shaohua.sun
@date: 2025/7/2
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import json
from pydantic import BaseModel, Field
from langchain_core.tools import tool
from utils.biz_logger import get_logger
from integration.services.ai_clock.service import AiClockService

logger = get_logger(__name__)

# AI Clock服务单例
_ai_clock_service = None

def get_ai_clock_service() -> AiClockService:
    """Get AI Clock service instance (singleton pattern)"""
    global _ai_clock_service
    if _ai_clock_service is None:
        _ai_clock_service = AiClockService()
    return _ai_clock_service

# 营养分析常量
class NutritionConstants:
    """Nutrition analysis constants definition"""

    # Meal timing mapping (following document standard)
    MEAL_TIMING_MAP = {
        1: "Breakfast",
        2: "Lunch",
        3: "Dinner",
        4: "Snack"
    }
    
    # 营养素基础信息
    NUTRITION_BASICS = {
        "calories_per_gram": {
            "carbohydrate": 4,
            "protein": 4,
            "fat": 9
        },
        "daily_recommendations": {
            "calories": 2000,
            "protein": 50,
            "carbohydrate": 300,
            "fat": 65,
            "fiber": 25,
            "sodium": 2300
        }
    }

# 全局AI Clock服务实例
_ai_clock_service = None

def get_ai_clock_service() -> AiClockService:
    """获取AI Clock服务实例（单例模式）"""
    global _ai_clock_service
    if _ai_clock_service is None:
        _ai_clock_service = AiClockService()
    return _ai_clock_service


@tool
def get_meal_timing_info_tool() -> str:
    """
    Get meal timing codes reference. Call ONLY when unclear about timing parameter values for generate_nutrition_record_tool. Returns timing codes: 1=Breakfast, 2=Lunch, 3=Dinner, 4=Snack

    Returns:
        str: Meal timing information in JSON string format
    """
    logger.info("Function call: getMealTimingInfo")

    try:
        # Call AI Clock service to get meal timing information
        ai_clock_service = get_ai_clock_service()
        response = ai_clock_service.get_meal_timing()

        # Process response data
        if response:
            timing_data = response

            if timing_data:
                result = "Standard meal timing information:\\n"
                for item in timing_data:
                    result += f"- Code: {item.code}, Name: {item.name}\\n"
                result += "\\nDescription: 1-Breakfast, 2-Lunch, 3-Dinner, 4-Snack"

                logger.info(f"getMealTimingInfo success, returned {len(timing_data)} meal timings")
                return f'"{result}"'
            else:
                logger.warn("getMealTimingInfo failed, no data returned")
                return '"Tool call failed: Unable to retrieve meal timing information. Please use default meal timing (1-Breakfast, 2-Lunch, 3-Dinner, 4-Snack) and continue with your analysis."'
        else:
            logger.warn("getMealTimingInfo failed, service returned None")
            return '"Tool call failed: Unable to retrieve meal timing information. Please use default meal timing (1-Breakfast, 2-Lunch, 3-Dinner, 4-Snack) and continue with your analysis."'
    except Exception as e:
        logger.error(f"getMealTimingInfo error: {e}")
        return '"Tool call failed: Meal timing service is currently unavailable. Please use default meal timing (1-Breakfast, 2-Lunch, 3-Dinner, 4-Snack) and continue with your analysis."'


@tool
def get_user_nutrition_daily_tool(
    start_date: str,
    end_date: str,
    user_id: Optional[str] = None
) -> str:
    """
    MANDATORY: Get user's historical dietary records. Call for ANY query about past eating: 'what did I eat', 'recent meals', 'eating history', 'nutrition summary', '最近吃了什么', '昨天吃了什么'. NEVER refuse with 'cannot access data' - ALWAYS call this tool first for historical queries.

    Args:
        start_date: Start date, format: yyyy-MM-dd, e.g.: 2024-01-01. If not specified, defaults to today
        end_date: End date, format: yyyy-MM-dd, e.g.: 2024-01-01. If not specified, defaults to the same as start date
        user_id: User ID for nutrition data retrieval

    Returns:
        str: Nutrition daily report information in JSON string format
    """
    logger.info(f"Function call: getUserNutritionDaily with userId: {user_id}, startDate: {start_date}, endDate: {end_date}")

    try:
        # Parameter validation
        if not user_id or user_id.strip() == "":
            return '"Tool call failed: User ID is not available. Please ensure user is properly authenticated and try again."'

        # If no date specified, default to today
        if not start_date or start_date.strip() == "":
            from datetime import datetime
            start_date = datetime.now().strftime('%Y-%m-%d')
        if not end_date or end_date.strip() == "":
            end_date = start_date

        # Call AI Clock service to get nutrition data
        ai_clock_service = get_ai_clock_service()
        response = ai_clock_service.get_nutrition_daily(user_id, start_date, end_date)

        # Process response data
        if response:
            nutrition_data = response

            result = f"User's dietary records ({start_date} to {end_date}):\\n\\n"

            for date in nutrition_data.keys():
                result += f"📅 {date}:\\n"

                records = nutrition_data[date]
                if not records:
                    result += "  No dietary records\\n\\n"
                    continue

                for record in records:
                    meal_type = _get_timing_name(record.get('timing', 0))
                    result += f"  🍽️ {meal_type}:\\n"
                    result += f"    Total calories: {record.get('calories', 0)} kcal\\n"
                    result += f"    Carbohydrates: {record.get('carbohydrate', 0)} g\\n"
                    result += f"    Protein: {record.get('protein', 0)} g\\n"
                    result += f"    Fat: {record.get('fat', 0)} g\\n"

                    if record.get('foods'):
                        result += "    Food details:\\n"
                        for food in record['foods']:
                            result += f"      - {food.get('name', 'Unknown')}: {food.get('servingNumber', 0)} {food.get('servingUnit', '')} ({food.get('calories', 0)} kcal)\\n"

                    result += "\\n"

            logger.info(f"getUserNutritionDaily success for user: {user_id}")
            return f'"{result}"'
        else:
            logger.warn("getUserNutritionDaily failed, service returned None")
            return '"Tool call failed: Unable to retrieve user\'s dietary records. Please inform the user that their dietary history is currently unavailable and continue with general nutrition analysis."'

    except Exception as e:
        logger.error(f"getUserNutritionDaily error: {e}")
        return '"Tool call failed: Dietary records service is currently unavailable. Please inform the user that their dietary history cannot be accessed at the moment and continue with general nutrition analysis."'


@tool
def generate_nutrition_record_tool(
    timing: str,
    record_date: str,
    foods: List[Dict[str, Any]]
) -> str:
    """
    🚨 **CRITICAL MULTI-TURN NUTRITION CONVERSATION TOOL** 🚨

    Generate nutrition record JSON card when user reports food consumption.

    🧠 **MANDATORY CONVERSATION HISTORY UTILIZATION FOR NUTRITION** 🧠
    - **BEFORE CALLING THIS TOOL**: Systematically review ALL previous conversation messages
    - **EXTRACT ALL FOOD DATA**: Parse every previous message for food names, quantities, cooking methods, nutrition mentions
    - **CONNECT MEAL ELEMENTS**: Link current user input with previously mentioned food items and nutrition details
    - **MEMORY INTEGRATION**: Use conversation history to avoid asking for already-provided food/nutrition information

    **📊 INTELLIGENT NUTRITION INFORMATION GATHERING PROCESS**:
    - **Step 1 - Food History Scan**: Review all messages for food items, portions, cooking methods, meal timing, nutrition values
    - **Step 2 - Nutrition Data Inventory**: List what nutrition information has been mentioned in conversation history
    - **Step 3 - Nutrition Gap Identification**: Determine exactly which nutrition values are still missing
    - **Step 4 - Context-Aware Nutrition Collection**: Ask ONLY for missing nutrition info, referencing previously shared food data
    - **Step 5 - Meal Validation**: Confirm all collected food and nutrition data before making function call

    **MANDATORY PARAMETERS WITH VALIDATION**:
    - timing: Meal timing code (1-4) - 1=Breakfast, 2=Lunch, 3=Dinner, 4=Snack (required)
    - recordDate: Record date in yyyy-MM-dd format (required, CANNOT be future date)
    - foods: Array of food items with complete nutrition data (required, minimum 1 item)

    **FOODS ARRAY STRUCTURE** (Match Java version exactly):
    Each food item must contain:
    - foodName: Name of the food item (required)
    - servingNumber: Quantity/amount (required, numeric)
    - servingUnit: Unit of measurement (required: g, ml, piece, cup, etc.)
    - calories: Caloric content (required, numeric)
    - carbohydrate: Carbs in grams (optional, default 0)
    - protein: Protein in grams (optional, default 0)
    - fat: Fat in grams (optional, default 0)

    **CRITICAL DATE RULE**: recordDate MUST NOT be a future date - only today or past dates are allowed.

    **SERVING SIZE HANDLING RULES**:
    - For weights: '500g chicken' → servingNumber=500, servingUnit='g'
    - For volumes: '250ml milk' → servingNumber=250, servingUnit='ml'
    - For pieces: '2 apples' → servingNumber=2, servingUnit='piece'
    - NEVER use: servingNumber=1, servingUnit='500g' (incorrect format)

    **FOOD BREAKDOWN STRATEGY**: Break complex dishes into individual ingredients when possible.

    **INTELLIGENT QUESTIONING EXAMPLES**:
    - "For your breakfast oatmeal, do you know the approximate calories?"
    - "You mentioned chicken rice for lunch - can you estimate the portion size in grams?"
    - "What was the cooking method for the vegetables? (affects calorie calculation)"
    - "Do you have nutrition information for the [specific food item] you mentioned?"

    **CONTEXT-AWARE NUTRITION COLLECTION**:
    - ✅ ALWAYS reference conversation history when asking follow-up nutrition questions
    - ✅ ALWAYS acknowledge previously provided food and nutrition information
    - ✅ Use contextual questioning: 'For your lunch chicken rice, do you know the calories?'
    - ❌ NEVER ignore previously mentioned food items in conversation history
    - ❌ NEVER ask for food information that user has already provided in earlier messages

    Args:
        timing: Meal timing: 1-Breakfast, 2-Lunch, 3-Dinner, 4-Snack (required)
        record_date: Record date, format: yyyy-MM-dd, e.g.: 2024-01-01 (required)
        foods: List of food items consumed with nutrition data (required, at least one item)

    Returns:
        str: Nutrition record parameters in JSON string format
    """
    logger.info(f"Function call: generateNutritionRecord with timing: {timing}, recordDate: {record_date}, foods count: {len(foods) if foods else 0}")

    try:
        # Parameter validation - convert timing to int if it's string
        try:
            timing_int = int(timing) if isinstance(timing, str) else timing
        except (ValueError, TypeError):
            return '"Tool call failed: Invalid timing value. Please use: 1=Breakfast, 2=Lunch, 3=Dinner, 4=Snack"'

        if not foods or len(foods) == 0:
            return '"Tool call failed: Foods list is required. Please provide at least one food item with name and calories."'

        if timing_int is None or timing_int < 1 or timing_int > 4:
            return '"Tool call failed: Invalid timing value. Please use: 1=Breakfast, 2=Lunch, 3=Dinner, 4=Snack"'

        if not record_date:
            record_date = datetime.now().strftime('%Y-%m-%d')

        # Date format validation
        try:
            datetime.strptime(record_date, '%Y-%m-%d')
        except ValueError:
            return '"Tool call failed: Invalid date format. Please use YYYY-MM-DD format (e.g., 2024-01-15)"'

        # Get meal timing name
        timing_name = _get_timing_name(timing_int)

        # Calculate total calories
        total_calories = 0
        for food in foods:
            if food.get('calories') is not None:
                total_calories += int(food.get('calories', 0))

        # Determine main food name (first food or combined name)
        show_food_name = foods[0].get('foodName', 'Unknown') if foods else 'Unknown'
        if len(foods) > 1:
            show_food_name = show_food_name + " and others"

        # Build business data structure (based on Java version)
        business_data = {
            "status": 1,
            "timingName": timing_name,
            "timing": timing,
            "recordDate": record_date,
            "showFoodName": show_food_name,
            "calories": total_calories,
            "caloriesUnit": "Kcal" if total_calories == 1 else "Kcals",
            "foods": []
        }

        # Process foods data
        for food in foods:
            # Nutrition information
            food_calories = int(food.get('calories', 0))
            food_carbs = int(food.get('carbohydrate', 0))
            food_protein = int(food.get('protein', 0))
            food_fat = int(food.get('fat', 0))

            food_data = {
                "foodName": food.get("foodName", "Unknown"),
                "servingNumber": food.get("servingNumber", 1),
                "servingUnit": food.get("servingUnit", "piece"),
                "calories": food_calories,
                "caloriesUnit": "Kcal" if food_calories == 1 else "Kcals",
                "carbohydrate": food_carbs,
                "carbohydrateUnit": "g",
                "protein": food_protein,
                "proteinUnit": "g",
                "fat": food_fat,
                "fatUnit": "g"
            }
            business_data["foods"].append(food_data)

        # Build complete card structure with content wrapper (统一到工具层处理)
        import time
        import json

        card_content = {
            "title": "Nutrition Record",
            "subTitle": "Meal Tracking",
            "summary": "Your nutrition intake has been recorded",
            "coverImgUrl": "",
            "jumpUrl": "",
            "buttonConfirmText": "Confirm",
            "buttonCancelText": "",
            "status": 1,
            "contentId": f"nutrition_{int(time.time() * 1000)}",
            "type": 2,
            "data": json.dumps(business_data, ensure_ascii=False)
        }

        logger.info(f"generateNutritionRecord success, generated complete card for {len(foods)} foods")
        return json.dumps(card_content, ensure_ascii=False)

    except Exception as e:
        logger.error(f"generateNutritionRecord error: {e}")
        return '"Tool call failed: Unable to generate nutrition record parameters. Please inform the user that the meal recording function is currently unavailable and continue with nutrition analysis only."'


def _get_timing_name(timing: int) -> str:
    """Get meal timing name based on timing code"""
    timing_map = {
        1: "Breakfast",
        2: "Lunch",
        3: "Dinner",
        4: "Snack"
    }
    return timing_map.get(timing, "Unknown")






