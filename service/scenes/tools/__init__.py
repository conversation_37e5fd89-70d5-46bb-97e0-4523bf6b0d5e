"""
场景工具模块初始化

导出所有可用的场景工具

注意：当前为接口设计版本，具体业务实现逻辑待后续开发

@author: shaohua.sun
@date: 2025/6/20
"""

# 基础工具
from .basic_tools import get_current_time_tool

# 营养分析工具
from .nutrition_tools import (
    generate_nutrition_record_tool,
    get_user_nutrition_daily_tool,
    get_meal_timing_info_tool
)

# 运动追踪工具
from .exercise_tools import (
    get_daily_detail_tool as exercise_get_daily_detail_tool,
    generate_exercise_record_tool
)

# 饮水追踪工具
from .hydration_tools import (
    get_daily_detail_tool as hydration_get_daily_detail_tool,
    generate_hydration_record_tool
)

# 睡眠追踪工具
from .sleep_tools import (
    get_daily_detail_tool as sleep_get_daily_detail_tool,
    generate_sleep_record_tool
)

# 健康分析工具 (基于Java版本重构的3个核心工具)
from .health_analytics_tools import (
    get_comprehensive_health_data_tool,
    get_detailed_exercise_history_tool,
    get_detailed_nutrition_history_tool
)

# 预约管理工具（基于营养场景成功模式重构为2个核心工具）
from .appointment_tools import (
    get_appointment_slot_list_tool,
    generate_appointment_recommendations_card_tool
)

# 健康顾问工具
from .health_advisor_tools import (
    get_daily_detail_tool as health_advisor_get_daily_detail_tool
)

__all__ = [
    # 基础工具
    "get_current_time_tool",
    
    # 营养分析工具
    "generate_nutrition_record_tool",
    "get_user_nutrition_daily_tool", 
    "get_meal_timing_info_tool",
    
    # 运动追踪工具
    "exercise_get_daily_detail_tool",
    "generate_exercise_record_tool",
    
    # 饮水追踪工具
    "hydration_get_daily_detail_tool",
    "generate_hydration_record_tool",

    # 睡眠追踪工具
    "sleep_get_daily_detail_tool",
    "generate_sleep_record_tool",

    # 健康分析工具 (基于Java版本重构的3个核心工具)
    "get_comprehensive_health_data_tool",
    "get_detailed_exercise_history_tool", 
    "get_detailed_nutrition_history_tool",

    # 预约管理工具（基于营养场景成功模式重构为2个核心工具）
    "get_appointment_slot_list_tool",
    "generate_appointment_recommendations_card_tool",

    # 健康顾问工具
    "health_advisor_get_daily_detail_tool"
] 