"""
Sleep tracking tools implementation

Based on Java version requirements, provides core sleep tracking functionality:
- Daily sleep data query (getDailyDetail equivalent)
- Sleep record generation (generateSleepRecord equivalent)

@author: shaohua.sun
@date: 2025/7/2
"""

from typing import Optional
from datetime import datetime
from langchain_core.tools import tool

from integration.services.ai_clock.service import AiClockService
from utils.biz_logger import get_logger

logger = get_logger(__name__)

# Global AI Clock service instance
_ai_clock_service = None


def get_ai_clock_service() -> AiClockService:
    """Get AI Clock service instance (singleton pattern)"""
    global _ai_clock_service
    if _ai_clock_service is None:
        _ai_clock_service = AiClockService()
    return _ai_clock_service


@tool
def get_daily_detail_tool(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    user_id: Optional[str] = None
) -> str:
    """
    MANDATORY: Get user's daily sleep progress, goals, and historical sleep data. Call for ANY query about past sleep: 'how is my sleep', 'recent sleep patterns', 'sleep history', '最近睡眠怎么样', '昨天睡了多久'. NEVER refuse with 'cannot access data' - ALWAYS call this tool first for historical sleep queries.

    Args:
        start_date: Start date for querying daily details, format: yyyy-MM-dd, e.g.: 2024-01-01 (optional, defaults to today)
        end_date: End date for querying daily details, format: yyyy-MM-dd, e.g.: 2024-01-01 (optional, defaults to startDate)
        user_id: User ID (optional, will be handled by context)

    Returns:
        str: Formatted sleep data and analysis results as string
    """
    logger.info(f"Function call: getDailyDetail with userId: {user_id}, startDate: {start_date}, endDate: {end_date}")

    try:
        # Parameter validation
        if not user_id:
            return '"Tool call failed: User ID is not available. Please ensure user is properly authenticated and try again."'

        # If no date specified, default to today
        if not start_date or start_date.strip() == "":
            start_date = datetime.now().strftime('%Y-%m-%d')
        if not end_date or end_date.strip() == "":
            end_date = start_date

        # Call AI Clock service to get sleep data
        ai_clock_service = get_ai_clock_service()
        response = ai_clock_service.get_daily_detail(user_id, start_date, end_date)

        # Process response data (exactly matching Java version logic)
        if response:
            data = response

            # Build formatted result string (matching Java version format exactly)
            result = f"User's daily task details ({start_date} to {end_date}):\\n\\n"

            for date_str, day_data in data.items():
                result += f"📅 {date_str}:\\n"

                if not day_data or not isinstance(day_data, list):
                    result += "  No task records\\n\\n"
                    continue

                for category in day_data:
                    # Handle both Pydantic models and dict objects
                    category_type = getattr(category, 'type', '') if hasattr(category, 'type') else category.get('type', '') if isinstance(category, dict) else ''
                    if category_type == 'sleep':
                        category_title = getattr(category, 'title', 'Sleep') if hasattr(category, 'title') else category.get('title', 'Sleep') if isinstance(category, dict) else 'Sleep'
                        result += f"  😴 {category_title}:\\n"

                        goals = getattr(category, 'goals', []) if hasattr(category, 'goals') else category.get('goals', []) if isinstance(category, dict) else []
                        if goals:
                            for goal in goals:
                                if goal:
                                    goal_title = getattr(goal, 'title', 'Sleep Goal') if hasattr(goal, 'title') else goal.get('title', 'Sleep Goal') if isinstance(goal, dict) else 'Sleep Goal'
                                    completed_value = getattr(goal, 'completedValue', 0) if hasattr(goal, 'completedValue') else goal.get('completedValue', 0) if isinstance(goal, dict) else 0
                                    completed_pct = getattr(goal, 'completedPct', 0) if hasattr(goal, 'completedPct') else goal.get('completedPct', 0) if isinstance(goal, dict) else 0
                                    
                                    goal_info = getattr(goal, 'goal', None) if hasattr(goal, 'goal') else goal.get('goal', {}) if isinstance(goal, dict) else {}
                                    goal_unit = getattr(goal_info, 'goalUnit', 'MIN') if hasattr(goal_info, 'goalUnit') else goal_info.get('goalUnit', 'MIN') if isinstance(goal_info, dict) else 'MIN'
                                    goal_min = getattr(goal_info, 'goalMinValue', 0) if hasattr(goal_info, 'goalMinValue') else goal_info.get('goalMinValue', 0) if isinstance(goal_info, dict) else 0
                                    goal_max = getattr(goal_info, 'goalMaxValue', 0) if hasattr(goal_info, 'goalMaxValue') else goal_info.get('goalMaxValue', 0) if isinstance(goal_info, dict) else 0
                                    
                                    result += f"    🎯 {goal_title}:\\n"
                                    result += f"      Completed: {completed_value} {goal_unit}\\n"
                                    result += f"      Progress: {completed_pct}%\\n"
                                    result += f"      Goal: {goal_min}-{goal_max} {goal_unit}\\n"
                        result += "\\n"

            logger.info(f"getDailyDetail success for user: {user_id}")
            # Return JSON string format (matching Java version exactly)
            return '"' + result.replace('"', '\\"') + '"'
        else:
            logger.warning(f"getDailyDetail failed, response: {response}")
            return '"Tool call failed: Unable to retrieve user\'s daily task details. Please inform the user that their sleep history is currently unavailable and continue with general sleep analysis."'

    except Exception as e:
        logger.error(f"getDailyDetail error: {e}")
        return '"Tool call failed: Daily task details service is currently unavailable. Please inform the user that their sleep history cannot be accessed at the moment and continue with general sleep analysis."'


@tool
def generate_sleep_record_tool(
    record_date: str,
    completed_value: int
) -> str:
    """
    Generate sleep record JSON card when user reports sleep duration or wants to log sleep.
    
    🚨 **INTELLIGENT SLEEP TRACKING** 🚨
    
    **WHEN TO CALL**: Immediately when user mentions sleep duration or requests sleep logging:
    - Sleep reports: 'I slept 8 hours', 'got 7.5 hours of sleep', 'slept from 10pm to 6am'
    - Logging requests: 'log my sleep', 'record sleep', 'add sleep data'
    - Chinese expressions: '我睡了8小时', '记录睡眠', '昨晚睡了7个半小时', '添加睡眠记录'
    
    **CRITICAL TIME CONVERSION SYSTEM** (Match Java version exactly):
    - Input: MINUTES (not hours) - this is crucial for API compatibility
    - 1 hour = 60 minutes
    - 8 hours = 480 minutes
    - 7.5 hours = 450 minutes
    - 30 minutes nap = 30 minutes
    
    **INTELLIGENT CONVERSION EXAMPLES**:
    - User: "I slept 8 hours" → completedValue: 480 (minutes)
    - User: "got 7.5 hours sleep" → completedValue: 450 (minutes)
    - User: "slept from 10pm to 6am" → completedValue: 480 (minutes, 8 hours)
    - User: "30 minute nap" → completedValue: 30 (minutes)
    
    **PARAMETER VALIDATION RULES**:
    - completedValue: Must be positive, not exceed 1440 minutes (24 hours)
    - recordDate: MUST NOT be future date - only today or past dates allowed
    - Reasonable sleep range: 60-720 minutes (1-12 hours)
    
    **SMART QUESTIONING FOR MISSING INFO**:
    - "How long did you sleep? (in hours or minutes)"
    - "What date was this sleep for? Today or another date?"
    - "Did you sleep from X time to Y time? Let me calculate the duration."
    
    **DATE HANDLING INTELLIGENCE**:
    - "last night" → yesterday's date
    - "today" → current date
    - "yesterday" → yesterday's date
    - Specific dates → validate format and future date restriction
    
    MANDATORY parameters: recordDate(yyyy-MM-dd), completedValue(minutes). 
    CRITICAL: recordDate MUST NOT be a future date - only today or past dates are allowed. 
    Convert hours to minutes: 8 hours = 480 minutes.

    Args:
        record_date: Record date, format: yyyy-MM-dd, e.g.: 2024-01-01 (required)
        completed_value: Sleep duration in minutes (required, must be positive and not exceed 1440 minutes/24 hours)

    Returns:
        str: JSON formatted sleep record card data structure（用于前端展示，不进行实际数据存储）
    """
    logger.info(f"Function call: generateSleepRecord with recordDate: {record_date}, completedValue: {completed_value}")

    try:
        # Parameter validation (matching Java version ValidationHelper logic)
        if not record_date:
            return '"Tool call failed: Record date is required. Please provide date in YYYY-MM-DD format."'

        if not completed_value or completed_value <= 0:
            return '"Tool call failed: Sleep duration must be a positive number in minutes. Please provide sleep duration."'

        if completed_value > 1440:  # More than 24 hours
            return '"Tool call failed: Sleep duration cannot exceed 24 hours (1440 minutes). Please provide a valid sleep duration."'

        # Date format validation
        try:
            record_dt = datetime.strptime(record_date, '%Y-%m-%d')
            # Check if date is not in the future
            if record_dt.date() > datetime.now().date():
                return '"Tool call failed: Record date cannot be in the future. Please use today or a past date."'
        except ValueError:
            return '"Tool call failed: Invalid date format. Please use YYYY-MM-DD format (e.g., 2024-01-15)"'

        # Build business data structure (exactly matching Java version)
        business_data = {
            "status": 1,
            "recordDate": record_date,
            "completedValue": completed_value,
            "completedUnit": "MIN"
        }

        # Build complete card structure with content wrapper (统一到工具层处理)
        import time
        import json

        card_content = {
            "title": "Sleep Record",
            "subTitle": "Sleep Tracking",
            "summary": "Your sleep duration has been recorded",
            "coverImgUrl": "",
            "jumpUrl": "",
            "buttonConfirmText": "Confirm",
            "buttonCancelText": "",
            "status": 1,
            "contentId": f"sleep_{int(time.time() * 1000)}",
            "type": 5,
            "data": json.dumps(business_data, ensure_ascii=False)
        }

        logger.info(f"generateSleepRecord success, generated complete card for {completed_value} minutes")
        return json.dumps(card_content, ensure_ascii=False)

    except Exception as e:
        logger.error(f"generateSleepRecord error: {e}")
        return '"Tool call failed: Unable to generate sleep record parameters. Please inform the user that the sleep recording function is currently unavailable and continue with sleep analysis only."'
