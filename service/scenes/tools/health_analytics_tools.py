"""
Health analytics tools implementation

基于Java版本HealthAnalyticsFunctionTools的Python实现，提供3个核心工具：
1. get_comprehensive_health_data_tool - 获取综合健康数据概览（包括所有健康维度）
2. get_detailed_exercise_history_tool - 获取详细运动历史数据
3. get_detailed_nutrition_history_tool - 获取详细营养历史数据

@author: shaohua.sun
@date: 2025/7/4
"""

from typing import Dict, Any, List, Optional
from langchain_core.tools import tool
from datetime import datetime, timedelta
import json

from integration.services.ai_clock.service import AiClockService
from utils.biz_logger import get_logger

logger = get_logger(__name__)

# Global AI Clock service instance
_ai_clock_service = None

def get_ai_clock_service() -> AiClockService:
    """Get AI Clock service instance (singleton pattern)"""
    global _ai_clock_service
    if _ai_clock_service is None:
        _ai_clock_service = AiClockService()
    return _ai_clock_service


@tool
def get_comprehensive_health_data_tool(
    start_date: str,
    end_date: str,
    user_id: Optional[str] = None
) -> str:
    """
    MANDATORY: Get user's comprehensive health data overview including all health dimensions 
    (exercise, nutrition, sleep, hydration) with goal completion rates. Call for ANY 
    comprehensive health analysis request: 'analyze my health', 'overall progress', 
    'health summary', 'comprehensive report', '分析我的健康数据', '整体进度', '综合分析'. 
    NEVER refuse with 'cannot access data' - ALWAYS call this tool first for holistic health analysis.
    
    Args:
        start_date: Start date for health data analysis in yyyy-MM-dd format
        end_date: End date for health data analysis in yyyy-MM-dd format
        user_id: User ID (optional, will be set by scene context)
    
    Returns:
        str: Comprehensive health data analysis in formatted string
    """
    logger.info(f"GET_COMPREHENSIVE_HEALTH_DATA_START|START_DATE:{start_date}|END_DATE:{end_date}|USER_ID:{user_id}")
    
    try:
        if not user_id:
            logger.warn("GET_COMPREHENSIVE_HEALTH_DATA_NO_USER_ID")
            return '{"error": "User ID not available"}'
        
        # 验证和调整日期范围
        validated_dates = _validate_and_adjust_date_range(start_date, end_date)
        final_start_date = validated_dates[0]
        final_end_date = validated_dates[1]
        
        # 获取综合健康数据
        ai_clock_service = get_ai_clock_service()
        data = ai_clock_service.get_daily_detail(user_id, final_start_date, final_end_date)
        
        logger.info(f"查询结果: data_keys={list(data.keys()) if data else 'N/A'}")
        
        if data:
            
            # 构建综合健康数据分析结果
            result = []
            result.append(f"Comprehensive Health Data Analysis ({final_start_date} to {final_end_date}):\\n\\n")
            
            if not data:
                result.append("No health data recorded for this period\\n\\n")
                return "".join(result)
            
            # 统计变量用于计算平均值和汇总
            total_days = len(data)
            total_sleep_hours = 0.0
            total_steps = 0
            total_water_glasses = 0.0
            total_calories = 0.0
            total_protein = 0.0
            total_carbs = 0.0
            total_fat = 0.0
            total_exercise_minutes = 0
            total_exercise_calories = 0.0
            first_weight = -1.0
            last_weight = -1.0
            first_weight_date = None
            last_weight_date = None
            
            # 分析每日数据
            for date_str, categories in data.items():
                result.append(f"=== {date_str} ===\\n")
                
                if not categories:
                    result.append("No health data recorded\\n\\n")
                    continue
                
                for category in categories:
                    if not category:
                        continue
                    
                    category_type = getattr(category, 'type', None)
                    result.append(f"\\n{_format_category_name(category_type)}:\\n")
                    
                    goals = getattr(category, 'goals', None)
                    if goals:
                        for goal in goals:
                            if not goal:
                                continue
                            
                            goal_title = getattr(goal, 'title', 'Health Goal')
                            result.append(f"  • {goal_title}:\\n")
                            
                            # 目标信息 - 修复属性名称
                            goal_obj = getattr(goal, 'goal', None)
                            if goal_obj:
                                goal_value = getattr(goal_obj, 'goalValue', None)
                                goal_unit = getattr(goal_obj, 'goalUnit', None)
                                if goal_value is not None and goal_unit:
                                    result.append(f"    Target: {goal_value} {goal_unit}\\n")
                            
                            # 完成情况 - 修复属性名称
                            completed_value = getattr(goal, 'completedValue', None)
                            if completed_value is not None and completed_value >= 0:
                                goal_unit = getattr(goal_obj, 'goalUnit', '') if goal_obj else ''
                                result.append(f"    Completed: {completed_value} {goal_unit}\\n")
                                
                                # 累计数据用于计算平均值 - 修复属性名称
                                goal_code = getattr(goal, 'goalCode', None)
                                if goal_code:
                                    if goal_code == 'sleep' and goal_unit == 'min':
                                        total_sleep_hours += completed_value / 60.0
                                    elif goal_code == 'steps':
                                        total_steps += completed_value
                                    elif goal_code == 'drinkWater':
                                        total_water_glasses += completed_value
                                    elif goal_code == 'calories':
                                        total_calories += completed_value
                                    elif goal_code == 'protein':
                                        total_protein += completed_value
                                    elif goal_code == 'carbohydrates':
                                        total_carbs += completed_value
                                    elif goal_code == 'fat':
                                        total_fat += completed_value
                                    elif goal_code == 'aerobic' and goal_unit == 'min':
                                        total_exercise_minutes += completed_value
                                    elif goal_code == 'exercise_calories':
                                        total_exercise_calories += completed_value
                                    elif goal_code == 'weight' and completed_value > 0:
                                        if first_weight < 0:
                                            first_weight = completed_value
                                            first_weight_date = date_str
                                        last_weight = completed_value
                                        last_weight_date = date_str
                
                result.append("\\n")
            
            # 添加汇总统计信息
            if total_days > 0:
                result.append("\\n=== HEALTH SUMMARY STATISTICS ===\\n")
                result.append(f"Analysis Period: {total_days} days ({final_start_date} to {final_end_date})\\n\\n")
                
                # 睡眠统计
                if total_sleep_hours > 0:
                    avg_sleep = total_sleep_hours / total_days
                    result.append(f"Average Sleep Duration: {avg_sleep:.1f} hours/day\\n")
                
                # 步数统计
                if total_steps > 0:
                    avg_steps = total_steps / total_days
                    result.append(f"Daily Steps: {total_steps:,} total, {avg_steps:,.0f} average/day\\n")
                
                # 饮水统计
                if total_water_glasses > 0:
                    avg_water = total_water_glasses / total_days
                    water_liters = total_water_glasses * 0.25  # 假设每杯250ml
                    result.append(f"Average Water Intake: {avg_water:.1f} glasses ({water_liters:.1f} L)/day\\n")
                
                # 营养统计
                if total_calories > 0:
                    avg_calories = total_calories / total_days
                    result.append(f"Daily Average Calories: {avg_calories:.0f} kcal\\n")
                    
                    if total_protein > 0 and total_carbs > 0 and total_fat > 0:
                        protein_pct = (total_protein * 4 / total_calories) * 100
                        carbs_pct = (total_carbs * 4 / total_calories) * 100
                        fat_pct = (total_fat * 9 / total_calories) * 100
                        result.append(f"Macronutrient Distribution: Protein {protein_pct:.1f}%, Carbs {carbs_pct:.1f}%, Fat {fat_pct:.1f}%\\n")
                
                # 运动统计
                if total_exercise_minutes > 0:
                    avg_exercise = total_exercise_minutes / total_days
                    result.append(f"Total Exercise Duration: {total_exercise_minutes} minutes, {avg_exercise:.1f} min/day average\\n")
                
                if total_exercise_calories > 0:
                    avg_exercise_cal = total_exercise_calories / total_days
                    result.append(f"Total Exercise Calories: {total_exercise_calories:.0f} kcal, {avg_exercise_cal:.0f} kcal/day average\\n")
                
                # 体重变化
                if first_weight > 0 and last_weight > 0:
                    weight_change = last_weight - first_weight
                    result.append(f"Weight Change: {first_weight:.1f} kg ({first_weight_date}) → {last_weight:.1f} kg ({last_weight_date}) = {weight_change:+.1f} kg\\n")
                
                # 添加BMR估算和能量平衡计算的数据注释
                result.append("\\n=== DATA NOTES FOR ANALYSIS ===\\n")
                result.append("• BMR estimation needed based on user gender and weight data\\n")
                result.append("• Net calorie balance = Daily intake - (BMR + Exercise calories)\\n")
                result.append("• Goal achievement status to be calculated based on target vs completed values\\n")
                if first_weight < 0:
                    result.append("• Weight data not available - BMR estimation will use gender-based averages\\n")
                if total_exercise_calories == 0:
                    result.append("• Exercise calorie data not available - energy balance calculation limited\\n")
            
            return "".join(result)
        
        else:
            logger.warn(f"Failed to get health data or empty response: {data}")
            return f"Comprehensive Health Data Analysis ({final_start_date} to {final_end_date}):\\n\\nNo health data found for the specified period. Please ensure you have recorded health activities during this timeframe.\\n"
    
    except Exception as e:
        logger.error(f"Failed to get comprehensive health data: {e}")
        return f'{{"error": "Failed to get comprehensive health data: {str(e)}"}}'


@tool
def get_detailed_exercise_history_tool(
    start_date: str,
    end_date: str,
    user_id: Optional[str] = None
) -> str:
    """
    MANDATORY: Get detailed exercise history with specific workout data, duration, distance, 
    calories, and statistics. Call for ANY detailed exercise analysis: 'exercise details', 
    'workout performance', 'fitness analysis', 'exercise trends', '运动详情', '健身分析', 
    '运动表现'. Use this when you need specific exercise insights beyond basic overview.
    
    Args:
        start_date: Start date for exercise history analysis in yyyy-MM-dd format
        end_date: End date for exercise history analysis in yyyy-MM-dd format
        user_id: User ID (optional, will be set by scene context)
    
    Returns:
        str: Detailed exercise history and analysis in formatted string
    """
    logger.info(f"GET_DETAILED_EXERCISE_HISTORY_START|START_DATE:{start_date}|END_DATE:{end_date}|USER_ID:{user_id}")
    
    try:
        if not user_id:
            logger.warn("GET_DETAILED_EXERCISE_HISTORY_NO_USER_ID")
            return '{"error": "User ID not available"}'
        
        # 验证和调整日期范围
        validated_dates = _validate_and_adjust_date_range(start_date, end_date)
        final_start_date = validated_dates[0]
        final_end_date = validated_dates[1]
        
        # 获取详细运动数据
        ai_clock_service = get_ai_clock_service()
        data = ai_clock_service.get_exercise_daily(user_id, final_start_date, final_end_date)
        
        logger.info(f"运动数据查询结果: data_keys={list(data.keys()) if data else 'N/A'}")
        
        result = []
        result.append(f"Detailed Exercise History Analysis ({final_start_date} to {final_end_date}):\\n\\n")
        
        if data:
            
            if not data:
                result.append("No exercise data recorded for this period\\n\\n")
                return "".join(result)
            
            # 运动统计变量
            total_workouts = 0
            total_duration = 0
            total_calories = 0.0
            total_distance = 0.0
            exercise_types = {}
            intensity_distribution = {"low": 0, "medium": 0, "high": 0}
            
            # 分析每日运动数据
            for date_str, daily_data in data.items():
                result.append(f"=== {date_str} ===\\n")
                
                if not daily_data:
                    result.append("No exercise activities\\n\\n")
                    continue
                
                daily_workouts = 0
                daily_duration = 0
                daily_calories = 0.0
                
                # 处理每日运动记录
                exercises = getattr(daily_data, 'exercises', []) if hasattr(daily_data, 'exercises') else []
                if isinstance(daily_data, dict):
                    exercises = daily_data.get('exercises', [])
                elif isinstance(daily_data, list):
                    exercises = daily_data
                
                for exercise in exercises:
                    if not exercise:
                        continue
                    
                    total_workouts += 1
                    daily_workouts += 1
                    
                    # 提取运动详情
                    exercise_type = getattr(exercise, 'type', 'Unknown Exercise')
                    duration = getattr(exercise, 'duration', 0)
                    calories = getattr(exercise, 'calories', 0.0)
                    distance = getattr(exercise, 'distance', 0.0)
                    intensity = getattr(exercise, 'intensity', 'medium')
                    
                    result.append(f"  • {exercise_type}:\\n")
                    result.append(f"    Duration: {duration} minutes\\n")
                    if calories > 0:
                        result.append(f"    Calories: {calories} kcal\\n")
                    if distance > 0:
                        result.append(f"    Distance: {distance} km\\n")
                    result.append(f"    Intensity: {intensity}\\n")
                    
                    # 累计统计
                    total_duration += duration
                    daily_duration += duration
                    total_calories += calories
                    daily_calories += calories
                    total_distance += distance
                    
                    # 运动类型统计
                    exercise_types[exercise_type] = exercise_types.get(exercise_type, 0) + 1
                    
                    # 强度分布统计
                    if intensity.lower() in intensity_distribution:
                        intensity_distribution[intensity.lower()] += 1
                    else:
                        intensity_distribution['medium'] += 1
                
                # 日总结
                if daily_workouts > 0:
                    result.append(f"\\n  Daily Summary: {daily_workouts} workout(s), {daily_duration} min total, {daily_calories:.0f} kcal burned\\n")
                
                result.append("\\n")
            
            # 添加运动汇总统计
            if total_workouts > 0:
                result.append("\\n=== EXERCISE SUMMARY ===\\n")
                total_days = len([d for d in data.keys() if data[d]])
                result.append(f"Total Workouts: {total_workouts} sessions over {total_days} active days\\n")
                result.append(f"Total Exercise Duration: {total_duration} minutes ({total_duration//60}h {total_duration%60}m)\\n")
                result.append(f"Total Exercise Calories: {total_calories:.0f} kcal\\n")
                
                if total_distance > 0:
                    result.append(f"Total Distance: {total_distance:.2f} km\\n")
                
                avg_duration = total_duration / total_workouts if total_workouts > 0 else 0
                avg_calories = total_calories / total_workouts if total_workouts > 0 else 0
                result.append(f"Average per workout: {avg_duration:.1f} min, {avg_calories:.0f} kcal\\n")
                
                # 运动强度分布
                result.append("\\n=== EXERCISE INTENSITY DISTRIBUTION ===\\n")
                total_intensity_sessions = sum(intensity_distribution.values())
                if total_intensity_sessions > 0:
                    for intensity, count in intensity_distribution.items():
                        percentage = (count / total_intensity_sessions) * 100
                        result.append(f"{intensity.title()} Intensity: {count} sessions ({percentage:.1f}%)\\n")
                
                # 运动类型分析
                result.append("\\n=== EXERCISE TYPE ANALYSIS ===\\n")
                if exercise_types:
                    most_common_exercise = max(exercise_types, key=exercise_types.get)
                    result.append(f"Most Common Exercise Type: {most_common_exercise} ({exercise_types[most_common_exercise]} sessions)\\n")
                    
                    result.append("Exercise Type Breakdown:\\n")
                    for exercise_type, count in sorted(exercise_types.items(), key=lambda x: x[1], reverse=True):
                        percentage = (count / total_workouts) * 100
                        result.append(f"  • {exercise_type}: {count} sessions ({percentage:.1f}%)\\n")
        
        else:
            result.append("No exercise data found for the specified period\\n")
        
        return "".join(result)
    
    except Exception as e:
        logger.error(f"Failed to get detailed exercise history: {e}")
        return f'{{"error": "Failed to get detailed exercise history: {str(e)}"}}'


@tool
def get_detailed_nutrition_history_tool(
    start_date: str,
    end_date: str,
    user_id: Optional[str] = None
) -> str:
    """
    MANDATORY: Get detailed nutrition history with meal-by-meal breakdown, macronutrient 
    distribution, and dietary patterns. Call for ANY detailed nutrition analysis: 
    'nutrition details', 'diet analysis', 'eating patterns', 'macro breakdown', 
    '营养详情', '饮食分析', '营养摄入'. Use this when you need specific nutrition 
    insights beyond basic overview.
    
    Args:
        start_date: Start date for nutrition history analysis in yyyy-MM-dd format
        end_date: End date for nutrition history analysis in yyyy-MM-dd format
        user_id: User ID (optional, will be set by scene context)
    
    Returns:
        str: Detailed nutrition history and analysis in formatted string
    """
    logger.info(f"GET_DETAILED_NUTRITION_HISTORY_START|START_DATE:{start_date}|END_DATE:{end_date}|USER_ID:{user_id}")
    
    try:
        if not user_id:
            logger.warn("GET_DETAILED_NUTRITION_HISTORY_NO_USER_ID")
            return '{"error": "User ID not available"}'
        
        # 验证和调整日期范围
        validated_dates = _validate_and_adjust_date_range(start_date, end_date)
        final_start_date = validated_dates[0]
        final_end_date = validated_dates[1]
        
        # 获取详细营养数据
        ai_clock_service = get_ai_clock_service()
        data = ai_clock_service.get_nutrition_daily(user_id, final_start_date, final_end_date)
        
        logger.info(f"营养数据查询结果: data_keys={list(data.keys()) if data else 'N/A'}")
        
        result = []
        result.append(f"Detailed Nutrition History Analysis ({final_start_date} to {final_end_date}):\\n\\n")
        
        if data:
            
            if not data:
                result.append("No nutrition data recorded for this period\\n\\n")
                return "".join(result)
            
            # 营养统计变量
            total_calories = 0.0
            total_protein = 0.0
            total_carbs = 0.0
            total_fat = 0.0
            meal_timing_stats = {"breakfast": 0, "lunch": 0, "dinner": 0, "snack": 0}
            
            # 分析每日营养数据
            for date_str, daily_data in data.items():
                result.append(f"=== {date_str} ===\\n")
                
                if not daily_data:
                    result.append("No nutrition data\\n\\n")
                    continue
                
                daily_calories = 0.0
                daily_protein = 0.0
                daily_carbs = 0.0
                daily_fat = 0.0
                
                # 处理每日营养记录
                meals = getattr(daily_data, 'meals', []) if hasattr(daily_data, 'meals') else []
                if isinstance(daily_data, dict):
                    meals = daily_data.get('meals', [])
                elif isinstance(daily_data, list):
                    meals = daily_data
                
                # 按餐次分组
                meals_by_timing = {"breakfast": [], "lunch": [], "dinner": [], "snack": []}
                
                for meal in meals:
                    if not meal:
                        continue
                    
                    timing = getattr(meal, 'timing', 1)  # 1: breakfast, 2: lunch, 3: dinner, 4: snack
                    timing_name = _get_meal_timing_name(timing)
                    
                    meals_by_timing[timing_name].append(meal)
                    meal_timing_stats[timing_name] += 1
                
                # 显示各餐次详情
                for timing_name, timing_meals in meals_by_timing.items():
                    if not timing_meals:
                        continue
                    
                    result.append(f"\\n{timing_name.title()}:\\n")
                    timing_calories = 0.0
                    timing_protein = 0.0
                    timing_carbs = 0.0
                    timing_fat = 0.0
                    
                    for meal in timing_meals:
                        food_name = getattr(meal, 'food_name', 'Unknown Food')
                        calories = getattr(meal, 'calories', 0.0)
                        protein = getattr(meal, 'protein', 0.0)
                        carbs = getattr(meal, 'carbohydrates', 0.0)
                        fat = getattr(meal, 'fat', 0.0)
                        quantity = getattr(meal, 'quantity', 1)
                        unit = getattr(meal, 'unit', 'serving')
                        
                        result.append(f"  • {food_name} ({quantity} {unit}):\\n")
                        result.append(f"    Calories: {calories} kcal\\n")
                        if protein > 0:
                            result.append(f"    Protein: {protein}g\\n")
                        if carbs > 0:
                            result.append(f"    Carbs: {carbs}g\\n")
                        if fat > 0:
                            result.append(f"    Fat: {fat}g\\n")
                        
                        # 累计各餐次营养
                        timing_calories += calories
                        timing_protein += protein
                        timing_carbs += carbs
                        timing_fat += fat
                    
                    # 餐次小计
                    result.append(f"  {timing_name.title()} Total: {timing_calories:.0f} kcal, {timing_protein:.1f}g protein, {timing_carbs:.1f}g carbs, {timing_fat:.1f}g fat\\n")
                    
                    # 累计每日营养
                    daily_calories += timing_calories
                    daily_protein += timing_protein
                    daily_carbs += timing_carbs
                    daily_fat += timing_fat
                
                # 日总结
                if daily_calories > 0:
                    result.append(f"\\n  Daily Total: {daily_calories:.0f} kcal, {daily_protein:.1f}g protein, {daily_carbs:.1f}g carbs, {daily_fat:.1f}g fat\\n")
                    
                    # 宏量营养素百分比
                    if daily_calories > 0:
                        protein_pct = (daily_protein * 4 / daily_calories) * 100
                        carbs_pct = (daily_carbs * 4 / daily_calories) * 100
                        fat_pct = (daily_fat * 9 / daily_calories) * 100
                        result.append(f"  Macros: {protein_pct:.1f}% protein, {carbs_pct:.1f}% carbs, {fat_pct:.1f}% fat\\n")
                
                # 累计总营养
                total_calories += daily_calories
                total_protein += daily_protein
                total_carbs += daily_carbs
                total_fat += daily_fat
                
                result.append("\\n")
            
            # 添加营养汇总统计
            if total_calories > 0:
                result.append("\\n=== NUTRITION SUMMARY ===\\n")
                total_days = len([d for d in data.keys() if data[d]])
                result.append(f"Total Calories: {total_calories:.0f} kcal over {total_days} days\\n")
                result.append(f"Daily Average: {total_calories/total_days:.0f} kcal/day\\n")
                result.append(f"Total Macronutrients: {total_protein:.1f}g protein, {total_carbs:.1f}g carbs, {total_fat:.1f}g fat\\n")
                
                # 平均宏量营养素分布
                if total_calories > 0:
                    avg_protein_pct = (total_protein * 4 / total_calories) * 100
                    avg_carbs_pct = (total_carbs * 4 / total_calories) * 100
                    avg_fat_pct = (total_fat * 9 / total_calories) * 100
                    result.append(f"Average Macronutrient Distribution: {avg_protein_pct:.1f}% protein, {avg_carbs_pct:.1f}% carbs, {avg_fat_pct:.1f}% fat\\n")
                
                # 餐次分布统计
                result.append("\\n=== MEAL TIMING DISTRIBUTION ===\\n")
                total_meals = sum(meal_timing_stats.values())
                if total_meals > 0:
                    for timing, count in meal_timing_stats.items():
                        if count > 0:
                            percentage = (count / total_meals) * 100
                            result.append(f"{timing.title()}: {count} meals ({percentage:.1f}%)\\n")
        
        else:
            result.append("No nutrition data found for the specified period\\n")
        
        return "".join(result)
    
    except Exception as e:
        logger.error(f"Failed to get detailed nutrition history: {e}")
        return f'{{"error": "Failed to get detailed nutrition history: {str(e)}"}}'


def _validate_and_adjust_date_range(start_date: str, end_date: str) -> List[str]:
    """验证和调整日期范围（基于Java版本逻辑）"""
    try:
        # 解析日期
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        
        # 确保开始日期不晚于结束日期
        if start_dt > end_dt:
            start_dt, end_dt = end_dt, start_dt
        
        # 限制查询范围不超过90天
        if (end_dt - start_dt).days > 90:
            start_dt = end_dt - timedelta(days=90)
        
        # 确保不超过当前日期
        today = datetime.now().date()
        if end_dt.date() > today:
            end_dt = datetime.combine(today, datetime.min.time())
        
        return [start_dt.strftime("%Y-%m-%d"), end_dt.strftime("%Y-%m-%d")]
    
    except Exception as e:
        logger.error(f"Date validation failed: {e}")
        # 返回默认日期范围（最近7天）
        today = datetime.now()
        end_date = today.strftime("%Y-%m-%d")
        start_date = (today - timedelta(days=6)).strftime("%Y-%m-%d")
        return [start_date, end_date]


def _format_category_name(category_type: str) -> str:
    """格式化分类名称"""
    if not category_type:
        return "Unknown Category"
    
    category_map = {
        "nutrition": "🍎 Nutrition",
        "hydration": "💧 Hydration", 
        "sleep": "😴 Sleep",
        "exercise": "🏃 Exercise",
        "weight": "⚖️ Weight Tracking",
        "mood": "😊 Mood & Wellness"
    }
    
    return category_map.get(category_type.lower(), f"📊 {category_type.title()}")


def _get_meal_timing_name(timing: int) -> str:
    """获取餐次名称"""
    timing_map = {
        1: "breakfast",
        2: "lunch", 
        3: "dinner",
        4: "snack"
    }
    return timing_map.get(timing, "snack")
