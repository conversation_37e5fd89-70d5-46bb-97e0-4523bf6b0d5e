"""
Health advisor tools implementation

Based on Java version HealthConsultationFunctionTools.java:
- Single tool: getDailyDetail for comprehensive health consultation
- Uses AiClockService (not HealthAgentService) consistent with other health scenes
- English prompts and descriptions following Java JsonPropertyDescription format
- Returns JSON string format consistent with Java implementation
- 14-day forced data range for comprehensive health history
- NEW: Patient data query tools with embedded SQL generation agent

@author: shaohua.sun
@date: 2025/7/2
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from langchain_core.tools import tool
import json

from integration.services.ai_clock.service import AiClockService
from integration.services.chatbot.service import ChatbotService
from utils.biz_logger import get_logger


logger = get_logger(__name__)

# Global service instances
_ai_clock_service = None
_chatbot_service = None

def get_ai_clock_service() -> AiClockService:
    """Get AI Clock service instance (singleton pattern)"""
    global _ai_clock_service
    if _ai_clock_service is None:
        _ai_clock_service = AiClockService()
    return _ai_clock_service

def get_chatbot_service() -> ChatbotService:
    """Get Chatbot service instance (singleton pattern)"""
    global _chatbot_service
    if _chatbot_service is None:
        _chatbot_service = ChatbotService()
    return _chatbot_service


@tool
def get_daily_detail_tool(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    user_id: Optional[str] = None
) -> str:
    """
    Get user's comprehensive health data for evidence-based health consultation
    
    Retrieves comprehensive health records including nutrition, hydration, sleep, exercise data.
    MANDATORY tool for all health consultation requests to provide data-driven advice.
    Follows Java version logic with forced 14-day data range for comprehensive health history.
    
    Args:
        start_date: Start date for querying daily details, format: yyyy-MM-dd, e.g.: 2024-01-01 (optional, defaults to today)
        end_date: End date for querying daily details, format: yyyy-MM-dd, e.g.: 2024-01-01 (optional, defaults to startDate)
        user_id: User ID (optional, will be set by scene context)
    
    Returns:
        str: Comprehensive health data information in JSON string format
    """
    logger.info(f"Get comprehensive health data for consultation: start_date={start_date}, end_date={end_date}, user_id={user_id}")
    
    try:
        if not user_id:
            logger.warn("GET_DAILY_DETAIL_NO_USER_ID")
            return '"Tool call failed: User ID is not available. Please ensure user is properly authenticated and try again."'
        # Java version logic: Force 14-day historical data range regardless of AI parameters
        # This ensures comprehensive health context for consultation
        current_date = datetime.now()
        current_date_str = current_date.strftime('%Y-%m-%d')
        
        # Apply FORCED 14-day range (ignoring AI parameters) - Java version pattern
        start_date_obj = current_date - timedelta(days=13)  # 14 days total including today
        end_date_obj = current_date
        
        final_start_date = start_date_obj.strftime('%Y-%m-%d')
        final_end_date = end_date_obj.strftime('%Y-%m-%d')
        
        logger.info(f"Applied FORCED 14-day range (ignoring AI parameters) - using range: {final_start_date} to {final_end_date}")
        
        # Call AI Clock service to get comprehensive health data
        ai_clock_service = get_ai_clock_service()
        response = ai_clock_service.get_daily_detail(user_id, final_start_date, final_end_date)
        
        # Process response data following Java version format
        if response:
            data = response
            
            # Build comprehensive health summary following Java version format
            result = []
            result.append(f"User's comprehensive health data ({final_start_date} to {final_end_date}):\\n\\n")
            
            if data:
                for date_str, day_data in data.items():
                    safe_date = date_str if date_str else "Unknown Date"
                    result.append(f"📅 {safe_date}:\\n")
                    
                    categories = day_data if isinstance(day_data, list) else []
                    if not categories:
                        result.append("  No health data available\\n\\n")
                        continue
                    
                    for category in categories:
                        if not category:
                            continue
                        
                        # Handle both Pydantic models and dict objects
                        category_type = getattr(category, 'type', '') if hasattr(category, 'type') else category.get('type', '') if isinstance(category, dict) else ''
                        category_icon = _get_category_icon(category_type)
                        category_title = getattr(category, 'title', 'Unknown Category') if hasattr(category, 'title') else category.get('title', 'Unknown Category') if isinstance(category, dict) else 'Unknown Category'
                        
                        result.append(f"  {category_icon} {category_title}:\\n")
                        
                        # Handle goals list
                        goals = getattr(category, 'goals', []) if hasattr(category, 'goals') else category.get('goals', []) if isinstance(category, dict) else []
                        if goals:
                            for goal in goals:
                                if not goal:
                                    continue
                                
                                goal_title = getattr(goal, 'title', 'Unknown Goal') if hasattr(goal, 'title') else goal.get('title', 'Unknown Goal') if isinstance(goal, dict) else 'Unknown Goal'
                                result.append(f"    🎯 {goal_title}:\\n")
                                
                                # Safe handling of completed value
                                completed_value = getattr(goal, 'completedValue', None) if hasattr(goal, 'completedValue') else goal.get('completedValue') if isinstance(goal, dict) else None
                                if completed_value is not None:
                                    goal_obj = getattr(goal, 'goal', None) if hasattr(goal, 'goal') else goal.get('goal', {}) if isinstance(goal, dict) else {}
                                    goal_unit = getattr(goal_obj, 'goalUnit', '') if hasattr(goal_obj, 'goalUnit') else goal_obj.get('goalUnit', '') if isinstance(goal_obj, dict) else ''
                                    result.append(f"      Completed: {completed_value} {goal_unit}\\n")
                                else:
                                    result.append("      Completed: No data\\n")
                                
                                # Safe handling of progress percentage
                                completed_pct = getattr(goal, 'completedPct', None) if hasattr(goal, 'completedPct') else goal.get('completedPct') if isinstance(goal, dict) else None
                                if completed_pct is not None:
                                    result.append(f"      Progress: {completed_pct:.1f}%\\n")
                                else:
                                    result.append("      Progress: Not tracked\\n")
                                
                                # Safe handling of goal targets
                                goal_obj = getattr(goal, 'goal', None) if hasattr(goal, 'goal') else goal.get('goal', {}) if isinstance(goal, dict) else {}
                                if goal_obj:
                                    goal_min = getattr(goal_obj, 'goalMinValue', None) if hasattr(goal_obj, 'goalMinValue') else goal_obj.get('goalMinValue') if isinstance(goal_obj, dict) else None
                                    goal_max = getattr(goal_obj, 'goalMaxValue', None) if hasattr(goal_obj, 'goalMaxValue') else goal_obj.get('goalMaxValue') if isinstance(goal_obj, dict) else None
                                    goal_value = getattr(goal_obj, 'goalValue', None) if hasattr(goal_obj, 'goalValue') else goal_obj.get('goalValue') if isinstance(goal_obj, dict) else None
                                    goal_unit = getattr(goal_obj, 'goalUnit', '') if hasattr(goal_obj, 'goalUnit') else goal_obj.get('goalUnit', '') if isinstance(goal_obj, dict) else ''
                                    
                                    if goal_min is not None and goal_max is not None:
                                        result.append(f"      Target: {goal_min}-{goal_max} {goal_unit}\\n")
                                    elif goal_value is not None:
                                        result.append(f"      Target: {goal_value} {goal_unit}\\n")
                                
                                # Progress status assessment following Java version
                                status_emoji = _get_progress_status_emoji(completed_pct)
                                progress_desc = _get_progress_description(completed_pct)
                                result.append(f"      Status: {status_emoji} {progress_desc}\\n")
                        
                        result.append("\\n")
            
            logger.info("Successfully retrieved comprehensive health data for consultation")
            # Return JSON string format following Java version
            result_text = "".join(result)
            return f'"{result_text.replace("\"", "\\\"")}"'
        
        else:
            logger.warn("Failed to retrieve health data or no data available")
            return '"Tool call failed: Unable to retrieve user\'s comprehensive health data. Please inform the user that their health history is currently unavailable and continue with general health consultation."'
    
    except Exception as e:
        logger.error(f"Health consultation data retrieval error: {e}")
        return '"Tool call failed: Health data service is currently unavailable. Please inform the user that their health history cannot be accessed at the moment and continue with general health consultation."'


def _get_category_icon(category_type: str) -> str:
    """Get category icon based on type (following Java version)"""
    if not category_type:
        return "📊"
    
    try:
        category_lower = category_type.lower()
        if category_lower in ['physical_activity', 'exercise']:
            return "🏃"
        elif category_lower in ['nutrition', 'diet']:
            return "🍽️"
        elif category_lower == 'sleep':
            return "😴"
        elif category_lower in ['hydration', 'water']:
            return "💧"
        elif category_lower == 'weight':
            return "⚖️"
        elif category_lower == 'heart_rate':
            return "❤️"
        elif category_lower == 'blood_pressure':
            return "🩺"
        else:
            return "📊"
    except Exception as e:
        logger.warn(f"Error getting category icon for type: {category_type}, {e}")
        return "📊"


def _get_progress_status_emoji(completed_pct: Optional[float]) -> str:
    """Get progress status emoji based on completion percentage (following Java version)"""
    if completed_pct is None:
        return "❓"
    
    if completed_pct >= 100:
        return "✅"
    elif completed_pct >= 80:
        return "🟢"
    elif completed_pct >= 60:
        return "🟡"
    elif completed_pct >= 40:
        return "🟠"
    else:
        return "🔴"


def _get_progress_description(completed_pct: Optional[float]) -> str:
    """Get progress description based on completion percentage (following Java version)"""
    if completed_pct is None:
        return "Unknown"
    
    if completed_pct >= 100:
        return "Goal achieved"
    elif completed_pct >= 80:
        return "Great progress"
    elif completed_pct >= 60:
        return "Good progress"
    elif completed_pct >= 40:
        return "Moderate progress"
    elif completed_pct > 0:
        return "Getting started"
    else:
        return "No progress yet"


# ==================== 患者数据查询工具 ====================

@tool
def query_patient_health_indicators_tool(
    query_description: str,
    user_id: Optional[str] = None
) -> str:
    """
    Query patient's latest health indicators data for medical consultation

    Retrieves the latest health indicator data (blood pressure, blood sugar, weight, etc.)
    for the current patient. Use this tool when users ask about their latest health metrics,
    medical indicators, or current health status measurements.

    Args:
        query_description: Description of what health indicators the user is asking about
                          (e.g., "blood pressure and blood sugar", "latest vital signs", "weight trends")
        user_id: User ID (optional, will be set by scene context)

    Returns:
        str: Patient's latest health indicator data in structured format
    """
    logger.info(f"查询患者健康指标: query_description={query_description}, user_id={user_id}")

    try:
        # 获取用户会员ID
        if not user_id:
            logger.warning("User ID is missing or empty")
            return '"Tool call failed: User ID is not available. Please ensure user is properly authenticated and try again."'

        # 使用固定的健康指标列表
        indicator_keys = ["weight"]

        logger.info(f"使用指标类型: {indicator_keys}")

        # 调用chatbot服务查询指标数据
        chatbot_service = get_chatbot_service()
        indicators = chatbot_service.get_patient_health_indicators(user_id, indicator_keys)

        if not indicators:
            return '"No health indicator data found for the current patient. The patient may need to record their health measurements first."'

        # 格式化返回结果
        result = []
        result.append(f"Patient's Latest Health Indicators ({len(indicators)} indicators found):\\n\\n")

        for indicator_type, data in indicators.items():
            indicator_name = indicator_type
            value = data.get('value', 'N/A')
            unit = _extract_unit_from_ext_data(data.get('extData', '{}'))
            record_time = data.get('recordDateTime', 'Unknown time')

            result.append(f"📊 {indicator_name}:\\n")
            result.append(f"   Value: {value} {unit}\\n")
            result.append(f"   Recorded: {record_time}\\n")
            result.append(f"   Type: {data.get('valueType', 'unknown')}\\n\\n")

        # 添加数据解释说明
        result.append("💡 Data Notes:\\n")
        result.append("- These are the patient's most recent recorded health measurements\\n")
        result.append("- Use this data to provide personalized health advice and recommendations\\n")
        result.append("- Consider trends and normal ranges when making assessments\\n")

        logger.info("成功获取患者健康指标数据")
        result_text = "".join(result)
        return f'"{result_text.replace("\"", "\\\"")}"'

    except Exception as e:
        logger.error(f"查询患者健康指标失败: {e}")
        return f'"Tool call failed: Unable to retrieve patient health indicators. Error: {str(e)}"'


@tool
def query_patient_medical_records_tool(
    query_description: str,
    user_id: Optional[str] = None
) -> str:
    """
    Query patient's medical records using intelligent SQL generation

    Retrieves patient's medical records (diagnosis, medication, treatment history)
    using an embedded AI agent that generates appropriate SQL queries based on
    the user's request. Use this for complex medical history inquiries.

    Args:
        query_description: Description of what medical records the user wants
                          (e.g., "recent diagnoses", "current medications", "diabetes treatment history")
        user_id: User ID (optional, will be set by scene context)

    Returns:
        str: Patient's medical records data based on the generated SQL query
    """
    logger.info(f"查询患者医疗记录: query_description={query_description}, user_id={user_id}")

    try:
        # 导入SQL代理
        from service.scenes.embedded_agents.sql_agent import SQLGenerationAgent

        # 创建SQL生成代理
        sql_agent = SQLGenerationAgent()

        # 构建完整的查询描述
        full_query = query_description

        # 使用AI代理生成SQL
        logger.info(f"使用SQL代理生成查询语句: {full_query}")
        sql_result = sql_agent.generate_sql(full_query)

        if not sql_result or not sql_result.get('sql_list'):
            return '"Unable to generate appropriate SQL queries for the requested medical records. Please try rephrasing your question."'

        # 获取chatbot服务
        chatbot_service = get_chatbot_service()

        # 执行每个SQL查询并收集结果
        all_records = []
        related_analysis = sql_result.get('related_list', [])

        for i, sql in enumerate(sql_result.get('sql_list', [])):
            try:
                logger.info(f"执行SQL查询 {i+1}: {sql}")
                records = chatbot_service.get_patient_records_by_sql(sql)
                if records:
                    all_records.extend(records)
            except Exception as e:
                logger.error(f"执行SQL查询失败: {e}")
                continue

        # 格式化返回结果
        result = []
        result.append(f"Patient's Medical Records Query Results:\\n\\n")

        # 添加AI分析的相关性说明
        if related_analysis:
            result.append("🧠 AI Analysis of Query Relevance:\\n")
            for analysis in related_analysis:
                result.append(f"• {analysis}\\n")
            result.append("\\n")

        # 添加查询结果
        if all_records:
            result.append(f"📋 Medical Records Found ({len(all_records)} records):\\n\\n")

            for i, record in enumerate(all_records[:20], 1):  # 限制显示前20条记录
                result.append(f"Record #{i}:\\n")

                # 格式化记录数据
                for key, value in record.items():
                    if value is not None and str(value).strip():
                        display_key = _format_field_name(key)
                        result.append(f"  {display_key}: {value}\\n")

                result.append("\\n")

            if len(all_records) > 20:
                result.append(f"... and {len(all_records) - 20} more records (showing first 20)\\n\\n")
        else:
            result.append("📋 No medical records found matching the query criteria.\\n\\n")

        # 添加数据使用说明
        result.append("💡 Usage Notes:\\n")
        result.append("- This data is retrieved from the patient's medical history database\\n")
        result.append("- Use this information to provide context-aware medical consultation\\n")
        result.append("- Consider the patient's medical history when making recommendations\\n")

        logger.info(f"成功获取患者医疗记录，共 {len(all_records)} 条记录")
        result_text = "".join(result)
        return f'"{result_text.replace("\"", "\\\"")}"'

    except Exception as e:
        logger.error(f"查询患者医疗记录失败: {e}")
        return f'"Tool call failed: Unable to retrieve patient medical records. Error: {str(e)}"'


# ==================== 辅助函数 ====================

def _extract_unit_from_ext_data(ext_data: str) -> str:
    """从扩展数据中提取单位"""
    try:
        import json
        data = json.loads(ext_data)
        return data.get('unit', '')
    except:
        return ''

def _format_field_name(field_name: str) -> str:
    """格式化字段名为显示名称"""
    # 使用通用的格式化方法，将下划线分隔的字段名转换为可读格式
    # 如果后续需要特殊映射，可以通过配置文件管理
    return field_name.replace('_', ' ').title()
