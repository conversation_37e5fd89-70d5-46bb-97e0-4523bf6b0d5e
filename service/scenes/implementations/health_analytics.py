"""
Health analytics scene implementation

基于Java版本HealthAnalyticsFunctionTools和HealthAnalyticsServiceImpl的Python实现：
- 双重能力模式：历史数据分析 + 未来规划建议
- 三个核心工具：综合健康数据概览、详细运动历史、详细营养历史
- 智能意图识别：自动区分历史分析请求和未来规划请求
- 能量平衡计算：BMR + 运动消耗 = 总消耗，摄入 - 总消耗 = 净平衡
- 只读分析场景：专注于数据分析和洞察，不生成新的健康数据记录

技术架构：
- 遵循LangGraph框架设计模式
- 使用ReactAgent进行工具调用和对话管理
- 通过HealthAnalyticsTemplate管理提示词模板
- 支持流式响应和卡片数据处理

@author: shaohua.sun
@date: 2025/7/2
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
from langchain_core.tools import BaseTool

from service.intention.base import IntentionSceneEnum
from service.scenes.base.base_scene import BaseScene
from service.scenes.base.scene_context import SceneContext
from service.scenes.base.scene_registry import scene_registry
from service.templates.scene_templates import HealthAnalyticsTemplate
from service.scenes.tools.health_analytics_tools import (
    get_comprehensive_health_data_tool,
    get_detailed_exercise_history_tool,
    get_detailed_nutrition_history_tool
)
from utils.biz_logger import get_logger

logger = get_logger(__name__)


@scene_registry.register_scene_decorator(IntentionSceneEnum.HEALTH_ANALYTICS)
class HealthAnalyticsScene(BaseScene):
    """
    健康分析场景实现
    
    业务能力：
    1. **历史数据分析**：分析用户历史健康打卡数据，提供综合健康洞察、趋势分析和表现评估
    2. **未来规划建议**：基于用户目标、偏好和历史模式生成个性化、可执行的健康计划和建议
    
    核心工具：
    - get_comprehensive_health_data_tool: 获取综合健康数据概览（包括运动、睡眠、饮水等各维度）
    - get_detailed_exercise_history_tool: 获取详细运动历史数据（运动类型、时长、距离、卡路里等）
    - get_detailed_nutrition_history_tool: 获取详细营养历史数据（每餐详细记录、宏量营养素等）
    
    智能意图识别：
    - 自动识别用户查询是历史分析请求还是未来规划请求
    - 基于关键词触发不同的处理模式（分析 vs 规划）
    - 支持模式切换和上下文整合
    
    输出要求：
    - 必须包含能量平衡计算（摄入-消耗=净平衡）
    - 必须显示BMR、总能量消耗、净卡路里平衡等具体数值
    - 提供具体的、可量化的建议而非泛泛而谈
    """
    def get_tools(self) -> List[BaseTool]:
        """
        获取健康分析场景工具（基于Java版本重构为3个核心工具）
        
        基于Java版本HealthAnalyticsFunctionTools的工具配置：
        1. get_comprehensive_health_data_tool - 获取综合健康数据概览（MANDATORY首选工具）
           - 包括运动、睡眠、饮水等各维度的目标完成情况
           - 提供完整的健康数据汇总和统计分析
           - 支持能量平衡计算和BMR估算
        
        2. get_detailed_exercise_history_tool - 获取详细运动历史数据
           - 运动类型、时长、距离、卡路里消耗等详细信息
           - 运动强度分布和频率分析
           - 支持运动维度的深度分析
        
        3. get_detailed_nutrition_history_tool - 获取详细营养历史数据
           - 每餐详细记录和宏量营养素分析
           - 膳食时间分布和营养质量评估
           - 支持营养维度的专业分析
        
        Returns:
            List[BaseTool]: 健康分析相关工具列表
        """
        return [
            get_comprehensive_health_data_tool,     # 综合健康数据概览（主要工具）
            get_detailed_exercise_history_tool,     # 详细运动历史分析
            get_detailed_nutrition_history_tool     # 详细营养历史分析
        ]
    
    def build_system_prompt(self, context: SceneContext) -> str:
        """
        构建健康分析场景的系统提示词
        
        使用HealthAnalyticsTemplate管理场景专用的提示词模板，
        包含双重能力模式、智能意图识别、工具调用协议等完整指导。
        
        Args:
            context: 场景上下文信息
            
        Returns:
            str: 健康分析场景专用的系统提示词（包含动态系统信息和用户上下文）
        """
        # 获取基础系统提示词，传递self实例以获取正确的场景名称
        base_prompt = HealthAnalyticsTemplate.build_complete_system_prompt(self)
        
        # 使用BaseScene的方法添加用户信息、时间信息和其他增强内容
        return self.build_enhanced_system_prompt(base_prompt, context)

