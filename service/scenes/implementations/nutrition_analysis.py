"""
营养分析场景实现

基于文档中的Java实现逻辑，提供完整的营养分析功能：
- 食物营养分析和计算
- 营养记录生成和追踪
- 个性化营养建议
- 营养历史查询和分析
- 营养卡片数据生成

@author: shaohua.sun
@date: 2025/7/2
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
from langchain_core.tools import BaseTool

from service.intention.base import IntentionSceneEnum
from ..base import BaseScene, SceneContext, scene_registry
from service.templates.scene_templates import NutritionAnalysisTemplate
from ..tools.nutrition_tools import (
    generate_nutrition_record_tool,
    get_user_nutrition_daily_tool,
    get_meal_timing_info_tool
)
from utils.biz_logger import get_logger

logger = get_logger(__name__)

# 注意：营养分析场景现在使用模板管理器统一管理提示词
# 原有的常量提示词已迁移到 service/templates/scene_templates.py 中的 NutritionAnalysisTemplate
# 这样可以确保公共部分提示词（如系统时间、用户信息等）的统一管理


@scene_registry.register_scene_decorator(IntentionSceneEnum.NUTRITION_ANALYSIS)
class NutritionAnalysisScene(BaseScene):
    """
    营养分析场景处理器

    基于ReactAgent架构的营养分析场景实现，
    提供完整的营养分析、记录和建议功能
    """

    def build_system_prompt(self, context: SceneContext) -> str:
        """
        构建营养分析场景的系统提示词，使用场景专用模板管理器

        Args:
            context: 场景上下文

        Returns:
            str: 营养分析场景专用的系统提示词（包含动态系统信息和用户信息）
        """
        # 获取基础系统提示词，传递self实例以获取正确的场景名称
        base_prompt = NutritionAnalysisTemplate.build_complete_system_prompt(self)

        # 使用BaseScene的方法添加用户信息和其他增强内容
        return self.build_enhanced_system_prompt(base_prompt, context)

    def get_tools(self) -> List[BaseTool]:
        """
        获取营养分析场景的专用工具

        按重要性和调用频率排序：
        1. 营养数据查询工具（最常用，MANDATORY）
        2. 营养记录生成工具（记录场景使用）
        3. 用餐时间工具（辅助工具，仅在需要时调用）

        Returns:
            List[BaseTool]: 营养分析相关的工具列表
        """
        return [
            get_user_nutrition_daily_tool,  # 最重要，MANDATORY工具放第一位
            generate_nutrition_record_tool,  # 记录功能，第二位
            get_meal_timing_info_tool  # 辅助工具，最后
        ]

    async def generate_card_data(self, context: SceneContext, agent_response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        生成营养记录卡片数据

        使用新的工具结果存储机制，从线程本地存储中获取工具调用结果

        Args:
            context: 场景上下文
            agent_response: Agent响应结果

        Returns:
            List[Dict[str, Any]]: 营养记录卡片数据数组
        """
        try:
            logger.info("开始生成营养卡片数据")

            # 使用统一的工具结果获取机制
            nutrition_record_results = self.get_tool_results('generate_nutrition_record_tool')

            if nutrition_record_results:
                logger.info("从工具结果存储中获取到营养记录数据")

                # 营养场景原有逻辑：处理单个工具结果（取第一个）
                try:
                    import json
                    nutrition_record_result = nutrition_record_results[0]  # 取第一个结果
                    if isinstance(nutrition_record_result, str):
                        complete_card = json.loads(nutrition_record_result)
                        logger.info("成功解析营养卡片数据")
                        return [complete_card]  # 返回单张卡片数组
                    else:
                        # 如果已经是字典格式，直接返回
                        logger.info("营养卡片数据已为字典格式")
                        return [nutrition_record_result]  # 返回单张卡片数组
                except (json.JSONDecodeError, IndexError):
                    logger.warning(f"工具返回数据解析失败: {nutrition_record_results}")
                    return []
            else:
                logger.info("未检测到营养记录工具调用结果")
                return []

        except Exception as e:
            logger.error(f"生成营养卡片数据失败: {e}")
            return []

