"""
Default chat scene implementation

Handles general conversations that don't belong to specific health scenes.
Uses streamChat without Function Calling as per document requirements.
Provides friendly, helpful responses while maintaining conversation flow.

@author: shaohua.sun
@date: 2025/7/2
"""

from typing import List, Dict, Any
from langchain_core.tools import BaseTool

from service.intention.base import IntentionSceneEnum
from service.scenes.base.base_scene import BaseScene
from service.scenes.base.scene_context import SceneContext
from service.scenes.base.scene_registry import scene_registry
from service.templates.base_templates import TemplateManager
from utils.biz_logger import get_logger

logger = get_logger(__name__)


@scene_registry.register_scene_decorator(IntentionSceneEnum.OTHER_SCENE)
class DefaultChatScene(BaseScene):
    """
    Default chat scene handler
    
    Handles user requests that don't belong to specific health scenes
    Uses streamChat without Function Calling as per document requirements
    """
    
    def build_system_prompt(self, context: SceneContext) -> str:
        """
        构建默认聊天场景的系统提示词，使用场景专用模板管理器

        Args:
            context: Scene context

        Returns:
            str: 默认聊天场景专用的系统提示词（包含动态系统信息和用户信息）
        """
        # 获取基础系统提示词，传递self实例以获取正确的场景名称
        from service.templates.scene_templates import DefaultChatTemplate
        base_prompt = DefaultChatTemplate.build_complete_system_prompt(self)

        # 使用BaseScene的方法添加用户信息和其他增强内容
        return self.build_enhanced_system_prompt(base_prompt, context)
    
    def get_tools(self) -> List[BaseTool]:
        """
        Get default chat scene tools
        
        Returns:
            List[BaseTool]: Default chat scene tools list (empty as per document - no Function Calling)
        """
        # Default chat scene uses streamChat without Function Calling
        return []
    