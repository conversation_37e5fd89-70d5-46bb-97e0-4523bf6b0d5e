"""
Sleep tracking scene implementation

Based on document requirements and following nutrition scene success pattern:
- Complete sleep tracking functionality
- English prompts and constants following project standards
- Dynamic system information integration
- Proper tool guidance and card data extraction

@author: shaohua.sun
@date: 2025/7/2
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
import json
from langchain_core.tools import BaseTool

from service.intention.base import IntentionSceneEnum
from service.scenes.base.base_scene import BaseScene
from service.scenes.base.scene_context import SceneContext
from service.scenes.base.scene_registry import scene_registry
from service.templates.scene_templates import SleepTrackingTemplate
from service.scenes.tools.sleep_tools import (
    get_daily_detail_tool,
    generate_sleep_record_tool
)
from utils.biz_logger import get_logger

logger = get_logger(__name__)



@scene_registry.register_scene_decorator(IntentionSceneEnum.SLEEP_TRACKING)
class SleepTrackingScene(BaseScene):
    """Sleep tracking scene implementation"""
    def get_tools(self) -> List[BaseTool]:
        """
        Get sleep tracking scene tools
        
        Ordered by importance and call frequency:
        1. Sleep data query tool (most common, MANDATORY)
        2. Sleep record generation tool (logging functionality)
        
        Returns:
            List[BaseTool]: Sleep tracking related tools list
        """
        return [
            get_daily_detail_tool,  # Most important, MANDATORY tool first
            generate_sleep_record_tool   # Recording functionality, second
        ]
    
    def build_system_prompt(self, context: SceneContext) -> str:
        """
        构建睡眠追踪场景的系统提示词，使用场景专用模板管理器

        Args:
            context: Scene context

        Returns:
            str: 睡眠追踪场景专用的系统提示词（包含动态系统信息和用户信息）
        """
        # 获取基础系统提示词，传递self实例以获取正确的场景名称
        base_prompt = SleepTrackingTemplate.build_complete_system_prompt(self)

        # 使用BaseScene的方法添加用户信息和其他增强内容
        return self.build_enhanced_system_prompt(base_prompt, context)
    
    async def generate_card_data(self, context: SceneContext, agent_response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generate sleep card data from stored tool results

        Args:
            context: Scene context
            agent_response: Agent response result

        Returns:
            List[Dict[str, Any]]: Generated card data array
        """
        logger.info("开始生成睡眠卡片数据")

        try:
            # Get sleep record tool result from storage
            sleep_record_results = self.get_tool_results('generate_sleep_record_tool')

            if sleep_record_results:
                logger.info("从工具结果存储中获取到睡眠记录数据")

                # 睡眠场景原有逻辑：处理单个工具结果（取第一个）
                try:
                    import json
                    sleep_record_result = sleep_record_results[0]  # 取第一个结果
                    if isinstance(sleep_record_result, str):
                        complete_card = json.loads(sleep_record_result)
                        logger.info("成功解析睡眠卡片数据")
                        return [complete_card]  # 返回单张卡片数组
                    else:
                        # 如果已经是字典格式，直接返回
                        logger.info("睡眠卡片数据已为字典格式")
                        return [sleep_record_result]  # 返回单张卡片数组
                except (json.JSONDecodeError, IndexError):
                    logger.warning(f"工具返回数据解析失败: {sleep_record_results}")
                    return []
            else:
                logger.info("未检测到睡眠记录工具调用结果")
                return []

        except Exception as e:
            logger.error(f"生成睡眠卡片数据失败: {e}")
            return []
    

