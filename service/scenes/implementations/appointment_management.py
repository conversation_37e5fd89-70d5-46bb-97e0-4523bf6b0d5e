"""
Appointment management scene implementation

基于营养、饮水、睡眠场景成功模式重构，遵循统一设计：
- 2个核心工具配置（获取号源 + 生成卡片）
- 使用模板管理器统一管理提示词
- 集成HSD服务而非AI Clock
- 符合工具调用后直接生成卡片的模式，参考营养场景成功经验

@author: shaohua.sun
@date: 2025/7/2
"""

from typing import List, Dict, Any
from langchain_core.tools import BaseTool

from service.intention.base import IntentionSceneEnum
from service.scenes.base.base_scene import BaseScene
from service.scenes.base.scene_context import SceneContext
from service.scenes.base.scene_registry import scene_registry
from service.templates.scene_templates import AppointmentManagementTemplate
from service.scenes.tools.appointment_tools import (
    get_appointment_slot_list_tool,
    generate_appointment_recommendations_card_tool
)
from utils.biz_logger import get_logger

logger = get_logger(__name__)


@scene_registry.register_scene_decorator(IntentionSceneEnum.APPOINTMENT_MANAGEMENT)
class AppointmentManagementScene(BaseScene):
    """预约管理场景实现（基于营养场景成功模式重构）"""
    def get_tools(self) -> List[BaseTool]:
        """
        Get appointment management scene tools（基于营养场景成功模式重构）
        
        参考营养场景的2工具配置模式：
        1. get_appointment_slot_list_tool - 获取预约号源列表（MANDATORY第一步工具）
        2. generate_appointment_recommendations_card_tool - 生成预约推荐卡片（第二步工具）
        
        工具调用流程：
        - 用户询问预约 -> 调用获取号源工具
        - 通过提示词引导 -> 立即调用卡片生成工具
        - 最终结果：用户获得文本回复 + 预约推荐卡片
        
        Returns:
            List[BaseTool]: Appointment management related tools list
        """
        return [
            get_appointment_slot_list_tool,  # 第一步：获取号源数据（MANDATORY）
            generate_appointment_recommendations_card_tool  # 第二步：生成预约卡片
        ]
    
    def build_system_prompt(self, context: SceneContext) -> str:
        """
        构建预约管理场景的系统提示词，使用场景专用模板管理器

        Args:
            context: Scene context

        Returns:
            str: 预约管理场景专用的系统提示词（包含动态系统信息）
        """
        # 获取基础系统提示词，传递self实例以获取正确的场景名称
        base_prompt = AppointmentManagementTemplate.build_complete_system_prompt(self)

        # 使用BaseScene的方法添加用户信息和其他增强内容
        return self.build_enhanced_system_prompt(base_prompt, context)
    
    async def generate_card_data(self, context: SceneContext, agent_response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        生成预约卡片数据（支持多张卡片）

        Args:
            context: Scene context
            agent_response: Agent response result

        Returns:
            List[Dict[str, Any]]: 预约卡片数据数组
        """
        logger.info("开始生成预约卡片数据")
        
        try:
            # 获取所有预约推荐工具调用结果
            appointment_results = self.get_tool_results('generate_appointment_recommendations_card_tool')
            
            if not appointment_results:
                logger.info("未检测到预约推荐工具调用结果")
                return []
            
            logger.info(f"从工具结果存储中获取到 {len(appointment_results)} 条预约推荐数据")
            
            # 解析所有预约推荐数据并提取卡片
            all_appointment_cards = []
            for result in appointment_results:
                try:
                    import json
                    # 尝试解析JSON字符串
                    card_data = json.loads(result)
                    
                    # 检查是否有错误
                    if "error" not in card_data:
                        # 检查是否为卡片数组（新格式）
                        if isinstance(card_data, list):
                            all_appointment_cards.extend(card_data)
                        else:
                            # 单张卡片（旧格式）
                            all_appointment_cards.append(card_data)
                    else:
                        logger.warning(f"工具返回错误: {card_data['error']}")
                        
                except json.JSONDecodeError:
                    logger.warning(f"工具返回非JSON数据: {result}")
                    continue
            
            if all_appointment_cards:
                logger.info(f"成功生成 {len(all_appointment_cards)} 张预约卡片")
                return all_appointment_cards
            else:
                logger.warning("所有预约推荐数据解析失败")
                return []
                
        except Exception as e:
            logger.error(f"生成预约卡片数据失败: {e}")
            return []
    

