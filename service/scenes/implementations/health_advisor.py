"""
Health advisor scene implementation

基于Java版本HealthConsultationServiceImpl的实现，提供专业的健康咨询服务：
- 专业健康咨询和建议
- 个性化健康指导
- 健康问题解答
- 不生成卡片数据，专注于咨询服务

@author: shaohua.sun
@date: 2025/7/2
"""

from typing import List, Dict, Any
from langchain_core.tools import BaseTool

from service.intention.base import IntentionSceneEnum
from service.scenes.base.base_scene import BaseScene
from service.scenes.base.scene_context import SceneContext
from service.scenes.base.scene_registry import scene_registry
from service.templates.scene_templates import HealthAdvisorTemplate
from service.scenes.tools.health_advisor_tools import (
    get_daily_detail_tool, 
    query_patient_health_indicators_tool,
    query_patient_medical_records_tool
)
from utils.biz_logger import get_logger

logger = get_logger(__name__)


@scene_registry.register_scene_decorator(IntentionSceneEnum.HEALTH_ADVISOR)
class HealthAdvisorScene(BaseScene):
    """Health advisor scene implementation"""
    def get_tools(self) -> List[BaseTool]:
        """
        Get health advisor scene tools
        
        Returns:
            List[BaseTool]: Health advisor related tools list
        """
        return [
            get_daily_detail_tool,  # 健康咨询工具
            query_patient_health_indicators_tool,  # 患者健康指标查询工具
            query_patient_medical_records_tool  # 患者医疗记录查询工具（内嵌SQL代理）
        ]
    
    def build_system_prompt(self, context: SceneContext) -> str:
        """
        构建健康顾问场景的系统提示词，使用场景专用模板管理器

        Args:
            context: Scene context

        Returns:
            str: 健康顾问场景专用的系统提示词（包含动态系统信息）
        """
        # 获取基础系统提示词，传递self实例以获取正确的场景名称
        base_prompt = HealthAdvisorTemplate.build_complete_system_prompt(self)

        # 使用BaseScene的方法添加用户信息和其他增强内容
        return self.build_enhanced_system_prompt(base_prompt, context)
