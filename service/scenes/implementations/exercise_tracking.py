"""
Exercise tracking scene implementation

Based on document requirements and following nutrition scene success pattern:
- Complete exercise tracking functionality
- English prompts and constants following project standards
- Dynamic system information integration
- Proper tool guidance and card data extraction

@author: shaohua.sun
@date: 2025/7/2
"""

from typing import List, Dict, Any, Optional, Generator
from datetime import datetime
from langchain_core.tools import BaseTool

from service.intention.base import IntentionSceneEnum
from service.scenes.base.base_scene import BaseScene
from service.scenes.base.scene_context import SceneContext
from service.scenes.base.scene_registry import scene_registry
from service.templates.scene_templates import ExerciseTrackingTemplate
from service.scenes.tools.exercise_tools import (
    get_daily_detail_tool,
    get_all_exercise_list_tool,
    get_exercise_daily_tool,
    generate_exercise_record_tool
)
from utils.biz_logger import get_logger

logger = get_logger(__name__)





@scene_registry.register_scene_decorator(IntentionSceneEnum.EXERCISE_TRACKING)
class ExerciseTrackingScene(BaseScene):
    """Exercise tracking scene implementation"""
    
    def get_tools(self) -> List[BaseTool]:
        """
        Get exercise tracking scene tools
        
        Ordered by importance and call frequency (matching Java implementation):
        1. Exercise data query tool (most common, MANDATORY)
        2. Exercise types list tool (when user needs exercise options)
        3. Detailed exercise history tool (for comprehensive analysis)
        4. Exercise record generation tool (logging functionality)
        
        Returns:
            List[BaseTool]: Exercise tracking related tools list
        """
        return [
            get_daily_detail_tool,          # Most important, MANDATORY tool first
            get_all_exercise_list_tool,     # Exercise options, second
            get_exercise_daily_tool,        # Detailed analysis, third
            generate_exercise_record_tool   # Recording functionality, fourth
        ]
    
    def build_system_prompt(self, context: SceneContext) -> str:
        """
        构建运动追踪场景的系统提示词，使用场景专用模板管理器

        Args:
            context: Scene context

        Returns:
            str: 运动追踪场景专用的系统提示词（包含动态系统信息和用户信息）
        """
        # 获取基础系统提示词，传递self实例以获取正确的场景名称
        base_prompt = ExerciseTrackingTemplate.build_complete_system_prompt(self)

        # 使用BaseScene的方法添加用户信息和其他增强内容
        return self.build_enhanced_system_prompt(base_prompt, context)
    
    async def generate_card_data(self, context: SceneContext, agent_response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        生成运动卡片数据（支持多张卡片）

        Args:
            context: Scene context
            agent_response: Agent response result

        Returns:
            List[Dict[str, Any]]: 运动卡片数据数组
        """
        logger.info("开始生成运动卡片数据")

        try:
            # 获取所有运动记录工具调用结果
            exercise_record_results = self.get_tool_results('generate_exercise_record_tool')

            if not exercise_record_results:
                logger.info("未检测到运动记录工具调用结果")
                return []

            logger.info(f"从工具结果存储中获取到 {len(exercise_record_results)} 条运动记录数据")

            # 解析所有运动记录并生成卡片
            all_exercise_cards = []
            for result in exercise_record_results:
                try:
                    import json
                    # 尝试解析JSON字符串（工具已经返回完整的卡片结构）
                    complete_card = json.loads(result)
                    if complete_card:
                        all_exercise_cards.append(complete_card)
                        
                except json.JSONDecodeError:
                    logger.warning(f"运动记录数据解析失败: {result}")
                    continue

            if all_exercise_cards:
                logger.info(f"成功生成 {len(all_exercise_cards)} 张运动卡片")
                return all_exercise_cards
            else:
                logger.warning("所有运动记录数据解析失败")
                return []

        except Exception as e:
            logger.error(f"生成运动卡片数据失败: {e}")
            return []
