"""
场景处理相关常量定义

包含：
- 场景处理消息常量
- 错误信息常量
- 配置常量
- 系统提示词常量
- 卡片数据常量
- 模板常量

@author: shaohua.sun
@date: 2025/6/20
"""

class SceneErrorMessages:
    """场景错误消息常量"""

    # 通用错误消息
    GENERAL_ERROR = "抱歉，处理您的请求时出现了问题，请稍后再试。"

    # 处理阶段错误
    PREPROCESSING_FAILED = "前置处理失败"
    AGENT_PROCESSING_FAILED = "Agent处理失败"




class SceneTemplates:
    """场景模板常量"""

    # 时间信息模板
    TIME_INFO_TEMPLATE = """## 当前时间信息
- 当前日期: {current_date}
- 当前时间: {current_time}
- 星期: {weekday}

请在涉及时间相关的操作时使用以上准确的时间信息。"""

    # 用户信息模板
    USER_INFO_TEMPLATE = """## User Information
{user_context}"""

    # 用户上下文字段模板（英文格式，便于LLM理解和工具调用）
    USER_CONTEXT_FIELDS = {
        "user_id": "- User ID: {user_id}"
    }


class SceneMessages:
    """场景处理消息常量"""
    
    # 通用消息
    SCENE_PROCESSING_START = "开始处理场景: {}"
    SCENE_PROCESSING_SUCCESS = "场景处理成功: {}"
    SCENE_PROCESSING_FAILED = "场景处理失败: {} - {}"


class SceneErrors:
    """场景处理错误信息常量"""
    
    # 上下文验证错误
    CONTEXT_IS_NONE = "场景上下文为空"
    CONTEXT_USER_INPUT_EMPTY = "用户输入为空"
