"""
场景注册器

负责管理和注册所有健康场景处理器：
- 场景注册和获取
- 场景验证
- 场景实例管理

@author: shaohua.sun
@date: 2025/6/20
"""

from typing import Dict, Type, Optional, List, Any
from service.intention.base import IntentionSceneEnum
from .base_scene import BaseScene
from utils.biz_logger import get_logger

logger = get_logger(__name__)


class SceneRegistry:
    """
    场景注册器
    
    管理所有健康场景处理器的注册和获取
    采用双层缓存策略：
    1. 默认实例缓存：无配置的默认场景实例
    2. 配置实例缓存：带特定配置的场景实例
    """
    
    _instance = None
    _scenes: Dict[IntentionSceneEnum, Type[BaseScene]] = {}
    # 默认场景实例缓存（无配置）
    _default_scene_instances: Dict[IntentionSceneEnum, BaseScene] = {}
    # 配置场景实例缓存（scene_type + config_hash -> instance）
    _config_scene_instances: Dict[str, BaseScene] = {}
    
    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            cls._instance = super(SceneRegistry, cls).__new__(cls)
        return cls._instance
    
    def register_scene(self, scene_type: IntentionSceneEnum, scene_class: Type[BaseScene]):
        """
        注册场景处理器
        
        Args:
            scene_type: 场景类型枚举
            scene_class: 场景处理器类
        """
        if not issubclass(scene_class, BaseScene):
            raise ValueError(f"Scene class {scene_class.__name__} must inherit from BaseScene")
        
        self._scenes[scene_type] = scene_class
        logger.info(f"Registered scene: {scene_type.value} -> {scene_class.__name__}")
    
    def get_scene(self, scene_type: IntentionSceneEnum, agent_config: Dict = None) -> Optional[BaseScene]:
        """
        获取场景处理器实例（支持双层缓存）
        
        Args:
            scene_type: 场景类型枚举
            agent_config: Agent配置参数（可选）
            
        Returns:
            Optional[BaseScene]: 场景处理器实例，如果未注册则返回None
        """
        # 如果没有配置参数，返回默认实例
        if agent_config is None:
            return self._get_default_scene_instance(scene_type)
        
        # 如果有配置参数，返回配置实例
        return self._get_config_scene_instance(scene_type, agent_config)
    
    def _get_default_scene_instance(self, scene_type: IntentionSceneEnum) -> Optional[BaseScene]:
        """
        获取默认场景实例（无配置）
        
        Args:
            scene_type: 场景类型枚举
            
        Returns:
            Optional[BaseScene]: 默认场景实例
        """
        # 检查是否已有默认实例缓存
        if scene_type in self._default_scene_instances:
            logger.debug(f"返回缓存的默认场景实例: {scene_type.value}")
            return self._default_scene_instances[scene_type]
        
        # 创建默认实例并缓存
        default_instance = self._create_scene_instance(scene_type, None)
        if default_instance:
            self._default_scene_instances[scene_type] = default_instance
            logger.info(f"创建并缓存默认场景实例: {scene_type.value}")
        return default_instance
    
    def _get_config_scene_instance(self, scene_type: IntentionSceneEnum, agent_config: Dict) -> Optional[BaseScene]:
        """
        获取配置场景实例（带特定配置）
        
        Args:
            scene_type: 场景类型枚举
            agent_config: Agent配置参数
            
        Returns:
            Optional[BaseScene]: 配置场景实例
        """
        # 生成配置缓存键
        config_key = self._generate_config_cache_key(scene_type, agent_config)
        
        # 检查是否已有配置实例缓存
        if config_key in self._config_scene_instances:
            logger.debug(f"返回缓存的配置场景实例: {scene_type.value} (key: {config_key[:20]}...)")
            return self._config_scene_instances[config_key]
        
        # 创建配置实例并缓存
        config_instance = self._create_scene_instance(scene_type, agent_config)
        if config_instance:
            self._config_scene_instances[config_key] = config_instance
            logger.info(f"创建并缓存配置场景实例: {scene_type.value} (key: {config_key[:20]}...)")
        return config_instance
    
    def _create_scene_instance(self, scene_type: IntentionSceneEnum, agent_config: Dict = None) -> Optional[BaseScene]:
        """
        创建场景处理器实例
        
        Args:
            scene_type: 场景类型枚举
            agent_config: Agent配置参数
            
        Returns:
            Optional[BaseScene]: 场景处理器实例
        """
        # 检查是否已注册类
        if scene_type not in self._scenes:
            logger.warning(f"Scene type {scene_type.value} not registered")
            return None
        
        # 创建新实例
        try:
            scene_class = self._scenes[scene_type]
            scene_instance = scene_class(scene_type, agent_config)
            logger.debug(f"Created scene instance: {scene_type.value} with config: {bool(agent_config)}")
            return scene_instance
        except Exception as e:
            logger.error(f"Failed to create scene instance for {scene_type.value}: {str(e)}")
            return None
    
    def _generate_config_cache_key(self, scene_type: IntentionSceneEnum, agent_config: Dict) -> str:
        """
        生成配置缓存键
        
        Args:
            scene_type: 场景类型枚举
            agent_config: Agent配置参数
            
        Returns:
            str: 缓存键
        """
        import hashlib
        import json
        
        # 确保配置字典的键有序，以便生成一致的哈希
        config_str = json.dumps(agent_config, sort_keys=True, ensure_ascii=False)
        config_hash = hashlib.md5(config_str.encode('utf-8')).hexdigest()
        
        return f"{scene_type.value}_{config_hash}"
    
    def is_registered(self, scene_type: IntentionSceneEnum) -> bool:
        """
        检查场景是否已注册
        
        Args:
            scene_type: 场景类型枚举
            
        Returns:
            bool: 是否已注册
        """
        return scene_type in self._scenes
    
    def get_all_registered_scenes(self) -> List[IntentionSceneEnum]:
        """
        获取所有已注册的场景类型
        
        Returns:
            List[IntentionSceneEnum]: 已注册的场景类型列表
        """
        return list(self._scenes.keys())
    
    def get_scene_class(self, scene_type: IntentionSceneEnum) -> Optional[Type[BaseScene]]:
        """
        获取场景处理器类（不创建实例）
        
        Args:
            scene_type: 场景类型枚举
            
        Returns:
            Optional[Type[BaseScene]]: 场景处理器类
        """
        return self._scenes.get(scene_type)
    
    def unregister_scene(self, scene_type: IntentionSceneEnum):
        """
        取消注册场景处理器（清理所有相关缓存）
        
        Args:
            scene_type: 场景类型枚举
        """
        if scene_type in self._scenes:
            del self._scenes[scene_type]
            logger.info(f"Unregistered scene: {scene_type.value}")
        
        # 清理默认实例缓存
        if scene_type in self._default_scene_instances:
            del self._default_scene_instances[scene_type]
            logger.info(f"Removed default scene instance: {scene_type.value}")
        
        # 清理配置实例缓存
        keys_to_remove = [key for key in self._config_scene_instances.keys() 
                         if key.startswith(f"{scene_type.value}_")]
        for key in keys_to_remove:
            del self._config_scene_instances[key]
        
        if keys_to_remove:
            logger.info(f"Removed {len(keys_to_remove)} config scene instances for: {scene_type.value}")
    
    def clear_all(self):
        """清除所有注册的场景和缓存"""
        self._scenes.clear()
        self._default_scene_instances.clear()
        self._config_scene_instances.clear()
        logger.info("Cleared all registered scenes and caches")
    
    def clear_config_cache(self):
        """清除配置场景实例缓存（保留默认实例缓存）"""
        cache_count = len(self._config_scene_instances)
        self._config_scene_instances.clear()
        logger.info(f"Cleared {cache_count} config scene instances from cache")
    
    def register_scene_decorator(self, scene_type: IntentionSceneEnum):
        """
        场景注册装饰器（作为实例方法）

        Args:
            scene_type: 场景类型枚举

        Returns:
            装饰器函数
        """
        def decorator(scene_class: Type[BaseScene]):
            self.register_scene(scene_type, scene_class)
            return scene_class
        return decorator

    def get_scene_instance(self, scene_type: IntentionSceneEnum, agent_config: Dict = None) -> Optional[BaseScene]:
        """
        获取场景处理器实例的便捷方法（作为实例方法）

        Args:
            scene_type: 场景类型枚举
            agent_config: Agent配置参数（可选）

        Returns:
            Optional[BaseScene]: 场景处理器实例
        """
        return self.get_scene(scene_type, agent_config)

    def get_registry_status(self) -> Dict[str, any]:
        """
        获取注册器状态信息（双层缓存详情）

        Returns:
            Dict[str, any]: 状态信息
        """
        return {
            "total_registered": len(self._scenes),
            "default_instances": len(self._default_scene_instances),
            "config_instances": len(self._config_scene_instances),
            "total_cached_instances": len(self._default_scene_instances) + len(self._config_scene_instances),
            "registered_scenes": [scene.value for scene in self._scenes.keys()],
            "default_cached_scenes": [scene.value for scene in self._default_scene_instances.keys()],
            "config_cache_keys": list(self._config_scene_instances.keys()),
            "cache_strategy": "dual_layer"
        }


# 全局注册器实例
scene_registry = SceneRegistry()