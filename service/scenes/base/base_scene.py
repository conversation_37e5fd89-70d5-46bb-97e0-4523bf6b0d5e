"""
场景处理抽象基类

基于ReactAgent设计理念的模板方法框架：
- 定义标准的场景执行流程模板
- 具体步骤实现由子类自定义
- 提供默认实现以简化子类开发
- 支持灵活的ReactAgent配置和记忆管理

@author: shaohua.sun
@date: 2025/6/20
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Generator
import logging
import threading
import time
import json
from datetime import datetime

from langchain_core.tools import BaseTool
from ai.agent.react_agent import ReactAgent
from ai.router import Router
from ai.config.ai_config import FactoryConfig, ModelConfig, RouterConfig
from ai.config.ai_properties import AIProperties
from service.intention.base import IntentionSceneEnum
from .scene_context import SceneContext
from .constants import SceneMessages, SceneErrors, SceneTemplates, SceneErrorMessages
from utils.biz_logger import get_logger


# 流式输出标记常量
class StreamMarkers:
    """流式输出标记常量"""
    TEXT_START = "[TEXT_START]"
    TEXT_END = "[TEXT_END]"
    ALL_START = "[ALL_START]"
    ALL_END = "[ALL_END]"


# 请求级别的工具结果存储
class RequestToolStorage:
    """请求级别的工具结果存储，确保每次请求的工具结果独立，支持同一工具多次调用"""

    def __init__(self):
        self.results: Dict[str, Any] = {}  # 存储工具调用结果的字典

    def store_result(self, tool_name: str, result: Any):
        """
        存储工具调用结果，支持同一工具多次调用
        
        Args:
            tool_name: 工具名称
            result: 工具调用结果
        """
        if tool_name not in self.results:
            # 首次调用，直接存储
            self.results[tool_name] = result
        else:
            # 多次调用，转换为列表存储
            if not isinstance(self.results[tool_name], list):
                # 将之前的单个结果转换为列表
                self.results[tool_name] = [self.results[tool_name]]
            # 添加新结果
            self.results[tool_name].append(result)

    def get_results_for_tool(self, tool_name: str) -> List[Any]:
        """
        获取指定工具的所有调用结果（始终返回列表）
        
        Args:
            tool_name: 工具名称
            
        Returns:
            List[Any]: 所有调用结果的列表，如果没有调用则返回空列表
        """
        result = self.results.get(tool_name)
        if result is None:
            return []
        elif isinstance(result, list):
            return result
        else:
            return [result]

    def clear(self):
        """清空所有结果"""
        self.results.clear()


class BaseScene(ABC):
    """
    场景处理抽象基类
    
    使用模板方法模式定义标准的场景执行流程：
    1. 前置处理：上下文验证、Agent初始化
    2. 核心处理：ReactAgent对话和工具调用
    3. 后置处理：结果处理、卡片数据生成
    
    子类只需实现具体的业务逻辑步骤即可
    """
    
    def __init__(self, scene_type: IntentionSceneEnum, agent_config: Dict[str, Any] = None):
        """
        初始化场景处理器
        
        Args:
            scene_type: 场景类型枚举
            agent_config: Agent配置参数（可选）
        """
        self.scene_type: IntentionSceneEnum = scene_type                      # 场景类型
        self.logger = get_logger(self.__class__.__name__)                      # 日志记录器
        self.agent_config: Dict[str, Any] = agent_config or self._get_default_agent_config()  # Agent配置
        # self._agent_cache: Dict[str, ReactAgent] = {}                          # 基于thread_id的Agent缓存（已移除缓存机制）
        self._current_request_agent: Optional[ReactAgent] = None               # 当前请求的ReactAgent实例（单次请求内复用）
        self._tool_storage: RequestToolStorage = RequestToolStorage()          # 当前请求的工具结果存储
        self.logger.info(SceneMessages.SCENE_PROCESSING_START.format(scene_type.value))
    
    # ==================== 模板方法：标准执行流程 ====================
    
    async def handle(self, context: SceneContext) -> Dict[str, Any]:
        """
        场景处理的标准模板流程
        
        标准流程：
        1. 前置处理：验证上下文、初始化Agent
        2. 核心处理：使用ReactAgent进行对话处理
        3. 后置处理：解析结果、生成卡片数据
        
        Args:
            context: 场景上下文信息
            
        Returns:
            Dict[str, Any]: 统一格式的处理结果
        """
        try:
            self.logger.info(f"开始处理场景: {self.scene_type.value}")
            
            # 清空当前请求的Agent实例，确保每次请求都创建新的Agent
            self._current_request_agent = None
            
            # 步骤1: 前置处理
            if not await self._pre_process(context):
                return self._create_error_response(SceneErrorMessages.PREPROCESSING_FAILED)
            
            # 步骤2: 核心处理 - ReactAgent对话
            agent_response = await self._core_process(context)
            if not agent_response:
                return self._create_error_response(SceneErrorMessages.AGENT_PROCESSING_FAILED)
            
            # 步骤3: 后置处理 - 结果处理和卡片生成
            final_result = await self._post_process(context, agent_response)
            
            self.logger.info(f"场景处理完成: {self.scene_type.value}")
            return final_result
            
        except Exception as e:
            self.logger.error(f"场景处理异常: {self.scene_type.value} - {str(e)}")
            return self._create_error_response(str(e))
        finally:
            # 请求处理完成后清空当前请求的Agent实例
            self._current_request_agent = None
    
    async def handle_stream(self, context: SceneContext) -> Generator[Dict[str, Any], None, None]:
        """
        流式处理的标准模板流程
        
        Args:
            context: 场景上下文信息
            
        Yields:
            Dict[str, Any]: 流式处理结果
        """
        try:
            self.logger.info(f"开始流式处理场景: {self.scene_type.value}")
            
            # 清空当前请求的Agent实例，确保每次请求都创建新的Agent
            self._current_request_agent = None
            
            # 步骤1: 前置处理
            if not await self._pre_process(context):
                yield {"success": False, "error": SceneErrorMessages.PREPROCESSING_FAILED, "final": True}
                return
            
            # 步骤2: 流式核心处理（包含文本流式输出和卡片发送）
            async for chunk in self._core_process_stream(context):
                yield chunk
            
            # 流式处理完成，发送最终完成标记
            yield {"success": True, "final": True}
            
        except Exception as e:
            self.logger.error(f"流式处理异常: {self.scene_type.value} - {str(e)}")
            yield {"success": False, "error": str(e), "final": True}
        finally:
            # 请求处理完成后清空当前请求的Agent实例
            self._current_request_agent = None
    
    # ==================== 模板步骤：默认实现（子类可重写） ====================
    
    async def _pre_process(self, context: SceneContext) -> bool:
        """
        前置处理步骤（默认实现，子类可重写）
        
        默认流程：
        1. 验证上下文
        2. 初始化ReactAgent
        3. 更新系统提示词
        
        Args:
            context: 场景上下文
            
        Returns:
            bool: 是否处理成功
        """
        # 验证上下文
        if not self.validate_context(context):
            return False
        
        # 初始化ReactAgent（基于上下文创建，已移除缓存机制）
        try:
            self._get_or_create_react_agent(context)
            # 注意：Agent创建时已包含完整的系统提示词，无需再次更新
            return True
        except Exception as e:
            self.logger.error(f"前置处理失败: {str(e)}")
            return False
    
    async def _core_process(self, context: SceneContext) -> Optional[Dict[str, Any]]:
        """
        核心处理步骤（默认实现，子类可重写）

        默认流程：
        1. 获取ReactAgent实例
        2. 使用thread_id进行记忆管理
        3. 调用ReactAgent处理用户输入
        4. 提取AI响应
        5. 存储工具调用结果到线程本地存储

        Args:
            context: 场景上下文

        Returns:
            Optional[Dict[str, Any]]: Agent响应结果
        """
        try:
            # 清空之前的工具结果存储
            self._tool_storage.clear()

            react_agent = self._get_or_create_react_agent(context)
            thread_id = self.get_thread_id(context)

            # 调用ReactAgent
            response = react_agent.run(context.user_input)

            # 提取AI响应
            ai_response = self._extract_ai_response(response)

            # 存储工具调用结果（从response中提取）
            self._extract_current_tool_results(response)

            return {
                "response": ai_response,
                "thread_id": thread_id,
                "raw_response": response
            }

        except Exception as e:
            self.logger.error(f"核心处理失败: {str(e)}")
            return None
    
    async def _core_process_stream(self, context: SceneContext) -> Generator[Dict[str, Any], None, None]:
        """
        流式核心处理步骤（伪流式实现）

        实现流程：
        1. 使用同步run方法获取完整响应
        2. 模拟流式输出效果（逐字符返回）
        3. 添加流式标记和卡片数据
        4. 存储工具调用结果供后续使用

        Args:
            context: 场景上下文

        Yields:
            Dict[str, Any]: 流式处理块
        """
        try:
            # 清空当前请求的工具结果存储
            self._tool_storage.clear()

            react_agent = self._get_or_create_react_agent(context)
            thread_id = self.get_thread_id(context)

            # 发送开始标记
            yield {"text": json.dumps({"text": StreamMarkers.TEXT_START})}
            yield {"marker": StreamMarkers.ALL_START}

            # 使用同步run方法获取完整响应
            response = react_agent.run(context.user_input)

            # 提取AI响应文本
            ai_response = self._extract_ai_response(response)

            # 提取并存储当次对话的工具调用结果
            self._extract_current_tool_results(response)

            # 模拟流式输出（可配置批次大小）
            async for chunk in self._simulate_streaming_output(ai_response, batch_size=10):
                yield chunk

            # 发送结束标记
            yield {"marker": StreamMarkers.ALL_END}

            # 生成并发送卡片数据（支持多张卡片）
            async for card_chunk in self._generate_and_send_cards(context, {
                "response": ai_response,
                "thread_id": thread_id,
                "raw_response": response
            }):
                yield card_chunk

            # 发送文本结束标记
            yield {"text": json.dumps({"text": StreamMarkers.TEXT_END})}

        except Exception as e:
            self.logger.error(f"流式核心处理失败: {str(e)}")
            yield {"success": False, "error": str(e), "final": True}
    
    async def _post_process(self, context: SceneContext, agent_response: Dict[str, Any]) -> Dict[str, Any]:
        """
        后置处理步骤（默认实现，子类可重写）
        
        默认流程：
        1. 解析Agent响应
        2. 生成卡片数据
        3. 构建最终响应
        
        Args:
            context: 场景上下文
            agent_response: Agent响应结果
            
        Returns:
            Dict[str, Any]: 最终处理结果
        """
        ai_response = agent_response.get("response", "")
        
        # 生成卡片数据（现在返回数组）
        card_data_list = await self.generate_card_data(context, agent_response)
        
        # 构建最终结果
        return {
            "success": True,
            "ai_response": ai_response,
            "data": card_data_list,  # 非流式处理返回完整的卡片数组
            "data_type": "card" if card_data_list else "text",
            "thread_id": agent_response.get("thread_id"),
            "scene_type": self.scene_type.value
        }
    
    # ==================== 抽象方法：子类必须实现 ====================
    
    @abstractmethod
    def build_system_prompt(self, context: SceneContext) -> str:
        """
        构建场景专用的系统提示词（抽象方法）

        子类必须实现此方法来定义场景特定的AI行为

        Args:
            context: 场景上下文

        Returns:
            str: 场景专用的系统提示词
        """
        pass
    
    @abstractmethod
    def get_tools(self) -> List[BaseTool]:
        """
        获取场景专用的工具列表（抽象方法）
        
        子类必须实现此方法来定义场景可用的工具
        
        Returns:
            List[BaseTool]: 场景专用工具列表
        """
        pass
    
    # ==================== 可重写的业务方法 ====================
    
    async def generate_card_data(self, context: SceneContext, agent_response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        生成卡片数据（默认不生成，子类可重写）

        子类可以重写此方法来实现特定的卡片数据生成逻辑。
        可以通过get_tool_results()方法获取工具调用结果（返回列表）。

        Args:
            context: 场景上下文
            agent_response: Agent响应结果

        Returns:
            List[Dict[str, Any]]: 卡片数据数组
                - 返回空列表表示不生成卡片
                - 返回[单张卡片]表示生成单张卡片
                - 返回[卡片1,卡片2,...]表示生成多张卡片
        """
        return []
    
    async def _generate_and_send_cards(self, context: SceneContext, agent_response: Dict[str, Any]) -> Generator[Dict[str, Any], None, None]:
        """
        生成并发送卡片数据（统一处理单张、多张、无卡片场景）
        
        调用generate_card_data方法获取卡片数组，然后流式发送每张卡片。
        
        Args:
            context: 场景上下文
            agent_response: Agent响应结果
            
        Yields:
            Dict[str, Any]: 卡片数据块
        """
        try:
            # 获取卡片数据数组
            card_data_list = await self.generate_card_data(context, agent_response)
            
            # 如果没有卡片数据，直接返回
            if not card_data_list:
                self.logger.info("当前场景不生成卡片数据")
                return
            
            # 流式发送每张卡片
            self.logger.info(f"检测到 {len(card_data_list)} 张卡片")
            for i, card_data in enumerate(card_data_list):
                if card_data:
                    yield {"card": json.dumps(card_data)}
                    self.logger.info(f"成功发送第 {i + 1} 张卡片")
                    
        except Exception as e:
            self.logger.error(f"生成卡片数据失败: {str(e)}")

    def get_tool_results(self, tool_name: str) -> List[Any]:
        """
        获取指定工具的所有调用结果（始终返回列表）
        
        Args:
            tool_name: 工具名称
            
        Returns:
            List[Any]: 所有调用结果的列表，如果没有调用则返回空列表
        """
        return self._tool_storage.get_results_for_tool(tool_name)
    
    # ==================== 基础公共函数 ====================
    
    async def _simulate_streaming_output(self, text: str, batch_size: int = 1, delay: float = 0.01) -> Generator[Dict[str, Any], None, None]:
        """
        模拟流式输出的封装函数
        
        支持可配置的批次大小，提高前端处理效率。
        默认保持1个字符的兼容性，确保现有逻辑不受影响。
        
        Args:
            text: 要输出的文本内容
            batch_size: 每次yield的字符数量，默认为1（保持兼容性）
            delay: 每次输出的延迟时间（秒），默认0.01秒
            
        Yields:
            Dict[str, Any]: 流式输出块，格式为 {"text": json.dumps({"text": chunk})}
        """
        if not text:
            return
            
        # 按批次大小分割文本
        for i in range(0, len(text), batch_size):
            # 获取当前批次的文本片段
            chunk = text[i:i + batch_size]
            
            # 按照原有格式输出
            yield {"text": json.dumps({"text": chunk})}
            
            # 模拟流式延迟
            time.sleep(delay)
    
    def _get_default_agent_config(self) -> Dict[str, Any]:
        """获取默认Agent配置"""
        return {
            'platform': AIProperties.PLATFORM_OPENAI,
            'model_name': 'gpt-4o-mini',
            'temperature': 0.7,
            'max_tokens': 1000,
            'streaming': True,
            'timeout': 30,
            'store_type': 'postgres',  # 使用postgres存储
            'history_strategy': 'trim',  # 改为trim策略，避免摘要丢失信息
            'overwrite_history': False,
            'mem_max_tokens': 10000,  # 增加到10000，提供更多记忆空间
            'mem_max_summary_tokens': 8000  # 增加摘要长度（虽然现在用trim策略）
        }
    
    def _get_or_create_react_agent(self, context: SceneContext) -> ReactAgent:
        """
        获取或创建ReactAgent实例（单次请求内复用，跨请求不缓存）

        设计说明：
        - 单次请求内（handle或handle_stream调用期间）复用同一个ReactAgent实例
        - 跨请求不缓存，每次新请求都会创建新的ReactAgent
        - 避免同一请求中多次创建ReactAgent导致的工具配置问题

        Args:
            context: 场景上下文（必需，用于获取thread_id和user_id）

        Returns:
            ReactAgent: 当前请求的ReactAgent实例
        """
        # 如果当前请求已有Agent实例，直接返回
        if self._current_request_agent is not None:
            thread_id = self.get_thread_id(context)
            user_id = context.get_user_id() or 'anonymous'
            self.logger.debug(f"复用当前请求的ReactAgent实例 - thread_id: {thread_id}, user_id: {user_id}")
            return self._current_request_agent

        # 获取基本信息
        thread_id = self.get_thread_id(context)
        user_id = context.get_user_id() or 'anonymous'

        try:
            # 创建新的ReactAgent实例
            self.logger.info(f"创建新的ReactAgent - thread_id: {thread_id}, user_id: {user_id}")

            # 使用Router统一处理模型实例创建
            router_config = RouterConfig(task_type='text')
            ai_router = Router(router_config)

            factory_config = FactoryConfig(
                platform=self.agent_config.get('platform', AIProperties.PLATFORM_OPENAI)
            )

            model_config = ModelConfig(
                model_name=self.agent_config.get('model_name', 'gpt-4o-mini'),
                temperature=self.agent_config.get('temperature', 0.7),
                max_tokens=self.agent_config.get('max_tokens', 1000),
                streaming=self.agent_config.get('streaming', True),
                timeout=self.agent_config.get('timeout', 30)
            )

            model = ai_router.handle(factory_config=factory_config, model_config=model_config)
            tools = self.get_tools()

            # 构建完整的系统提示词
            system_prompt = self.build_system_prompt(context)
            enhanced_prompt = self.build_enhanced_system_prompt(system_prompt, context)

            # 创建ReactAgent（传入thread_id和biz_user_id）
            react_agent = ReactAgent(
                model=model,
                tools=tools,
                system_prompt=enhanced_prompt,
                store_type=self.agent_config.get('store_type', 'postgres'),
                history_strategy=self.agent_config.get('history_strategy', 'trim'),
                overwrite_history=self.agent_config.get('overwrite_history', False),
                max_tokens=self.agent_config.get('mem_max_tokens', 10000),
                max_summary_tokens=self.agent_config.get('mem_max_summary_tokens', 8000),
                thread_id=thread_id,
                biz_user_id=user_id
            )

            # 存储到当前请求实例
            self._current_request_agent = react_agent

            self.logger.info(f"ReactAgent创建成功 - thread_id: {thread_id}, user_id: {user_id}")
            return react_agent

        except Exception as e:
            self.logger.error(f"创建ReactAgent失败: {str(e)}")
            raise RuntimeError(f"创建ReactAgent失败: {str(e)}")
    
    def _extract_ai_response(self, response) -> str:
        """从ReactAgent响应中提取AI回复"""
        if response and "messages" in response:
            last_message = response["messages"][-1]
            if hasattr(last_message, 'content'):
                return last_message.content
            elif isinstance(last_message, dict) and 'content' in last_message:
                return last_message['content']
            else:
                return str(last_message)
        return ""

    def _extract_current_tool_results(self, response):
        """
        提取当次对话的工具调用结果

        关键设计：只提取当次对话产生的工具调用结果，避免历史记忆混淆
        通过分析响应中的消息序列，识别最新的工具调用和结果对
        """
        try:
            if not response or "messages" not in response:
                return

            messages = response["messages"]
            if not messages:
                return

            # 找到最后一个HumanMessage的位置（当次用户输入）
            last_human_index = -1
            for i in range(len(messages) - 1, -1, -1):
                message = messages[i]
                if hasattr(message, 'type') and message.type == 'human':
                    last_human_index = i
                    break

            if last_human_index == -1:
                self.logger.warning("未找到当次用户输入消息，无法确定工具调用范围")
                return

            # 只处理最后一个HumanMessage之后的消息（当次对话产生的）
            current_messages = messages[last_human_index + 1:]

            # 提取当次对话的工具调用结果
            for message in current_messages:
                if hasattr(message, 'type') and message.type == 'tool':
                    tool_name = getattr(message, 'name', None)
                    tool_content = getattr(message, 'content', None)

                    if tool_name and tool_content:
                        self._tool_storage.store_result(tool_name, tool_content)
                        self.logger.info(f"存储当次工具调用结果: {tool_name}")

        except Exception as e:
            self.logger.warning(f"提取当次工具调用结果时出错: {str(e)}")
    
    def _create_error_response(self, error_msg: str) -> Dict[str, Any]:
        """创建错误响应"""
        return {
            "success": False,
            "ai_response": SceneErrorMessages.GENERAL_ERROR,
            "data": None,
            "data_type": "text",
            "error": error_msg,
            "scene_type": self.scene_type.value
        }
    
    def get_scene_type(self) -> IntentionSceneEnum:
        """获取场景类型"""
        return self.scene_type
    
    def get_scene_description(self) -> str:
        """获取场景描述"""
        return self.scene_type.get_description() if hasattr(self.scene_type, 'get_description') else self.scene_type.value
    
    def get_scene_name(self) -> str:
        """
        获取场景名称（用于远程提示词映射）
        
        Returns:
            str: 场景名称，基于场景枚举值，确保与远程API的一致性
        """
        return self.scene_type.value
    
    def validate_context(self, context: SceneContext) -> bool:
        """验证场景上下文（基础实现，子类可重写）"""
        if context is None:
            self.logger.error(SceneErrors.CONTEXT_IS_NONE)
            return False
        
        if not context.user_input or not context.user_input.strip():
            self.logger.error(SceneErrors.CONTEXT_USER_INPUT_EMPTY)
            return False
        
        if not context.thread_id:
            self.logger.error("SceneContext thread_id is missing")
            return False
        
        return True
    
    def get_thread_id(self, context: SceneContext) -> str:
        """
        获取线程ID用于ReactAgent记忆管理

        直接使用context.thread_id，与ReactAgent保持一致
        如果thread_id为空，则根据用户ID生成默认thread_id
        """
        if context.thread_id:
            return context.thread_id

        user_id = context.get_user_id()
        if user_id:
            return f"scene_{self.scene_type.value}_{user_id}"

        return f"scene_{self.scene_type.value}_default"
    
    def build_enhanced_system_prompt(self, base_prompt: str, context: SceneContext) -> str:
        """构建增强的系统提示词（基础实现，子类可重写）"""
        enhanced_parts = []
        
        # 添加当前时间信息
        current_time = datetime.now()
        time_info = SceneTemplates.TIME_INFO_TEMPLATE.format(
            current_date=current_time.strftime('%Y-%m-%d'),
            current_time=current_time.strftime('%H:%M:%S'),
            weekday=current_time.strftime('%A')
        )
        enhanced_parts.append(time_info)

        # 添加用户信息（如果可用）
        if context.user_info:
            user_info = self.build_user_context_prompt(context.user_info)
            if user_info:
                enhanced_parts.append(SceneTemplates.USER_INFO_TEMPLATE.format(user_context=user_info))
        
        enhanced_parts.append(base_prompt)
        
        return "\n".join(enhanced_parts)
    
    def build_user_context_prompt(self, user_info) -> str:
        """构建用户信息上下文提示词（基础实现，子类可重写）"""
        if not user_info:
            return ""

        context_parts = []
        templates = SceneTemplates.USER_CONTEXT_FIELDS

        # 只使用真正的third_user_id进行工具调用
        # biz_user_id只是前端传递的参数，不应该被当作用户ID使用
        user_id_for_tools = user_info.third_user_id
        
        if user_id_for_tools:
            context_parts.append(templates["user_id"].format(user_id=user_id_for_tools))
            
            # 添加工具调用说明
            context_parts.append("")  # 空行分隔
            context_parts.append("**Tool Call Instructions:**")
            context_parts.append("- When calling tools that require 'user_id' parameter, use the User ID value shown above")
            context_parts.append("- Always include user_id in data retrieval tools to ensure accurate and personalized results")
            context_parts.append("- Note: Additional user profile data (name, age, etc.) may be retrieved through user service integration in the future")
        else:
            # 如果没有真正的用户ID，提示需要认证
            context_parts.append("**User Authentication Status:**")
            context_parts.append("- User ID: Not available (authentication required)")
            context_parts.append("- Tools requiring user_id parameter cannot be used")
            context_parts.append("- Please inform user that authentication is required for personalized data access")

        return "\n".join(context_parts) if context_parts else ""
    

    
    def get_agent_info(self) -> Dict[str, Any]:
        """获取Agent状态信息（基础实现，子类可重写，已移除缓存机制）"""
        # 解析缓存键信息（已移除缓存机制）
        # cache_details = []
        # for cache_key in self._agent_cache.keys():
        #     if '#' in cache_key:
        #         thread_id, user_id = cache_key.split('#', 1)
        #         cache_details.append({"thread_id": thread_id, "user_id": user_id, "cache_key": cache_key})
        #     else:
        #         cache_details.append({"cache_key": cache_key})

        return {
            "scene_type": self.scene_type.value,
            "class_name": self.__class__.__name__,
            "tools_count": len(self.get_tools()),
            "cached_agents_count": 0,  # 缓存机制已移除
            "cache_details": [],  # 缓存机制已移除
            "cache_strategy": "no_cache"  # 已移除缓存
        }

    def clear_agent_cache(self, thread_id: str = None, user_id: str = None):
        """
        清理Agent缓存（已移除缓存机制，此方法现在为空操作）

        Args:
            thread_id: 指定要清理的thread_id
            user_id: 指定要清理的user_id
            如果都为None则清理所有缓存
        """
        # 缓存机制已移除，此方法现在为空操作
        self.logger.info("清理Agent缓存方法被调用，但缓存机制已移除，无需操作")
        # if thread_id and user_id:
        #     # 清理指定的thread_id + user_id组合
        #     cache_key = f"{thread_id}#{user_id}"
        #     if cache_key in self._agent_cache:
        #         del self._agent_cache[cache_key]
        #         self.logger.info(f"已清理指定缓存: {cache_key}")
        #     else:
        #         self.logger.warning(f"指定的缓存键不存在: {cache_key}")
        # elif thread_id:
        #     # 清理所有包含指定thread_id的缓存
        #     keys_to_remove = [key for key in self._agent_cache.keys() if key.startswith(f"{thread_id}#")]
        #     for key in keys_to_remove:
        #         del self._agent_cache[key]
        #     self.logger.info(f"已清理thread_id相关的缓存: {thread_id}，共清理 {len(keys_to_remove)} 个实例")
        # elif user_id:
        #     # 清理所有包含指定user_id的缓存
        #     keys_to_remove = [key for key in self._agent_cache.keys() if key.endswith(f"#{user_id}")]
        #     for key in keys_to_remove:
        #         del self._agent_cache[key]
        #     self.logger.info(f"已清理user_id相关的缓存: {user_id}，共清理 {len(keys_to_remove)} 个实例")
        # else:
        #     # 清理所有缓存
        #     cache_count = len(self._agent_cache)
        #     self._agent_cache.clear()
        #     self.logger.info(f"已清理所有Agent缓存，共清理 {cache_count} 个实例")


# 注册机制已移至 scene_registry 类中，无需在此导入