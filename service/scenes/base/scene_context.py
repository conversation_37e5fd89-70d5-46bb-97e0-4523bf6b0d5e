"""
场景上下文类

定义场景处理过程中需要的上下文信息：
- 用户输入
- 用户信息  
- 会话状态
- 其他元数据

注意：对话记忆由ReactAgent通过thread_id自动管理，
SceneContext不再负责对话历史的存储和处理。

@author: shaohua.sun
@date: 2025/6/20
"""

from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime


@dataclass
class UserInfo:
    """
    用户信息数据类

    注意：前端当前只提供 biz_user_id 字段，其他字段暂时为可选
    未来可能通过用户服务或其他方式获取完整用户信息
    """
    biz_user_id: str  # 前端提供的业务用户ID，作为主要标识
    
    # 从chatbot服务获取的真实用户ID（各个场景应该使用这个ID）
    third_user_id: Optional[str] = None

    # 以下字段暂时为可选，等待后续集成用户服务时填充
    name: Optional[str] = None
    age: Optional[int] = None
    gender: Optional[str] = None
    height: Optional[float] = None
    weight: Optional[float] = None
    health_goals: Optional[List[str]] = None
    preferences: Optional[Dict[str, Any]] = None
    additional_info: Optional[Dict[str, Any]] = None

    # 向后兼容属性 - 映射到 third_user_id（真正的用户ID）
    @property
    def user_id(self) -> Optional[str]:
        """向后兼容：user_id属性映射到third_user_id（真正的用户ID）"""
        return self.third_user_id

    @user_id.setter
    def user_id(self, value: Optional[str]):
        """向后兼容：user_id设置器映射到third_user_id"""
        self.third_user_id = value

    @property
    def user_name(self) -> Optional[str]:
        """向后兼容：user_name属性"""
        return self.name

    @user_name.setter
    def user_name(self, value: Optional[str]):
        """向后兼容：user_name设置器"""
        self.name = value


@dataclass
class SceneContext:
    """
    场景上下文数据类
    
    包含场景处理所需的所有上下文信息
    """
    
    # 必需字段
    user_input: str
    thread_id: str  # 统一使用thread_id，与ReactAgent保持一致
    
    # 可选字段
    user_info: Optional[UserInfo] = None
    scene_status: Optional[Dict[str, Any]] = None
    channel_info: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    
    # 时间戳
    timestamp: datetime = None
    created_at: datetime = None  # 保持向后兼容
    
    def __post_init__(self):
        """初始化后处理"""
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.created_at is None:
            self.created_at = self.timestamp
    
    def get_user_id(self) -> Optional[str]:
        """
        获取用户ID（只返回真正的third_user_id）
        
        biz_user_id只是前端传递的参数，用于转换获取真正的用户ID，
        它本身不应该被当作用户ID使用。
        
        Returns:
            Optional[str]: 真正的用户ID（third_user_id），如果没有则返回None
        """
        if not self.user_info:
            return None
        # 只返回真正的用户ID，不兼容biz_user_id
        return self.user_info.third_user_id

    def get_biz_user_id(self) -> Optional[str]:
        """获取业务用户ID"""
        return self.user_info.biz_user_id if self.user_info else None
    

    
    def get_scene_status(self, key: str, default: Any = None) -> Any:
        """
        获取场景状态信息
        
        Args:
            key: 状态键
            default: 默认值
            
        Returns:
            Any: 状态值
        """
        if not self.scene_status:
            return default
        return self.scene_status.get(key, default)
    
    def set_scene_status(self, key: str, value: Any):
        """
        设置场景状态信息
        
        Args:
            key: 状态键
            value: 状态值
        """
        if self.scene_status is None:
            self.scene_status = {}
        self.scene_status[key] = value
    
    def get_metadata(self, key: str, default: Any = None) -> Any:
        """
        获取元数据
        
        Args:
            key: 元数据键
            default: 默认值
            
        Returns:
            Any: 元数据值
        """
        if not self.metadata:
            return default
        return self.metadata.get(key, default)
    
    def set_metadata(self, key: str, value: Any):
        """
        设置元数据
        
        Args:
            key: 元数据键
            value: 元数据值
        """
        if self.metadata is None:
            self.metadata = {}
        self.metadata[key] = value
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        Returns:
            Dict[str, Any]: 字典格式的上下文数据
        """
        result = {
            "user_input": self.user_input,
            "thread_id": self.thread_id,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }
        
        if self.user_info:
            result["user_info"] = {
                "biz_user_id": self.user_info.biz_user_id,  # 主要字段
                "third_user_id": self.user_info.third_user_id,  # 真实用户ID
                "user_id": self.user_info.third_user_id,  # 向后兼容
                "user_name": self.user_info.name,  # 向后兼容
                "name": self.user_info.name,
                "age": self.user_info.age,
                "gender": self.user_info.gender,
                "height": self.user_info.height,
                "weight": self.user_info.weight,
                "health_goals": self.user_info.health_goals,
                "preferences": self.user_info.preferences,
                "additional_info": self.user_info.additional_info
            }
        
        if self.scene_status:
            result["scene_status"] = self.scene_status
        
        if self.channel_info:
            result["channel_info"] = self.channel_info
        
        if self.metadata:
            result["metadata"] = self.metadata
        
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SceneContext':
        """
        从字典创建上下文对象
        
        Args:
            data: 字典数据
            
        Returns:
            SceneContext: 上下文对象
        """
        context = cls(
            user_input=data["user_input"],
            thread_id=data["thread_id"]
        )
        
        if "user_info" in data and data["user_info"]:
            user_data = data["user_info"]
            # 优先使用 biz_user_id，向后兼容 user_id
            biz_user_id = user_data.get("biz_user_id") or user_data.get("user_id")
            if not biz_user_id:
                raise ValueError("user_info must contain either 'biz_user_id' or 'user_id'")

            context.user_info = UserInfo(
                biz_user_id=biz_user_id,
                third_user_id=user_data.get("third_user_id"),  # 真实用户ID
                name=user_data.get("name") or user_data.get("user_name"),  # 支持两种字段名
                age=user_data.get("age"),
                gender=user_data.get("gender"),
                height=user_data.get("height"),
                weight=user_data.get("weight"),
                health_goals=user_data.get("health_goals"),
                preferences=user_data.get("preferences"),
                additional_info=user_data.get("additional_info")
            )
        
        if "scene_status" in data:
            context.scene_status = data["scene_status"]
        
        if "channel_info" in data:
            context.channel_info = data["channel_info"]
        
        if "metadata" in data:
            context.metadata = data["metadata"]
        
        if "created_at" in data and data["created_at"]:
            context.created_at = datetime.fromisoformat(data["created_at"])
        
        return context 