"""
场景处理抽象基础组件

包含：
- BaseScene: 抽象基类
- SceneRegistry: 场景注册器
- SceneContext: 场景上下文
- 相关常量和配置

@author: shaohua.sun
@date: 2025/6/20
"""

# 基础组件
from .base_scene import BaseScene
from .scene_context import SceneContext, UserInfo
from .constants import SceneMessages, SceneErrors, SceneErrorMessages, SceneTemplates

# 注册机制
from .scene_registry import scene_registry

__all__ = [
    # 基础组件
    'BaseScene',
    'SceneContext',
    'UserInfo',
    'SceneMessages',
    'SceneErrors',
    'SceneErrorMessages',
    'SceneTemplates',

    # 注册机制（全局实例）
    'scene_registry'  # 场景注册器实例（推荐使用）
]