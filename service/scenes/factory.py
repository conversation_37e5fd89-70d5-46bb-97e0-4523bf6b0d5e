"""
场景工厂

提供统一的场景调用入口和动态管理：
- 统一的场景调用接口，避免业务代码直接耦合具体场景
- 动态场景路由分发，根据场景类型自动选择处理器
- 场景生命周期管理，包括注册、实例化、缓存
- 基于LangGraph的流式处理支持
- 优雅的解耦设计，业务流程只需调用工厂即可

设计理念：
- 工厂是外部使用场景的主要入口
- 业务代码通过工厂统一调用，不直接依赖具体场景实现
- 支持场景的动态扩展和替换

@author: shaohua.sun
@date: 2025/6/20
"""

from typing import Optional, Dict, Any, Generator
from service.intention.base import IntentionSceneEnum
from .base import scene_registry, SceneContext
from .base.constants import SceneErrors, SceneMessages, SceneErrorMessages
from utils.biz_logger import get_logger

logger = get_logger(__name__)


class SceneFactory:
    """
    场景工厂类 - 统一的场景调用入口

    核心职责：
    1. **统一入口**：为所有业务流程提供统一的场景调用接口
    2. **动态路由**：根据场景类型自动路由到对应的场景处理器
    3. **生命周期管理**：管理场景的注册、实例化、缓存等生命周期
    4. **优雅解耦**：业务代码只需要知道工厂，不需要直接依赖具体场景

    使用方式：
    ```python
    # 推荐：通过工厂统一调用
    result = await scene_factory.handle_scene(scene_type, context)

    # 不推荐：直接使用场景类
    # scene = NutritionAnalysisScene()
    # result = await scene.handle(context)
    ```
    """

    def __init__(self, auto_register: bool = True):
        """
        初始化场景工厂

        Args:
            auto_register: 是否自动注册所有场景，默认True
        """
        self._initialized = False
        if auto_register:
            self._auto_register_scenes()
    
    def _auto_register_scenes(self):
        """
        自动注册所有场景实现

        通过导入场景实现模块触发 @register_scene 装饰器
        这是工厂确保所有场景可用的核心机制
        """
        if self._initialized:
            logger.debug("场景已经注册过，跳过重复注册")
            return

        try:
            # 导入所有场景实现以触发注册装饰器
            from .implementations import (
                DefaultChatScene,
                NutritionAnalysisScene,
                ExerciseTrackingScene,
                HydrationTrackingScene,
                SleepTrackingScene,
                HealthAnalyticsScene,
                AppointmentManagementScene,
                HealthAdvisorScene
            )

            self._initialized = True

            # 验证注册结果
            from .base import scene_registry
            registered_count = len(scene_registry.get_all_registered_scenes())
            logger.info(f"场景工厂初始化完成，已注册 {registered_count} 个场景")

        except ImportError as e:
            logger.error(f"场景注册失败: {str(e)}")
            self._initialized = False
    
    async def handle_scene(self, scene_type: IntentionSceneEnum, 
                          context: SceneContext, 
                          agent_config: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        处理场景请求
        
        Args:
            scene_type: 场景类型
            context: 场景上下文
            agent_config: Agent配置参数
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        logger.info(SceneMessages.SCENE_PROCESSING_START.format(scene_type.value))
        
        try:
            # 获取场景处理器（直接传入配置）
            scene = self.get_scene(scene_type, agent_config)
            if not scene:
                # 降级到默认场景
                logger.warning(f"场景 {scene_type.value} 不可用，降级到默认场景")
                scene = self.get_scene(IntentionSceneEnum.OTHER_SCENE, agent_config)
                if not scene:
                    raise Exception("默认场景不可用")
            

            result = await scene.handle_stream(context)
            
            logger.info(SceneMessages.SCENE_PROCESSING_SUCCESS.format(scene_type.value))
            return result
            
        except Exception as e:
            error_msg = SceneMessages.SCENE_PROCESSING_FAILED.format(scene_type.value, str(e))
            logger.error(error_msg)
            
            # 返回错误结果
            return {
                "success": False,
                "ai_response": SceneErrorMessages.GENERAL_ERROR,
                "data": None,
                "data_type": "text",
                "error": str(e)
            }
    
    async def handle_scene_stream(self, scene_type: IntentionSceneEnum, 
                                 context: SceneContext, 
                                 agent_config: Dict[str, Any] = None) -> Generator[Dict[str, Any], None, None]:
        """
        流式处理场景请求
        
        Args:
            scene_type: 场景类型
            context: 场景上下文
            agent_config: Agent配置参数
            
        Yields:
            Dict[str, Any]: 流式处理结果
        """
        logger.info(f"开始流式处理场景: {scene_type.value}")
        
        try:
            # 获取场景处理器（直接传入配置）
            scene = self.get_scene(scene_type, agent_config)
            if not scene:
                # 降级到默认场景
                logger.warning(f"场景 {scene_type.value} 不可用，降级到默认场景")
                scene = self.get_scene(IntentionSceneEnum.OTHER_SCENE, agent_config)
                if not scene:
                    yield {
                        "success": False,
                        "error": "默认场景不可用",
                        "chunk_type": "error"
                    }
                    return
            
            # 执行流式场景处理
            async for chunk in scene.handle_stream(context):
                yield chunk
            
            logger.info(f"流式处理完成: {scene_type.value}")
            
        except Exception as e:
            error_msg = f"流式处理失败: {scene_type.value} - {str(e)}"
            logger.error(error_msg)
            
            yield {
                "success": False,
                "error": str(e),
                "chunk_type": "error"
            }
    
    def get_scene(self, scene_type: IntentionSceneEnum, agent_config: Dict[str, Any] = None):
        """
        获取场景处理器实例
        
        Args:
            scene_type: 场景类型
            agent_config: Agent配置参数（可选）
            
        Returns:
            Optional[BaseScene]: 场景处理器实例
        """
        return scene_registry.get_scene(scene_type, agent_config)
    
    def is_scene_available(self, scene_type: IntentionSceneEnum) -> bool:
        """
        检查场景是否可用
        
        Args:
            scene_type: 场景类型
            
        Returns:
            bool: 是否可用
        """
        from .base import scene_registry
        return scene_registry.is_registered(scene_type)
    
    def get_available_scenes(self) -> list:
        """
        获取所有可用的场景
        
        Returns:
            list: 可用场景列表
        """
        from .base import scene_registry
        return scene_registry.get_all_registered_scenes()
    
    def ensure_initialized(self):
        """
        确保工厂已初始化（延迟初始化支持）

        如果工厂创建时没有自动注册，可以调用此方法手动初始化
        """
        if not self._initialized:
            self._auto_register_scenes()

    def get_factory_status(self) -> Dict[str, Any]:
        """
        获取工厂状态信息

        Returns:
            Dict[str, Any]: 状态信息
        """
        from .base import scene_registry
        registry_status = scene_registry.get_registry_status()
        return {
            "factory_initialized": self._initialized,
            "registry_status": registry_status,
            "available_scenes": [scene.value for scene in self.get_available_scenes()],
            "langgraph_integration": True,
            "react_agent_support": True
        }
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取工厂状态信息（别名方法，兼容测试）

        Returns:
            Dict[str, Any]: 状态信息
        """
        status = self.get_factory_status()
        # 适配测试期望的格式
        return {
            "total_scenes": len(status.get("available_scenes", [])),
            "langgraph_integration": status.get("langgraph_integration", True),
            "react_agent_support": status.get("react_agent_support", True),
            "factory_initialized": status.get("factory_initialized", self._initialized),
            "available_scenes": status.get("available_scenes", [])
        }


# 全局场景工厂实例
scene_factory = SceneFactory()