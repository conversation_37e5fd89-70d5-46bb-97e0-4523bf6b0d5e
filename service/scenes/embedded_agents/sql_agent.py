# sql_agent.py - SQL生成专用代理
#
# @author: shao<PERSON>.sun
# @date: 2025-01-27
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD

"""
SQL生成专用代理

专门用于在工具内部生成SQL查询语句的轻量级AI代理。
设计为无状态，可以安全地嵌套在工具中使用。

特性：
1. 专注于SQL生成任务
2. 支持医疗数据表结构理解
3. 轻量级设计，无状态管理
4. 可以嵌套在工具中使用
5. 英文提示词，适配医疗数据分析场景
6. 智能限制查询结果数量
7. 自动限制就诊日期查询范围
"""

from typing import Dict, Any, Optional
import json
from datetime import datetime, timedelta, timezone
from ai.router import Router
from ai.config.ai_config import RouterConfig, FactoryConfig, ModelConfig
from ai.config.ai_enum import AIPlatformEnum
from ai.config.ai_properties import AIProperties
from utils.biz_logger import get_logger

logger = get_logger(__name__)


class SQLGenerationAgent:
    """SQL生成专用代理"""
    
    # 基础系统提示词（英文版本）
    BASE_SYSTEM_PROMPT = """You are a Medical Data Intelligence Analysis AI model specializing in patient-centric medical data queries. Your role is to analyze and extract meaningful insights from a specific patient's medical records.

**CONTEXT**: You are working with medical data for a specific patient (BN NUMBER: BN40371431). All queries should focus on this individual's medical history, diagnoses, treatments, and visit records to provide personalized insights.

Your responsibilities:

1. **Patient-Centric Analysis**: Analyze user input to identify which aspects of this patient's medical data (tables and fields) are relevant to their query, and explain the reasoning in simple Chinese.

2. **Personalized SQL Generation**: Generate SQL statements that extract information specifically for this patient, ensuring all queries are focused on their individual medical journey.

**QUERY DESIGN PRINCIPLES**:
- Focus on the individual patient's data (BN NUMBER: BN40371431)
- Ensure all queries include patient identification to maintain data relevance
- In most cases, only single-table query statements are allowed, and the only table that can be joined is olap_visit
- Consider the patient's complete medical timeline when analyzing patterns

**IMPORTANT GUIDELINES**:
- Only use field names explicitly listed in the table structures below
- Strictly prohibit using fields that do not exist in the tables
- Each table's fields are independent; you cannot use fields across different tables
- Before generating SQL, carefully verify that field names exist in the corresponding table
- **QUERY LIMITS**: Always add LIMIT 5 to your SELECT queries unless the query is a simple COUNT, SUM, AVG, MAX, MIN aggregation
- **DATE CONSTRAINTS**: For visit_date queries, if user doesn't specify a date range, default to last 6 months; if user specifies a date, ensure the query range doesn't exceed 6 months from the specified date
- **PATIENT FOCUS**: All queries should include patient identification (bn_number = 'BN40371431') to ensure we're analyzing the correct patient's data

Your output must be in strict JSON format, example:

{
    "related_list":[
        "May be related to olap_diagnosis table's disease name and diagnosis type, reason: xxxxx",
        "May be related to olap_drug table's drug name, medical institution, prescription type, medication instructions and trade name, reason: xxxxx"
    ],
    "sql_list":[
         "SELECT d.disease_name, d.diagnosis_date FROM olap_diagnosis d WHERE d.bn_number = 'xxxxxx' ORDER BY d.diagnosis_date DESC LIMIT 5",
         "SELECT v.visit_date, v.visit_dept_name, v.chief_complain FROM olap_visit v WHERE v.bn_number = 'xxxxxxx' AND v.visit_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH) ORDER BY v.visit_date DESC LIMIT 5"
    ]
}

**PATIENT INFORMATION**:
BN NUMBER: BN40371431

SQL Version: 8.0.11-TiDB-v8.5.1 (Very important! Syntax must be compatible)

Available user medical data table structures:

Diagnosis Table (olap_diagnosis):
id text  'Data primary key'
patient_id text  'EMPI ID'
visit_id text  'Visit ID'
bn_number text  'Patient ID'
disease_name text  'Disease name'
diagnosis_norm text  'Standardized diagnosis name'
disease_code text  'Disease code (ICD-10)'
diagnosis_date text  'Diagnosis time'
main_diagnosis_flag int  'Whether it is main diagnosis (boolean)'
diagnosis_type_name text  'Diagnosis type (outpatient)'
diagnosis_department_name text  'Diagnosis department name'
diagnosis_doctor text  'Diagnosis doctor (encrypted information)'
diagnosis_comments text  'Diagnosis comments'

Medication Table (olap_drug):
id text  'Data primary key'
patient_id text  'EMPI ID'
visit_id text  'Visit ID'
bn_number text  'Patient ID'
common_name text  'Common drug name'
trade_name text  'Trade name'
dosage_per_use text  'Dosage per use'
unit_of_dosage_per_use text  'Unit of dosage per use'
total_dosage text  'Total drug dosage'
performed_frequency_desc text  'Medication frequency description'
administration_method text  'Administration method'
prescribed_time text  'Drug prescription time'
prescriber_name text  'Prescriber ID or name'
prescribed_department_name text  'Prescribing department name'
patient_instruction text  'Medication instructions'

Visit Table (olap_visit):
id text  'Data primary key'
patient_id text  'EMPI ID'
visit_id text  'Visit ID'
bn_number text  'Patient ID'
age text  'Age'
visit_type text  'Visit type'
visit_dept_name text  'Visit department name'
visit_date text  'Visit date'
visit_doctor_name text  'Visit doctor name'
admission_date text  'Admission date'
discharge_date text  'Discharge date'
chief_complain text  'Chief complaint'
present_illness_history text  'Present illness history'
past_medical_history text  'Past medical history'
marital_history text  'Marital history'

Follow strict JSON output format and ensure all SQL statements use only existing table fields while maintaining focus on the specific patient's data."""

    def __init__(self):
        """初始化SQL生成代理"""
        self.patient_id = "000afef05bac4365f0b16fe66aae1766"  # 固定患者ID
        
    def generate_sql(self, user_query: str) -> Dict[str, Any]:
        """
        根据用户查询生成SQL语句
        
        Args:
            user_query: 用户查询内容
            
        Returns:
            Dict[str, Any]: 包含相关分析和SQL语句的字典
        """
        try:
            logger.info(f"SQL代理开始处理用户查询: {user_query}")
            
            # 创建AI模型实例
            ai_instance = self._create_ai_instance()
            
            # 构建完整的系统提示词（包含系统信息）
            enhanced_system_prompt = self._build_enhanced_system_prompt()
            
            # 构建完整的用户提示词
            full_prompt = f"User query: {user_query}"
            
            # 调用AI生成SQL
            response = ai_instance.invoke([
                {"role": "system", "content": enhanced_system_prompt},
                {"role": "user", "content": full_prompt}
            ])
            
            # 解析响应
            if hasattr(response, 'content'):
                content = response.content
            else:
                content = str(response)
            
            logger.info(f"AI响应原始内容: {content}")
            
            # 尝试解析JSON
            try:
                result = json.loads(content)
                logger.info(f"SQL生成成功: {result}")
                return result
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败: {e}, 原始内容: {content}")
                # 尝试从内容中提取JSON
                result = self._extract_json_from_content(content)
                if result:
                    return result
                else:
                    return {
                        "related_list": [f"AI响应解析失败，原因: {str(e)}"],
                        "sql_list": []
                    }
                    
        except Exception as e:
            logger.error(f"SQL生成失败: {e}", exc_info=True)
            return {
                "related_list": [f"SQL生成服务暂时不可用，错误: {str(e)}"],
                "sql_list": []
            }
    
    def _build_enhanced_system_prompt(self) -> str:
        """
        构建增强的系统提示词（包含系统信息）
        
        Returns:
            str: 增强的系统提示词
        """
        # 获取当前时间信息（东八区）
        china_tz = timezone(timedelta(hours=8))
        now = datetime.now(china_tz)
        
        # 构建系统时间信息
        system_time_info = f"""**SYSTEM TIME INFORMATION:**
Current Date and Time: {now.strftime("%Y-%m-%d %H:%M:%S")}
Current Date: {now.strftime("%Y-%m-%d")}
Current Time: {now.strftime("%H:%M:%S")}
Day of Week: {now.strftime("%A").upper()}
Current Year: {now.strftime("%Y")}

**CRITICAL TIME CONSTRAINTS:**
1. Today's date is {now.strftime("%Y-%m-%d")} - use this as the absolute reference point
2. When user mentions "today", it refers to {now.strftime("%Y-%m-%d")}
3. Current time is {now.strftime("%H:%M:%S")} on {now.strftime("%A").upper()}
4. For any date-related queries, calculate based on {now.strftime("%Y-%m-%d")}
5. NEVER assume dates - always calculate relative to {now.strftime("%Y-%m-%d")}
6. ALL date calculations MUST be based on {now.strftime("%Y-%m-%d")} as the reference point

**QUERY LIMITATIONS:**
1. Always add LIMIT 5 to SELECT queries unless it's a simple aggregation (COUNT, SUM, AVG, MAX, MIN)
2. For visit_date queries, default to last 6 months if user doesn't specify a date range
3. If user specifies a date for visit_date, ensure the query range doesn't exceed 6 months from the specified date
4. Use WHERE visit_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH) for date constraints

"""

        # 拼接基础提示词
        enhanced_prompt = system_time_info + self.BASE_SYSTEM_PROMPT
        
        return enhanced_prompt
    
    def _create_ai_instance(self):
        """创建AI模型实例"""
        try:
            # 使用Router创建AI实例
            router_config = RouterConfig(task_type='text')
            ai_router = Router(router_config)
            
            factory_config = FactoryConfig(
                platform=AIPlatformEnum.OPENAI  # 使用OpenAI平台
            )
            
            model_config = ModelConfig(
                model_name='gpt-4o-mini',  # 使用高效的模型
                temperature=0.1,  # 低温度，保证输出稳定性
                max_tokens=2000,  # 足够的token数
                streaming=False,  # 不使用流式输出
                timeout=30
            )
            
            return ai_router.handle(factory_config=factory_config, model_config=model_config)
            
        except Exception as e:
            logger.error(f"创建AI实例失败: {e}")
            raise
    
    def _extract_json_from_content(self, content: str) -> Optional[Dict[str, Any]]:
        """从内容中提取JSON"""
        try:
            # 尝试查找JSON块
            start_idx = content.find('{')
            end_idx = content.rfind('}')
            
            if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                json_str = content[start_idx:end_idx + 1]
                return json.loads(json_str)
                
        except Exception as e:
            logger.error(f"提取JSON失败: {e}")
            
        return None