"""
Embedded agents utility functions

提供嵌入式代理的通用工具函数，包括：
- 系统时间信息处理
- 通用提示词组件复用

@author: shaohua.sun
@date: 2025/8/4
"""

from typing import Dict
from datetime import datetime, timedelta, timezone


class EmbeddedAgentUtils:
    """嵌入式代理工具类"""
    
    @staticmethod
    def get_current_time_info() -> Dict[str, str]:
        """
        获取当前时间信息（东八区时区）
        
        复用CommonComponents的时间处理逻辑，确保与其他场景保持一致
        
        Returns:
            Dict[str, str]: 时间信息字典
        """
        # 强制使用东八区时间（UTC+8）
        china_tz = timezone(timedelta(hours=8))
        now = datetime.now(china_tz)
        yesterday = now - timedelta(days=1)
        tomorrow = now + timedelta(days=1)
        
        return {
            "currentDateTime": now.strftime("%Y-%m-%d %H:%M:%S"),
            "currentDate": now.strftime("%Y-%m-%d"),
            "currentTime": now.strftime("%H:%M:%S"),
            "currentDayOfWeek": now.strftime("%A").upper(),
            "currentYear": now.strftime("%Y"),
            "yesterdayDate": yesterday.strftime("%Y-%m-%d"),
            "tomorrowDate": tomorrow.strftime("%Y-%m-%d"),
            "currentTimeUTC": now.strftime("%b %d %H:%M:%S %Y")
        }
    
    @staticmethod
    def build_system_time_info_for_sql() -> str:
        """
        构建SQL代理专用的系统时间信息提示词
        
        基于CommonComponents.build_system_time_info()的逻辑，
        但针对SQL查询场景进行了优化
        
        Returns:
            str: SQL代理专用的时间信息提示词
        """
        time_info = EmbeddedAgentUtils.get_current_time_info()
        
        return f"""**SYSTEM TIME INFORMATION:**
Current Date and Time: {time_info['currentDateTime']}
Current Date: {time_info['currentDate']}
Current Time: {time_info['currentTime']}
Day of Week: {time_info['currentDayOfWeek']}
Current Year: {time_info['currentYear']}

**CRITICAL TIME CONSTRAINTS FOR SQL QUERIES:**
1. Today's date is {time_info['currentDate']} - use this as the absolute reference point
2. When user mentions "today", it refers to {time_info['currentDate']}
3. When user mentions "yesterday", it refers to {time_info['yesterdayDate']}
4. When user mentions "tomorrow", it refers to {time_info['tomorrowDate']}
5. Current time is {time_info['currentTime']} on {time_info['currentDayOfWeek']}
6. For any date-related queries, calculate based on {time_info['currentDate']}
7. NEVER assume dates - always calculate relative to {time_info['currentDate']}
8. ALL date calculations MUST be based on {time_info['currentDate']} as the reference point
9. If unsure about dates, ASK for clarification rather than guessing

**SQL QUERY LIMITATIONS:**
1. Always add LIMIT 5 to SELECT queries unless it's a simple aggregation (COUNT, SUM, AVG, MAX, MIN)
2. For visit_date queries, default to last 6 months if user doesn't specify a date range
3. If user specifies a date for visit_date, ensure the query range doesn't exceed 6 months from the specified date
4. Use WHERE visit_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH) for date constraints
5. Remember: CURDATE() returns {time_info['currentDate']} in the database context

"""
    
    @staticmethod
    def build_enhanced_system_prompt_for_sql(base_prompt: str) -> str:
        """
        为SQL代理构建增强的系统提示词
        
        Args:
            base_prompt: 基础提示词
            
        Returns:
            str: 增强的系统提示词
        """
        # 获取系统时间信息
        system_time_info = EmbeddedAgentUtils.build_system_time_info_for_sql()
        
        # 拼接基础提示词
        enhanced_prompt = system_time_info + base_prompt
        
        return enhanced_prompt
