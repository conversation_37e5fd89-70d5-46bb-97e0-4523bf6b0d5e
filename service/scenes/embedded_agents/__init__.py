# embedded_agents package - 场景内嵌套的AI代理模块
#
# @author: s<PERSON><PERSON>.sun
# @date: 2025-01-27
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD

"""
场景内嵌套的AI代理模块

提供可以在工具内部嵌套使用的专用AI代理，实现更复杂的业务逻辑。
与主场景的ReactAgent不同，这些代理专注于特定的子任务处理。

设计原则：
1. 轻量级：避免过重的状态管理
2. 专用性：针对特定任务优化
3. 可组合：可以嵌套在工具中使用
4. 无状态：保持无状态设计，避免冲突

命名说明：
- embedded_agents: 表示这些代理是嵌入在工具内部使用的
- 与ai/agent目录区分：ai/agent是主场景的ReactAgent，这里是工具内嵌套的专用代理
"""

from .sql_agent import SQLGenerationAgent

__all__ = [
    'SQLGenerationAgent'
]