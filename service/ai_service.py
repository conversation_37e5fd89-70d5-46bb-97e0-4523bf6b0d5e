# ai_service
#
# @author: 崔晓波
# @email: <EMAIL>
# @date: 2025/6/17 11:31
# @license: Copyright © 2012 - 2025 YiDuCloud.EVYD
import json
from typing import Generator, Any, Optional, Dict, AsyncGenerator

from ai.agent.react_agent import ReactAgent
from ai.config.ai_enum import AIPlatformEnum
from ai.factory import Factory
from ai.manager.ai_manager import AIManager
from ai.router import Router
from ai.tools.weather import Weather, get_weather
from ai.config.ai_config import ModelConfig, FactoryConfig, RouterConfig
from ai.config.ai_properties import AIProperties
from workflow.workflow_manager import WorkflowManager
from workflow.workflow_types import WorkflowType
from workflow.base_workflow import WorkflowContext
from utils.biz_logger import get_logger
from utils.random_util import build_random_uuid, build_random
from service.ocr import OcrService, OcrRequest, OcrResponse

logger = get_logger(__name__)


class AIService:
    llm: Any
    workflow_manager: WorkflowManager

    def __init__(self, factory: FactoryConfig, model: ModelConfig):
        """
        Init AI Service
        :param factory:
        :param model:
        """
        self.ai_factory = Factory(factory_config=factory)
        self.llm = self.ai_factory.build(model_config=model)
        
        # 初始化工作流管理器
        self.workflow_manager = WorkflowManager()
        
        # 初始化OCR服务
        self.ocr_service = OcrService()
        
        logger.info("AIService初始化完成，包含工作流管理器和OCR服务")

    async def _ensure_workflow_manager_initialized(self):
        """确保工作流管理器已初始化"""
        if not self.workflow_manager._initialized:
            await self.workflow_manager.initialize()

    def chat(self, message: str) -> str:
        logger.info(f"chat called: {message}")
        try:
            result = self.llm.invoke(message).content
            logger.info(f"chat response: {result}")
            return result
        except Exception as e:
            logger.error(f"chat exception: {e}", exc_info=True)
            raise

    def stream_chat(self, message: str) -> Generator[str, None, None]:
        logger.info(f"stream_chat called: {message}")
        try:
            yield "[TEXT_START]"
            for chunk in self.llm.stream(message):
                logger.debug(f"stream_chat chunk: {chunk.content}")
                yield chunk.content
            yield "[TEXT_END]"
        except Exception as e:
            logger.error(f"stream_chat exception: {e}", exc_info=True)
            raise

    def handle_agent(self, message: str) -> dict:
        logger.info(f"agent_chat called: {message}")
        try:
            # Initialize the agent
            tool_weather = Weather()

            ai_router = Router(RouterConfig())
            factory_config = FactoryConfig()
            factory_config.platform = AIPlatformEnum.OPENAI
            ai_instance = ai_router.handle(factory_config=factory_config, model_config=ModelConfig())
            ai_agent = ReactAgent(
                model=ai_instance,
                tools=[get_weather],
                system_prompt="You are a weather specialist."
            )
            result: dict = {}
            # Update the prompt
            ai_agent.update_prompt("You are a helpful assistant. Your name is bob")
            # Run a agent
            # response = ai_agent.agent.invoke({"messages": [{"role": "user", "content": "what is the weather in Beijing"}]})
            response = ai_agent.run(message)
            logger.info(f"agent_chat response: {response}")
            result["weather"] = response["structured_response"]

            # # Dynamically add a new tool
            # def get_time(timezone: str) -> str:
            #     """Simulate time lookup."""
            #     return f"Current time in {timezone}: 12:00 PM"
            #
            # ai_agent.add_tool(get_time)
            # time_response = ai_agent.run("what is the time in Beijing")
            # print(f"Time response: %s", time_response)

            return result
        except Exception as e:
            logger.error(f"agent_chat exception: {e}", exc_info=True)
            raise

    def agent_chat_stream(self, message: str) -> Generator[str, None, None]:
        logger.info(f"agent_chat called: {message}")
        try:
            # Initialize the agent
            tool_weather = Weather()

            ai_router = Router(RouterConfig())
            factory_config = FactoryConfig()
            factory_config.platform = AIPlatformEnum.OPENAI
            ai_instance = ai_router.handle(factory_config=factory_config, model_config=ModelConfig())
            ai_agent = ReactAgent(
                model=ai_instance,
                tools=[get_weather],
                system_prompt="You are a weather specialist."
            )
            result: dict = {}
            # Update the prompt
            ai_agent.update_prompt("You are a helpful assistant. Your name is bob")
            # Run a agent
            try:
                for chunk in ai_agent.run_stream_new(message):
                    for node, update in chunk.items():
                        # 取出你关心的内容
                        text = ""
                        if isinstance(update, dict) and "messages" in update:
                            messages = update["messages"]
                            if isinstance(messages, list) and messages:
                                msg = messages[-1]
                                text = getattr(msg, "content", str(msg))
                        elif isinstance(update, dict) and "result" in update:
                            text = update["result"]
                        else:
                            text = str(update)
                        yield f"data: {json.dumps({'node': node, 'text': text}, ensure_ascii=False)}\n\n"

            except Exception as e:
                logger.error(f"agent_chat_stream exception: {e}", exc_info=True)
                raise

        except Exception as e:
            logger.error(f"agent_chat_stream exception: {e}", exc_info=True)
            raise

    def agent_chat_stream_short(self, message: str) -> Generator[str, None, None]:
        logger.info(f"agent_chat_stream_short called: {message}")
        try:
            tool_weather = Weather()

            ai_router = Router(RouterConfig())
            factory_config = FactoryConfig()
            factory_config.platform = AIPlatformEnum.OPENAI
            ai_instance = ai_router.handle(factory_config=factory_config, model_config=ModelConfig())
            ai_agent = ReactAgent(
                model=ai_instance,
                tools=[get_weather],
                system_prompt="You are a weather specialist.",
                store_type="postgres"
            )
            ai_agent.update_prompt("You are a helpful assistant.")
            try:
                for chunk in ai_agent.run_stream_short(message):
                    for node, update in chunk.items():
                        text = ""
                        if isinstance(update, dict) and "messages" in update:
                            messages = update["messages"]
                            if isinstance(messages, list) and messages:
                                msg = messages[-1]
                                text = getattr(msg, "content", str(msg))
                        elif isinstance(update, dict) and "result" in update:
                            text = update["result"]
                        else:
                            text = str(update)
                        yield f"data: {json.dumps({'node': node, 'text': text}, ensure_ascii=False)}\n\n"
            except Exception as e:
                logger.error(f"agent_chat_stream_short exception: {e}", exc_info=True)
                raise
        except Exception as e:
            logger.error(f"agent_chat_stream_short exception: {e}", exc_info=True)
            raise

    def agent_completion(self, biz_unique_id: Optional[str], biz_user_id: Optional[str], message: str, stream: bool = False):
        """
        通用的AI接口，支持流式和非流式
        :param biz_unique_id: 业务唯一ID
        :param biz_user_id: 业务用户ID
        :param message: 用户消息
        :param stream: 是否流式
        :return: 响应结果
        """
        biz_unique_id = biz_unique_id or build_random("bun_")
        biz_user_id = biz_user_id or build_random("bu_")
        logger.info(f"completion_called: biz_unique_id={biz_unique_id}, biz_user_id={biz_user_id}, stream={stream}, message={message}")

        result = AIManager().handle_agent(biz_unique_id, biz_user_id, message)
        return result

    def agent_completion_stream(self, biz_unique_id: Optional[str], biz_user_id: Optional[str], message: str, stream: bool = False):
        """
        通用的AI接口，支持流式和非流式
        :param biz_unique_id: 业务唯一ID
        :param biz_user_id: 业务用户ID
        :param message: 用户消息
        :param stream: 是否流式
        :return: 响应结果
        """
        biz_unique_id = biz_unique_id or build_random("bun_")
        biz_user_id = biz_user_id or build_random("bu_")
        logger.info(f"completion_called: biz_unique_id={biz_unique_id}, biz_user_id={biz_user_id}, stream={stream}, message={message}")
        for chunk in AIManager().handle_agent_stream(biz_unique_id, biz_user_id, message):
            yield chunk

    def agent_completion_stream_test(self):
        ai_agent = ReactAgent(
            model=self.llm,
            tools=[get_weather],
            system_prompt="You are a weather specialist.",
            store_type="postgres",
            thread_id="101",
            biz_user_id="1"
        )
        # for chunk in ai_agent.run_stream_new("what is the weather in Beijing?" + "03"):
        for chunk in ai_agent.run_stream_short("what is the weather in Beijing?" + "03"):
            # chunk["messages"][-1].pretty_print()
            yield chunk

    async def workflow_completion(
        self, 
        biz_unique_id: Optional[str], 
        biz_user_id: Optional[str], 
        message: str, 
        workflow_type: str = "default",
        custom_data: Optional[str] = None,
        stream: bool = False
    ) -> Any:
        """
        基于工作流的AI完成接口（同步版本）
        
        Args:
            biz_unique_id: 业务唯一ID
            biz_user_id: 业务用户ID  
            message: 用户消息
            workflow_type: 工作流类型标识
            custom_data: 自定义数据(JSON字符串)
            stream: 是否流式输出
            
        Returns:
            工作流处理结果
        """
        # 确保工作流管理器已初始化
        await self._ensure_workflow_manager_initialized()
        
        # 生成默认ID
        biz_unique_id = biz_unique_id or build_random("bun_")
        biz_user_id = biz_user_id or build_random("bu_")
        
        logger.info(f"工作流completion调用: workflow_type={workflow_type}, biz_unique_id={biz_unique_id}, biz_user_id={biz_user_id}, message={message[:50]}...")
        
        try:
            # 解析自定义数据
            custom_data_dict = {}
            if custom_data:
                try:
                    custom_data_dict = json.loads(custom_data)
                except json.JSONDecodeError:
                    logger.warning(f"自定义数据解析失败: {custom_data}")
            
            # 构建工作流上下文
            context = {
                "thread_id": biz_unique_id,  # 使用biz_unique_id作为thread_id
                "user_info": {
                    "biz_user_id": biz_user_id,
                    "user_id": biz_user_id,  # 兼容字段
                    "name": custom_data_dict.get("user_name", "用户"),
                    **custom_data_dict  # 合并自定义数据
                },
                "session_data": custom_data_dict,
                "metadata": {
                    "workflow_type": workflow_type,
                    "biz_unique_id": biz_unique_id,
                    "biz_user_id": biz_user_id
                }
            }
            
            # 根据工作流类型选择处理方式
            if workflow_type == "default" or workflow_type is None:
                # 默认工作流：使用原有的AIManager处理
                logger.info("使用默认工作流处理")
                result = AIManager().handle_agent(biz_unique_id, biz_user_id, message)
                return result
            else:
                # 使用工作流管理器处理
                logger.info(f"使用工作流管理器处理: {workflow_type}")
                result = await self.workflow_manager.execute_workflow(
                    workflow_id=workflow_type,
                    user_input=message,
                    context=context
                )
                
                # 转换为统一的响应格式
                return {
                    "success": result.success,
                    "response": result.response,
                    "data": result.data,
                    "metadata": result.metadata,
                    "workflow_type": workflow_type,
                    "execution_time": result.execution_time
                }
                
        except Exception as e:
            logger.error(f"工作流completion处理失败: {e}", exc_info=True)
            return {
                "success": False,
                "response": "抱歉，处理您的请求时出现了问题，请稍后重试。",
                "error": str(e),
                "workflow_type": workflow_type
            }

    async def workflow_completion_stream(
        self, 
        biz_unique_id: Optional[str], 
        biz_user_id: Optional[str], 
        message: str, 
        workflow_type: str = "default",
        custom_data: Optional[str] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        基于工作流的AI完成接口（流式版本）
        
        Args:
            biz_unique_id: 业务唯一ID
            biz_user_id: 业务用户ID  
            message: 用户消息
            workflow_type: 工作流类型标识
            custom_data: 自定义数据(JSON字符串)
            
        Yields:
            流式处理结果
        """
        # 确保工作流管理器已初始化
        await self._ensure_workflow_manager_initialized()
        
        # 生成默认ID
        biz_unique_id = biz_unique_id or build_random("bun_")
        biz_user_id = biz_user_id or build_random("bu_")
        
        logger.info(f"工作流stream调用: workflow_type={workflow_type}, biz_unique_id={biz_unique_id}, biz_user_id={biz_user_id}, message={message[:50]}...")
        
        try:
            # 解析自定义数据
            custom_data_dict = {}
            if custom_data:
                try:
                    custom_data_dict = json.loads(custom_data)
                except json.JSONDecodeError:
                    logger.warning(f"自定义数据解析失败: {custom_data}")
            
            # 构建工作流上下文
            context = {
                "thread_id": biz_unique_id,  # 使用biz_unique_id作为thread_id
                "user_info": {
                    "biz_user_id": biz_user_id,
                    "user_id": biz_user_id,  # 兼容字段
                    "name": custom_data_dict.get("user_name", "用户"),
                    **custom_data_dict  # 合并自定义数据
                },
                "session_data": custom_data_dict,
                "metadata": {
                    "workflow_type": workflow_type,
                    "biz_unique_id": biz_unique_id,
                    "biz_user_id": biz_user_id
                }
            }
            
            # 统一使用工作流管理器处理所有请求
            logger.info(f"使用工作流管理器处理: {workflow_type}")
            
            # 确保工作流管理器已初始化
            await self._ensure_workflow_manager_initialized()
            
            # 解析工作流类型，处理默认值和映射
            resolved_workflow_type = WorkflowType.resolve_workflow_type(workflow_type)
            logger.info(f"解析后的工作流类型: {resolved_workflow_type}")
            
            # 通过工作流管理器获取工作流实例
            workflow_instance = self.workflow_manager.registry.get_workflow(resolved_workflow_type)
            if not workflow_instance:
                yield {
                    "chunk_type": "error",
                    "error": f"工作流不可用: {resolved_workflow_type}",
                    "workflow_type": resolved_workflow_type
                }
                return
            
            # 直接调用工作流的流式处理
            async for chunk in workflow_instance.process_stream(message, context):
                yield {
                    **chunk,
                    "workflow_type": resolved_workflow_type
                }
                    
        except Exception as e:
            logger.error(f"工作流stream处理失败: {e}", exc_info=True)
            yield {
                "chunk_type": "error",
                "error": str(e),
                "workflow_type": workflow_type
            }

    def ocr_process_image(self, file_id: str = None, image_url: str = None, biz_user_id: str = None) -> Dict[str, Any]:
        """
        OCR图片处理接口
        
        Args:
            file_id: 文件ID（可选）
            image_url: 图片URL（可选）
            biz_user_id: 业务用户ID
            
        Returns:
            Dict[str, Any]: OCR处理结果
        """
        try:
            # 生成默认用户ID
            biz_user_id = biz_user_id or build_random("ocr_user_")
            
            logger.info(f"OCR处理请求: file_id={file_id}, image_url={image_url}, biz_user_id={biz_user_id}")
            
            # 构建OCR请求
            ocr_request = OcrRequest(
                file_id=file_id,
                image_url=image_url,
                biz_user_id=biz_user_id
            )
            
            # 调用OCR服务处理
            ocr_response = self.ocr_service.process_image(ocr_request)
            
            # 转换为统一的响应格式
            result = {
                "success": ocr_response.success,
                "message": ocr_response.message,
                "data": {
                    "text": ocr_response.text,
                    "processing_time": ocr_response.processing_time,
                    "file_id": file_id,
                    "image_url": image_url
                }
            }
            
            if not ocr_response.success:
                result["error"] = ocr_response.error_code
                
            logger.info(f"OCR处理完成: success={ocr_response.success}, text_length={len(ocr_response.text or '')}")
            return result
            
        except Exception as e:
            logger.error(f"OCR处理异常: {e}", exc_info=True)
            return {
                "success": False,
                "message": "OCR处理失败",
                "error": str(e),
                "data": None
            }
