import os
import requests
from utils.biz_logger import get_logger

logger = get_logger(__name__)

def get_family_member_ids(user_id: str):
    """获取家庭成员ID列表"""
    chatbot_base_url = os.getenv('CHATBOT_BASE_URL', 'http://evyd-chatbot-chatbot-app-web')
    timeout = int(os.getenv('CHATBOT_TIMEOUT', '30'))
    
    url = f"{chatbot_base_url}/internal/user/member-list"
    
    logger.info(f"调用家庭成员列表接口 - 用户: {user_id}")
    
    try:
        # 构建请求参数
        params = {
            'userId': user_id
        }
        
        # 发送请求 - requests会自动使用环境变量中的代理配置
        response = requests.get(
            url,
            params=params,
            timeout=timeout,
            headers={
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        )
        
        response.raise_for_status()
        
        # 解析响应
        result = response.json()
        
        # 记录响应日志
        logger.info(f"家庭成员列表接口响应: {result}")
        
        # 返回成员ID列表，期望格式为 ["1","2"]
        if isinstance(result, list):
            member_ids = result
        else:
            # 如果响应格式不是直接的列表，尝试从data字段获取
            member_ids = result.get('data', [])
        
        logger.info(f"获取用户 {user_id} 的家庭成员列表: {member_ids}")
        return member_ids
        
    except Exception as e:
        logger.error(f"获取家庭成员列表失败: {e}")
        # 发生错误时返回包含用户成员ID的列表作为fallback
        fallback_result = [user_member_id] if user_member_id else []
        logger.info(f"使用fallback结果: {fallback_result}")
        return fallback_result