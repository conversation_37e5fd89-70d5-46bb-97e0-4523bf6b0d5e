import os
import requests
from utils.biz_logger import get_logger
from .member_service import get_family_member_ids

logger = get_logger(__name__)

class OPAClient:
    def __init__(self):
        self.base_url = os.getenv('OPA_BASE_URL', 'http://0.0.0.0:8181')
        self.timeout = int(os.getenv('OPA_TIMEOUT', '30'))
        logger.info(f"OPA客户端初始化 - 服务地址: {self.base_url}, 超时: {self.timeout}s")
    
    def check_access(self, user_id: str,target_member_id: str):
        """权限检查接口"""
        logger.info(f"开始权限检查 - 用户: {user_id}, 目标成员: {target_member_id}")
        
        try:
            # 获取family_member_ids
            family_member_ids = get_family_member_ids(user_id)
            
            # 构建请求
            request_data = {
                "user": {
                    "id": user_id,
                    "family_member_ids": family_member_ids
                },
                "member": {
                    "id": target_member_id
                }
            }
            
            logger.info(f"发送OPA请求: {request_data}")
            
            # 发送请求 - requests会自动使用环境变量中的代理配置
            url = f"{self.base_url}/api/v1/check_access"
            
            logger.info(f"发送请求到: {url}")
            logger.info(f"请求头: Content-Type: application/json")
            
            response = requests.post(
                url, 
                json=request_data, 
                timeout=self.timeout,
                headers={
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            )
            
            logger.info(f"响应状态码: {response.status_code}")
            logger.info(f"响应头: {dict(response.headers)}")
            
            if response.status_code != 200:
                logger.error(f"OPA服务返回错误状态码: {response.status_code}")
                logger.error(f"响应内容: {response.text}")
            
            response.raise_for_status()
            
            # 解析响应
            result = response.json()
            
            # 记录完整响应日志
            logger.info(f"OPA响应详情: {result}")
            
            # 返回关键字段
            return {
                "allowed": result.get("allowed", False),
                "redact_fields": result.get("redact_fields", [])
            }
            
        except Exception as e:
            logger.error(f"权限检查失败: {e}")
            return {
                "allowed": False,
                "redact_fields": [],
                "error": str(e)
            }