"""
Default intention recognizer based on ReactAgent

Complete intention recognition implementation following document requirements:
- LLM-based intention recognition with complete English prompts
- No hardcoded keyword filtering, fully controlled by LLM + prompts
- Enum value inclusion checking for robust scene determination
- Follows Java version prompt template exactly

@author: shaohua.sun
@date: 2025/7/2
"""

from typing import Dict, Any, Optional, Generator

from ai.config.ai_enum import AIModelEnum
from .base import BaseIntentionRecognizer
from .base.scene_enums import IntentionSceneEnum
from service.templates.intention_templates import IntentionTemplateManager
from ai.agent.react_agent import ReactAgent
from ai.router import Router
from ai.config.ai_config import RouterConfig, FactoryConfig, ModelConfig
from utils.biz_logger import get_logger

logger = get_logger(__name__)


class DefaultIntentionRecognizer(BaseIntentionRecognizer):
    """
    基于ReactAgent的默认意图识别器
    
    核心特性：
    - 使用ReactAgent的自动记忆功能管理对话历史
    - 支持智能的摘要和修剪策略
    - 支持流式处理
    - 意图识别通常无需工具调用，保持简洁设计
    """
    
    def __init__(
        self,
        agent_config: Dict[str, Any] = None,
        **kwargs
    ):
        """
        初始化意图识别器
        
        Args:
            agent_config: Agent配置参数（可选），支持完整配置
            **kwargs: 其他配置参数（为了兼容性保留）
        """
        super().__init__(**kwargs)
        
        # 使用传入的agent_config或默认配置
        self.agent_config = agent_config or self._get_default_agent_config()
        
        # 从agent_config中提取常用配置
        self.store_type: str = self.agent_config.get('store_type', 'postgres')
        self.history_strategy: str = self.agent_config.get('history_strategy', 'trim')
        
        # ReactAgent缓存（基于thread_id + user_id）- 已注释掉以移除缓存
        # self._agent_cache: Dict[str, 'ReactAgent'] = {}
        self._current_request_agent: Optional['ReactAgent'] = None             # 当前请求的ReactAgent实例（单次请求内复用）
        
        logger.info(f"DefaultIntentionRecognizer初始化完成 - 存储: {self.store_type}, 策略: {self.history_strategy}")
    
    def _get_or_create_react_agent(self, context: Dict[str, Any]) -> 'ReactAgent':
        """
        获取或创建ReactAgent实例（单次请求内复用，跨请求不缓存）

        设计说明：
        - 单次请求内（recognize或recognize_stream调用期间）复用同一个ReactAgent实例
        - 跨请求不缓存，每次新请求都会创建新的ReactAgent
        - 避免同一请求中多次创建ReactAgent导致的问题

        Args:
            context: 上下文信息（包含thread_id、user_id等）

        Returns:
            ReactAgent: 当前请求的ReactAgent实例
        """
        # 如果当前请求已有Agent实例，直接返回
        if self._current_request_agent is not None:
            thread_id = self._get_thread_id(context)
            user_id = context.get("user_id", "anonymous")
            logger.debug(f"复用当前请求的意图识别Agent实例 - thread_id: {thread_id}, user_id: {user_id}")
            return self._current_request_agent

        # 获取基本信息
        thread_id = self._get_thread_id(context)
        user_id = context.get("user_id", "anonymous")

        # 创建新的ReactAgent实例
        logger.info(f"创建新的意图识别Agent - thread_id: {thread_id}, user_id: {user_id}")
        react_agent = self._build_agent(thread_id, user_id)

        # 存储到当前请求实例
        self._current_request_agent = react_agent

        logger.info(f"意图识别Agent创建成功 - thread_id: {thread_id}, user_id: {user_id}")
        return react_agent
    
    def _get_default_agent_config(self) -> Dict[str, Any]:
        """
        获取默认Agent配置
        
        专门为意图识别优化的默认配置：
        - 低temperature确保稳定的枚举输出
        - 较小的token限制提高响应速度
        - 使用postgres存储确保记忆持久化
        
        Returns:
            Dict[str, Any]: 默认配置字典
        """
        return {
            'platform': 'openai',
            'model_name': 'gpt-4o-mini',
            'temperature': 0.0,  # 意图识别使用低temperature确保枚举输出
            'max_tokens': 150,   # 意图识别通常只需要简短响应
            'streaming': False,  # 意图识别通常不需要流式输出
            'timeout': 30,
            'store_type': 'postgres',  # 使用postgres存储
            'history_strategy': 'trim',  # 使用trim策略避免摘要丢失信息
            'overwrite_history': False,
            'mem_max_tokens': 5000,      # 意图识别的记忆需求相对较小
            'mem_max_summary_tokens': 3000
        }
    def _build_agent(self, thread_id: str, user_id: str) -> 'ReactAgent':
        """
        构建ReactAgent实例

        Args:
            thread_id: 线程ID，用于会话管理
            user_id: 用户ID，用于store namespace

        Returns:
            ReactAgent: 新创建的ReactAgent实例
        """
        try:
            # 使用Router统一处理模型实例创建（与BaseScene保持一致）
            router_config = RouterConfig(task_type='text')
            factory_config = FactoryConfig(
                platform=self.agent_config.get('platform', 'openai')
            )
            model_config = ModelConfig(
                model_name=self.agent_config.get('model_name', AIModelEnum.OPEN_AI_GPT_4_1),
                temperature=self.agent_config.get('temperature', 0.0),
                max_tokens=self.agent_config.get('max_tokens', 150),
                streaming=self.agent_config.get('streaming', False),
                timeout=self.agent_config.get('timeout', 30)
            )

            router = Router(router_config)
            llm = router.handle(factory_config, model_config)

            # 意图识别通常不需要工具，保持空列表
            tools = []

            # 构建系统提示词
            system_prompt = self._build_system_prompt()

            logger.info(f"意图识别系统提示词: {system_prompt}")

            # 创建ReactAgent（传入thread_id和biz_user_id）
            react_agent = ReactAgent(
                model=llm,
                tools=tools,
                system_prompt=system_prompt,
                store_type=self.agent_config.get('store_type', 'postgres'),
                history_strategy=self.agent_config.get('history_strategy', 'trim'),
                overwrite_history=self.agent_config.get('overwrite_history', False),
                max_tokens=self.agent_config.get('mem_max_tokens', 5000),
                max_summary_tokens=self.agent_config.get('mem_max_summary_tokens', 3000),
                thread_id=thread_id,
                biz_user_id=user_id
            )

            logger.info(f"意图识别ReactAgent构建成功 - thread_id: {thread_id}, user_id: {user_id}")
            return react_agent

        except Exception as e:
            logger.error(f"构建意图识别ReactAgent失败: {str(e)}")
            raise RuntimeError(f"Agent构建失败: {str(e)}")
    
    
    def _build_system_prompt(self) -> str:
        """构建完整的系统提示词，使用意图识别专用模板管理器"""
        # 使用意图识别专用模板管理器中的提示词
        return IntentionTemplateManager.build_complete_intention_prompt()
    
    def _get_thread_id(self, context: Dict[str, Any]) -> str:
        """获取线程ID用于ReactAgent记忆管理"""
        if "thread_id" in context:
            return str(context["thread_id"])
        if "user_id" in context:
            return f"intention_{context['user_id']}"
        return "default_intention_thread"
    
    def recognize(self, user_input: str, context: Optional[Dict[str, Any]] = None) -> str:
        """
        执行意图识别
        
        Args:
            user_input: User input
            context: Context information (including thread_id, user_id, etc.)

        Returns:
            str: Recognized intention scene
        """
        logger.info(f"Starting intention recognition: {user_input}")

        try:
            # 清空当前请求的Agent实例，确保每次请求都创建新的Agent
            self._current_request_agent = None
            
            context = context or {}

            # 获取或创建ReactAgent（基于上下文）
            react_agent = self._get_or_create_react_agent(context)

            # Build complete user input with prompt template
            full_input = f"{user_input}"

            # Call ReactAgent（使用run方法，thread_id已在创建时设置）
            result = react_agent.run(full_input)

            # Extract response content
            response_content = ""
            if result and "messages" in result:
                last_message = result["messages"][-1]
                response_content = getattr(last_message, 'content', str(last_message))

            # Extract scene using enum inclusion checking
            scene = self.extract_scene_from_response(response_content)

            logger.info(f"Intention recognition completed - Input: {user_input[:50]}..., Result: {scene}")
            return scene

        except Exception as e:
            logger.error(f"Intention recognition failed: {str(e)}")
            return IntentionSceneEnum.OTHER_SCENE.value
        finally:
            # 请求处理完成后清空当前请求的Agent实例
            self._current_request_agent = None
    
    def recognize_stream(
        self, 
        user_input: str, 
        context: Optional[Dict[str, Any]] = None
    ) -> Generator[Dict[str, Any], None, None]:
        """
        流式意图识别
        
        Args:
            user_input: 用户输入
            context: 上下文信息
            
        Yields:
            Dict[str, Any]: 流式处理结果
        """
        try:
            # 清空当前请求的Agent实例，确保每次请求都创建新的Agent
            self._current_request_agent = None
            
            context = context or {}

            # 获取或创建ReactAgent（基于上下文）
            react_agent = self._get_or_create_react_agent(context)

            # 直接使用用户输入，系统提示词已在ReactAgent中设置
            formatted_input = user_input

            # 流式处理（使用run_stream方法，thread_id已在创建时设置）
            final_content = ""
            for chunk in react_agent.run_stream(formatted_input):
                if chunk:
                    # ReactAgent.run_stream返回AIMessageChunk
                    chunk_content = str(chunk)
                    final_content += chunk_content

                    # 生成流式数据
                    yield {
                        "chunk": chunk_content,
                        "partial_content": final_content,
                        "final": False
                    }
            
            # 最终结果
            if final_content:
                scene = self.extract_scene_from_response(final_content)
                yield {
                    "scene": scene,
                    "final_content": final_content,
                    "final": True
                }
            
        except Exception as e:
            logger.error(f"流式意图识别失败: {str(e)}")
            yield {
                "node": "error",
                "error": str(e),
                "scene": IntentionSceneEnum.OTHER_SCENE.value
            }
        finally:
            # 请求处理完成后清空当前请求的Agent实例
            self._current_request_agent = None

    def get_agent_info(self) -> Dict[str, Any]:
        """获取Agent状态信息（已移除缓存机制）"""
        # 解析缓存键信息（已移除缓存机制）
        # cache_details = []
        # for cache_key in self._agent_cache.keys():
        #     if '#' in cache_key:
        #         thread_id, user_id = cache_key.split('#', 1)
        #         cache_details.append({"thread_id": thread_id, "user_id": user_id, "cache_key": cache_key})
        #     else:
        #         cache_details.append({"cache_key": cache_key})

        return {
            "recognizer_type": "DefaultIntentionRecognizer",
            "agent_config": {
                "store_type": self.agent_config.get('store_type'),
                "history_strategy": self.agent_config.get('history_strategy'),
                "model_name": self.agent_config.get('model_name'),
                "temperature": self.agent_config.get('temperature'),
                "max_tokens": self.agent_config.get('max_tokens')
            },
            "cached_agents_count": 0,  # 缓存机制已移除
            "cache_details": [],  # 缓存机制已移除
            "cache_strategy": "no_cache"  # 已移除缓存
        }

    def clear_agent_cache(self, thread_id: str = None, user_id: str = None):
        """
        清理Agent缓存（已移除缓存机制，此方法现在为空操作）

        Args:
            thread_id: 指定要清理的thread_id，为None时清理所有缓存
            user_id: 指定要清理的user_id，为None时清理所有缓存
        """
        # 缓存机制已移除，此方法现在为空操作
        logger.info("清理Agent缓存方法被调用，但缓存机制已移除，无需操作")
        # if thread_id and user_id:
        #     # 清理指定的thread_id + user_id组合
        #     cache_key = f"{thread_id}#{user_id}"
        #     if cache_key in self._agent_cache:
        #         del self._agent_cache[cache_key]
        #         logger.info(f"已清理指定的意图识别缓存: {cache_key}")
        #     else:
        #         logger.warning(f"指定的意图识别缓存键不存在: {cache_key}")
        # elif thread_id:
        #     # 清理所有包含指定thread_id的缓存
        #     keys_to_remove = [key for key in self._agent_cache.keys() if key.startswith(f"{thread_id}#")]
        #     for key in keys_to_remove:
        #         del self._agent_cache[key]
        #     logger.info(f"已清理thread_id相关的意图识别缓存: {thread_id}，共清理 {len(keys_to_remove)} 个实例")
        # elif user_id:
        #     # 清理所有包含指定user_id的缓存
        #     keys_to_remove = [key for key in self._agent_cache.keys() if key.endswith(f"#{user_id}")]
        #     for key in keys_to_remove:
        #         del self._agent_cache[key]
        #     logger.info(f"已清理user_id相关的意图识别缓存: {user_id}，共清理 {len(keys_to_remove)} 个实例")
        # else:
        #     # 清理所有缓存
        #     cache_count = len(self._agent_cache)
        #     self._agent_cache.clear()
        #     logger.info(f"已清理所有意图识别Agent缓存，共清理 {cache_count} 个实例")
    
    def extract_scene_from_response(self, response: str) -> str:
        """Extract scene from ReactAgent response using enum inclusion checking"""
        try:
            if not response:
                return IntentionSceneEnum.OTHER_SCENE.value

            # Clean response text
            response = response.strip().upper()

            # Use enum value inclusion checking to handle extra characters from LLM
            for intention in IntentionSceneEnum:
                if intention.value in response:
                    logger.debug(f"Matched intention: {intention.value} in response: {response}")
                    return intention.value

            # If no match found, return default scene
            logger.warning(f"No intention matched for response: {response}, using OTHER_SCENE")
            return IntentionSceneEnum.OTHER_SCENE.value

        except Exception as e:
            logger.warning(f"Scene extraction failed: {str(e)}")
            return IntentionSceneEnum.OTHER_SCENE.value
    

    
    