"""
意图识别模块

提供用户意图识别功能：
- 抽象基类：BaseIntentionRecognizer
- 默认实现：DefaultIntentionRecognizer  
- 场景枚举：IntentionSceneEnum
- 相关常量和配置

结构：
- base/: 抽象基础组件
- default_recognizer.py: 默认实现
- usage_example.py: 使用示例

@author: shoahua.sun
@date: 2025/6/20
"""

# 导出抽象基类和基础组件
from .base import (
    BaseIntentionRecognizer,
    IntentionSceneEnum,
    IntentionMessages,
    IntentionRemotePromptKeys
)
from config.intention_config import IntentionConfig

# 导出默认实现
from .default_recognizer import DefaultIntentionRecognizer

__all__ = [
    # 抽象基类
    'BaseIntentionRecognizer',
    
    # 默认实现
    'DefaultIntentionRecognizer',
    
    # 场景枚举
    'IntentionSceneEnum',
    
    # 常量和配置
    'IntentionMessages', 
    'IntentionConfig',
    'IntentionRemotePromptKeys'
] 