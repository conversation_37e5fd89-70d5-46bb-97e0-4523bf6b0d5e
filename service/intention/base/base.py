"""
意图识别抽象基类

基于ReactAgent设计理念的简化意图识别框架：
- 专注于ReactAgent的组装和配置
- 去除复杂的手动历史管理逻辑
- 简化抽象方法设计
- 支持灵活的ReactAgent配置

@author: shaohua.sun
@date: 2025/6/30
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union, Generator
from utils.biz_logger import get_logger
from .constants import IntentionMessages
from .scene_enums import IntentionSceneEnum


class BaseIntentionRecognizer(ABC):
    """
    意图识别抽象基类
    
    基于ReactAgent设计理念的简化框架：
    1. 专注于ReactAgent的组装和配置管理
    2. 依赖ReactAgent的自动记忆功能
    3. 简化抽象方法，只保留核心业务逻辑
    4. 支持同步和异步两种调用方式
    """
    
    def __init__(self, **kwargs):
        """
        初始化意图识别器
        
        Args:
            **kwargs: 配置参数，由子类具体处理
        """
        self.logger = get_logger(self.__class__.__name__)
        self.logger.info(IntentionMessages.RECOGNIZER_INITIALIZED.format(self.__class__.__name__))
    
    @abstractmethod
    def recognize(self, user_input: str, context: Optional[Dict[str, Any]] = None) -> str:
        """
        核心意图识别方法（抽象方法）
        
        子类必须实现此方法，使用ReactAgent进行意图识别
        
        Args:
            user_input: 用户输入文本
            context: 上下文信息（包含thread_id, user_id等）
            
        Returns:
            str: 识别出的场景标识
        """
        pass
    
    @abstractmethod
    def recognize_stream(
        self, 
        user_input: str, 
        context: Optional[Dict[str, Any]] = None
    ) -> Generator[Dict[str, Any], None, None]:
        """
        流式意图识别方法（抽象方法）
        
        子类必须实现此方法，使用ReactAgent进行流式意图识别
        
        Args:
            user_input: 用户输入文本
            context: 上下文信息
            
        Yields:
            Dict[str, Any]: 流式处理的结果
        """
        pass
    
    @abstractmethod
    def extract_scene_from_response(self, response: str) -> str:
        """
        从ReactAgent响应中提取场景标识（抽象方法）
        
        Args:
            response: ReactAgent响应内容
            
        Returns:
            str: 场景标识
        """
        pass
    
    def get_agent_info(self) -> Dict[str, Any]:
        """
        获取Agent信息（可选重写）
        
        Returns:
            Dict[str, Any]: Agent信息
        """
        return {
            "agent_type": "BaseIntentionRecognizer",
            "class_name": self.__class__.__name__
        }
    
    def validate_input(self, user_input: str) -> bool:
        """
        验证用户输入
        
        Args:
            user_input: 用户输入
            
        Returns:
            bool: 是否有效
        """
        if not user_input or not user_input.strip():
            self.logger.warning(IntentionMessages.INPUT_EMPTY_WARNING)
            return False
        
        # 简化验证逻辑，去除复杂的长度检查
        if len(user_input.strip()) > 10000:  # 基本的长度限制
            self.logger.warning("用户输入过长")
            return False
        
        return True 