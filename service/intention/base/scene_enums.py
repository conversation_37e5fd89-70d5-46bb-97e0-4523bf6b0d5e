"""
意图识别场景标识枚举

统一管理意图识别系统的所有场景标识：
- 提供类型安全的场景标识
- 便于代码提示和维护
- 支持场景标识的统一管理

@author: shoahua.sun
@date: 2025/6/20
"""

from enum import Enum


class IntentionSceneEnum(str, Enum):
    """意图识别场景标识枚举"""
    
    # 营养分析场景
    NUTRITION_ANALYSIS = "NUTRITION_ANALYSIS"
    
    # 健康顾问场景
    HEALTH_ADVISOR = "HEALTH_ADVISOR"
    
    # 运动追踪场景
    EXERCISE_TRACKING = "EXERCISE_TRACKING"
    
    # 水分追踪场景
    HYDRATION_TRACKING = "HYDRATION_TRACKING"
    
    # 睡眠追踪场景
    SLEEP_TRACKING = "SLEEP_TRACKING"
    
    # 健康分析场景
    HEALTH_ANALYTICS = "HEALTH_ANALYTICS"
    
    # 预约管理场景
    APPOINTMENT_MANAGEMENT = "APPOINTMENT_MANAGEMENT"

    # 默认场景（其他场景）
    OTHER_SCENE = "OTHER_SCENE"
    
    @classmethod
    def get_all_scenes(cls) -> list:
        """获取所有场景标识列表"""
        return [scene.value for scene in cls]
    
    @classmethod
    def is_valid_scene(cls, scene: str) -> bool:
        """验证场景标识是否有效"""
        return scene in cls.get_all_scenes()
    
    @classmethod
    def get_default_scene(cls) -> str:
        """获取默认场景"""
        return cls.OTHER_SCENE.value
    
    def get_description(self) -> str:
        """获取场景描述"""
        descriptions = {
            self.NUTRITION_ANALYSIS: "营养分析相关场景",
            self.HEALTH_ADVISOR: "健康顾问相关场景",
            self.EXERCISE_TRACKING: "运动追踪相关场景",
            self.HYDRATION_TRACKING: "水分追踪相关场景",
            self.SLEEP_TRACKING: "睡眠追踪相关场景",
            self.HEALTH_ANALYTICS: "健康分析相关场景",
            self.APPOINTMENT_MANAGEMENT: "预约管理相关场景",

            self.OTHER_SCENE: "其他场景"
        }
        return descriptions.get(self, "未知场景") 