"""
Intention recognition module constants definition

Unified management of intention recognition related string constants to improve code readability and maintainability

@author: shaohua.sun
@date: 2025/7/2
"""

from enum import Enum


class IntentionMessages:
    """意图识别相关消息常量"""
    
    # 日志消息
    RECOGNIZER_INITIALIZED = "意图识别器初始化完成: {}"
    
    # 验证消息
    INPUT_EMPTY_WARNING = "用户输入为空"


class IntentionRemotePromptKeys:
    """意图识别远程提示词键值常量"""
    
    # 意图识别专用远程提示词键
    INTENTION_RECOGNITION_SYSTEM_PROMPTS = "intention_recognition_system_prompts" 