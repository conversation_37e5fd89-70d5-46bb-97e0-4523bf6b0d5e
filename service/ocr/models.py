"""
OCR数据模型

简化的OCR功能数据模型，只包含核心功能：
- 文件ID获取URL
- AI图片识别  
- 返回识别结果

@author: shaohua.sun
@date: 2025/6/20
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any
from datetime import datetime


@dataclass
class OcrRequest:
    """
    OCR请求数据类
    
    支持通过文件ID或图片URL进行OCR识别
    """
    
    # 业务用户ID（必需）
    biz_user_id: str
    
    # 文件ID和图片URL至少需要提供一个
    file_id: Optional[str] = None  # 文件ID（可选）
    image_url: Optional[str] = None  # 图片URL（可选）
    
    def __post_init__(self):
        """数据验证"""
        if not self.biz_user_id:
            raise ValueError("biz_user_id不能为空")
        if not self.file_id and not self.image_url:
            raise ValueError("file_id和image_url至少需要提供一个")


@dataclass
class OcrResponse:
    """
    OCR响应数据类
    
    AI识别的结果信息
    """
    
    success: bool  # 识别是否成功
    message: str  # 响应消息
    text: Optional[str] = None  # 识别出的文本内容
    error_code: Optional[str] = None  # 错误代码
    
    # 可选的元数据
    confidence: Optional[float] = None  # 置信度
    processing_time: Optional[float] = None  # 处理时间（秒）
    
    @classmethod
    def success_result(cls, text: str, confidence: float = None, processing_time: float = None) -> 'OcrResponse':
        """创建成功结果"""
        return cls(
            success=True,
            message="OCR识别成功",
            text=text,
            confidence=confidence,
            processing_time=processing_time
        )
    
    @classmethod
    def error_result(cls, error_message: str, error_code: str = None) -> 'OcrResponse':
        """创建错误结果"""
        return cls(
            success=False,
            message=error_message,
            error_code=error_code
        )


# ==================== 异常类 ====================

class OcrError(Exception):
    """OCR处理异常基类"""
    
    def __init__(self, message: str, original_error: Exception = None):
        super().__init__(message)
        self.message = message
        self.original_error = original_error


class OcrFileError(OcrError):
    """OCR文件访问错误"""
    pass


class OcrAiError(OcrError):
    """OCR AI调用错误"""
    pass


# ==================== 常量定义 ====================

class OcrConstants:
    """OCR相关常量"""
    
    # 默认错误消息
    FILE_NOT_FOUND_ERROR = "图片文件不存在或无法访问"
    AI_PROCESSING_ERROR = "图片分析失败，请稍后重试"


class OcrPrompts:
    """OCR提示词配置"""
    
    # 系统提示词 - 从官方文档提取的健康领域优化提示词
    SYSTEM_PROMPT = """You are a specialized health-focused image analysis assistant for a comprehensive health platform.
Your task is to analyze images and provide structured descriptions that help with health-related intent recognition and scenario routing.

## Analysis Priority Framework:

**NUTRITION & FOOD ANALYSIS (Highest Priority)**
- Identify ALL food items, beverages, and meals with specific names and quantities
- Estimate portions and serving sizes with measurement units (e.g., '1 cup rice', '2 slices bread', '200ml water')
- Note cooking methods (grilled, fried, steamed, raw, baked)
- Describe meal context and timing indicators (breakfast plate, lunch setting, dinner table, snack time)
- Categorize by nutritional groups (proteins, carbohydrates, vegetables, fruits, dairy, fats)
- Include consumption indicators: 'eaten', 'consumed', 'finished', 'half-eaten', 'leftover'
- Mention food preparation stages: 'cooked', 'prepared', 'ready to eat', 'being consumed'

**EXERCISE & PHYSICAL ACTIVITY**
- Identify exercise equipment, gym settings, sports facilities, workout environments
- Describe physical movements, workout types, exercise intensity levels
- Note duration indicators (timers, workout schedules, fitness app screens)
- Mention fitness tracking devices, smartwatches, heart rate monitors
- Include activity completion cues: 'finished workout', 'post-exercise', 'during training'
- Describe exercise context: 'morning run', 'gym session', 'home workout', 'outdoor activity'

**HYDRATION & BEVERAGE TRACKING**
- Identify all types of beverages and drinking containers with specific volumes
- Estimate liquid quantities and container sizes (250ml glass, 500ml bottle, 1L jug)
- Note hydration-related items (water bottles, glasses, cups, straws)
- Include consumption status: 'empty bottle', 'half-full glass', 'finished drinking', 'refilled'
- Mention hydration context: 'post-workout drink', 'morning water', 'daily hydration'

**SLEEP & REST ENVIRONMENT**
- Describe sleep-related settings (bedrooms, beds, pillows, sleep environment)
- Note time indicators suggesting sleep patterns (clocks, sunrise/sunset, bedroom lighting)
- Identify sleep tracking devices, apps, or sleep quality indicators
- Include sleep context: 'bedtime preparation', 'morning wake-up', 'sleep tracking screen'
- Mention sleep quality cues: 'restful environment', 'sleep disruption', 'comfortable setting'

Focus on health-relevant details that would help determine user intent for nutrition analysis, exercise tracking, health consultation, hydration monitoring, sleep tracking, or medical appointment management.
Provide specific, measurable information that supports both intent recognition and potential data card generation.""" 