"""
OCR处理器

简化的OCR处理服务，核心功能：
1. 根据文件ID获取文件URL
2. 调用AI服务进行图片识别
3. 返回识别结果

@author: shaohua.sun
@date: 2025/6/20
"""

import time
from typing import Optional
from utils.biz_logger import get_logger
from .base import OcrServiceInterface
from .models import OcrRequest, OcrResponse, OcrError, OcrFileError, OcrAiError, OcrConstants, OcrPrompts
from integration.services.chatbot.service import ChatbotService
from ai.manager.ai_manager import AIManager
from ai.config.ai_enum import TaskTypeEnum

logger = get_logger(__name__)


class OcrService(OcrServiceInterface):
    """
    OCR处理服务
    
    实现核心的OCR处理功能
    """
    
    def __init__(self, chatbot_service=None, ai_manager=None):
        """
        初始化OCR服务
        
        Args:
            chatbot_service: ChatBot服务实例（可选，用于测试时注入模拟对象）
            ai_manager: AI管理器实例（可选，用于测试时注入模拟对象）
        """
        self.chatbot_service = chatbot_service
        self.ai_manager = ai_manager
        logger.info("OCR服务初始化完成")
    
    def _get_chatbot_service(self):
        """获取ChatBot服务实例（延迟初始化）"""
        if self.chatbot_service is None:
            self.chatbot_service = ChatbotService()
        return self.chatbot_service
    
    def _get_ai_manager(self):
        """获取AI管理器实例（延迟初始化）"""
        if self.ai_manager is None:
            self.ai_manager = AIManager()
        return self.ai_manager
    
    def get_file_url(self, file_id: str, biz_user_id: str) -> Optional[str]:
        """
        根据文件ID获取文件URL
        
        Args:
            file_id: 文件ID
            biz_user_id: 业务用户ID
            
        Returns:
            Optional[str]: 文件URL，获取失败返回None
            
        Raises:
            OcrFileError: 当文件访问出现严重错误时抛出
        """
        try:
            logger.info(f"获取文件URL: file_id={file_id}, user_id={biz_user_id}")
            
            # 获取ChatBot服务
            chatbot_service = self._get_chatbot_service()
            
            # 调用文件预览接口获取URL
            file_url = chatbot_service.get_file_url_by_id(file_id, biz_user_id)
            
            if file_url:
                logger.info(f"成功获取文件URL: {file_url}")
                return file_url
            else:
                logger.warning(f"无法获取文件URL: file_id={file_id}")
                return None
                
        except Exception as e:
            error_msg = f"获取文件URL失败: file_id={file_id}, 错误: {str(e)}"
            logger.error(error_msg)
            # 对于严重的系统错误，抛出异常；对于业务错误，返回None
            if "Connection" in str(e) or "Timeout" in str(e):
                return None  # 网络问题，返回None让上层处理
            else:
                raise OcrFileError(error_msg, e)
    
    def analyze_image_by_url(self, image_url: str) -> str:
        """
        通过URL分析图片
        
        Args:
            image_url: 图片URL
            
        Returns:
            str: 分析结果文本
            
        Raises:
            OcrAiError: AI分析失败时抛出
        """
        try:
            logger.info(f"开始分析图片: {image_url}")
            
            # 获取AI管理器
            ai_manager = self._get_ai_manager()
            
            # 使用系统提示词
            system_prompt = self._get_system_prompt()
            
            # 调用AI服务分析图片
            result = ai_manager.handle_by_router(
                message=system_prompt,
                task_type=TaskTypeEnum.IMAGE,
                platform="openai",
                image_url=image_url
            )
            
            if result:
                logger.info(f"图片分析成功")
                # AIManager.handle_by_router返回的是AIMessage对象，需要提取content
                if hasattr(result, 'content'):
                    return result.content
                else:
                    return str(result)
            else:
                raise OcrAiError("AI服务返回空结果")
                
        except Exception as e:
            logger.error(f"图片分析失败: {image_url}, 错误: {str(e)}")
            raise OcrAiError(f"图片分析失败: {str(e)}", e)
    
    def process_image(self, request: OcrRequest) -> OcrResponse:
        """
        处理图片识别请求
        
        Args:
            request: OCR请求对象
            
        Returns:
            OcrResponse: OCR响应结果
        """
        start_time = time.time()
        
        try:
            logger.info(f"开始处理OCR请求: file_id={request.file_id}")
            
            # 1. 获取图片URL
            if request.image_url:
                # 如果请求中已有URL，直接使用
                image_url = request.image_url
                logger.info(f"使用请求中的图片URL: {image_url}")
            else:
                # 根据文件ID获取URL
                image_url = self.get_file_url(request.file_id, request.biz_user_id)
                if not image_url:
                    return OcrResponse.error_result(OcrConstants.FILE_NOT_FOUND_ERROR)
            
            # 2. 调用AI分析图片
            try:
                analysis_result = self.analyze_image_by_url(image_url)
                processing_time = time.time() - start_time
                
                return OcrResponse.success_result(
                    text=analysis_result,
                    processing_time=processing_time
                )
                
            except OcrAiError as e:
                logger.error(f"AI分析失败: {str(e)}")
                return OcrResponse.error_result(OcrConstants.AI_PROCESSING_ERROR)
            
        except Exception as e:
            logger.error(f"OCR处理异常: {str(e)}", exc_info=True)
            return OcrResponse.error_result(f"处理异常: {str(e)}")
    
    def _get_system_prompt(self) -> str:
        """获取系统提示词"""
        return OcrPrompts.SYSTEM_PROMPT
