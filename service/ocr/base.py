"""
OCR基础架构

简化的OCR服务接口定义

@author: shaohua.sun
@date: 2025/6/20
"""

from abc import ABC, abstractmethod
from typing import Optional
from .models import OcrRequest, OcrResponse


class OcrServiceInterface(ABC):
    """
    OCR服务接口
    
    定义OCR服务的核心方法
    """
    
    @abstractmethod
    def process_image(self, request: OcrRequest) -> OcrResponse:
        """
        处理图片识别请求
        
        Args:
            request: OCR请求对象
            
        Returns:
            OcrResponse: OCR响应结果
        """
        pass
    
    @abstractmethod
    def get_file_url(self, file_id: str, biz_user_id: str) -> Optional[str]:
        """
        根据文件ID获取文件URL
        
        Args:
            file_id: 文件ID
            biz_user_id: 业务用户ID
            
        Returns:
            Optional[str]: 文件URL，获取失败返回None
        """
        pass 