"""
OCR模块

简化的OCR功能模块，提供：
- 文件ID到URL的转换
- AI图片识别服务
- 简单的错误处理

@author: shaohua.sun
@date: 2025/6/20
"""

from .models import OcrRequest, OcrResponse, OcrError, OcrFileError, OcrAiError, OcrConstants, OcrPrompts
from .processor import OcrService
from .base import OcrServiceInterface

__all__ = [
    # 数据模型
    'OcrRequest',
    'OcrResponse', 
    'OcrError',
    'OcrFileError',
    'OcrAiError',
    
    # 常量和配置
    'OcrConstants',
    'OcrPrompts',
    
    # 服务接口
    'OcrServiceInterface',
    'OcrService'
] 